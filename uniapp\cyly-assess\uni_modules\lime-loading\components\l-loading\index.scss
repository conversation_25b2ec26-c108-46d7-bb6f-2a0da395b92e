@import '@/uni_modules/lime-style/index.scss';

$loading-color: create-var(loading-color, $primary-color);
$loading-size: create-var(loading-size, 40rpx);
$loading-text-color: create-var(loading-text-color, $text-color-3);
$loading-font-size: create-var(loading-font-size, $font-size);

/* #ifndef MP-ALIPAY */
$loading-duration: create-var(loading-duration, 2s);
/* #endif */
/* #ifdef MP-ALIPAY */
$loading-duration: create-var(loading-duration, 1s);
/* #endif */

/* #ifndef APP-NVUE */



/* #ifndef MP-ALIPAY */
@property --l-loading-start {
	syntax: '<length-percentage>';
	initial-value: 1%;
	inherits: false;
}
@property --l-loading-end {
	syntax: '<length-percentage>';
	initial-value: 1%;
	inherits: false;
}
@property --l-left {
	syntax: '<length-percentage>';
	initial-value: 1%;
	inherits: false;
}
@property --l-loadding-ball-size {
	syntax: '<length> | <length-percentage>';
	// initial-value: 1%;
	inherits: false;
}
/* #endif */

:host {
	display: inline-flex;
}
/* #endif */


.l-loading {
	position: relative;
	// color: #c8c9cc;
	color: $loading-color;
	font-size: 0;
	vertical-align: middle;
	&--ball{
		display: inline-flex;
		align-items: center;
		.l-loading {
			&__ball {
				position: relative;
				perspective: calc(var(--l-loadding-ball-size) * 4);
				transform-style: preserve-3d;
				// border: 1px solid;
				
				&:before{
					background-color: $primary-color;
					left: 0%;
					// mix-blend-mode: darken;
					animation-name: l-ball-before;
				}
				&:after{
					right: 0;
					background-color: red;
					// mix-blend-mode: darken;
					animation-name: l-ball-after;
				}
				&:before,&:after{
					top: 0;
					content: '';
					position: absolute;
					// width: 100%;
					height: 100%;
					aspect-ratio: 1/1;
					border-radius: 50%;
					animation-iteration-count: infinite;
					animation-delay: -100ms;
					animation-duration: 900ms;
					mix-blend-mode: darken;
					animation-play-state: var(--l-play-state, running);
				}
			}
		}
	}
	&--circular {
		.l-loading {
			&__circular {
				display: inline-block;
				position: relative;
				/* #ifndef APP-NVUE */
				animation: l-rotate $loading-duration linear infinite;
				vertical-align: middle;
				animation-play-state: var(--l-play-state, running);
				&:before {
					content: '';
					display: block;
					width: 100%;
					height: 100%;
					border-radius: 50%;
					/* #ifndef MP-ALIPAY || APP-VUE */
					background-image: conic-gradient(
						transparent 0%, 
						transparent var(--l-loading-start, 0%), var(--l-loading-color-1, currentColor) var(--l-loading-start, 0%), 
						var(--l-loading-color-2, currentColor) var(--l-loading-end, 0%), transparent var(--l-loading-end, 0%), 
						transparent 100%);
					/* #endif */
					/* #ifdef MP-ALIPAY  || APP-VUE */
					background-image: conic-gradient(
						var(--l-loading-color-1, transparent) 0%, 
						var(--l-loading-color-2, currentColor) 100%);
					/* #endif */
					mask: radial-gradient(closest-side, transparent calc(80% - 1px), #fff 80%);
					-webkit-mask: radial-gradient(closest-side, transparent calc(80% - 1px), #fff 80%);
					animation: l-circular 2.5s ease-in-out infinite;
					transform: rotate(90deg);
					animation-play-state: var(--l-play-state, running);
				}
				/* #endif */
			}
		}
	}
	&--spinner {
		.l-loading {
			&__spinner {
				position: relative;
				box-sizing: border-box;
				width: 100%;
				height: 100%;
				max-width: 100%;
				max-height: 100%;
				animation-timing-function: steps(12);
				animation: l-rotate 0.8s linear infinite;
				animation-play-state: var(--l-play-state, running);
			}
			&__dot {
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				transform: rotate(calc(var(--l-loading-dot, 1) * 30deg));
				opacity: calc(var(--l-loading-dot, 1) / 12);
				&::before {
				  display: block;
				  width: 5rpx;
				  height: 25%;
				  margin: 0 auto;
				  background-color: currentColor;
				  border-radius: 40%;
				  content: ' ';
				}
			}
		}
		
	}
	&__text{
		display: inline-block;
		margin-left: $spacer-xs;
		color: $loading-text-color;
		font-size: $loading-font-size;
		vertical-align: middle;
	}
	&.is-vertical {
		display: inline-flex;    
		flex-direction: column;
		align-items: center;
		
		.l-loading__text {
			margin: $spacer-tn 0 0;
		}
	}
	&__ball,&__circular,&__spinner {
		width: $loading-size;
		height: $loading-size;
	}
}
/* #ifndef APP-NVUE */
@keyframes l-circular {
	0% {
		--l-loading-start: 0%;
		--l-loading-end: 0%;
	}
	50% {
		--l-loading-start: 0%;
		--l-loading-end: 100%;
	}
	100% {
		--l-loading-start: 100%;
		--l-loading-end: 100%;
	}
}
@keyframes l-rotate {
	to {
		transform: rotate(1turn)
	}
}

@keyframes l-ball-before {
	0%{
		animation-timing-function: ease-in;
	}
	25% {
		animation-timing-function: ease-out;
		--l-left: calc((var(--l-loadding-ball-size,100%) * 2.1 - var(--l-loadding-ball-size,100%)) / 2);
		transform: translate3d(var(--l-left), 0, var(--l-loadding-ball-size));
	}
	50% {
		--l-left: calc((var(--l-loadding-ball-size,100%) * 2.1 - var(--l-loadding-ball-size,100%)));
		animation-timing-function:ease-in;
		transform: translate3d(var(--l-left), 0, 0);
	}
	75% {
		animation-timing-function: ease-out;
		--l-left: calc((var(--l-loadding-ball-size,100%) * 2.1 - var(--l-loadding-ball-size,100%)) / 2);
		transform: translate3d(var(--l-left), 0, calc(var(--l-loadding-ball-size) * -1));
	}
}
@keyframes l-ball-after {
	0%{
		animation-timing-function: ease-in;
	}
	25% {
		animation-timing-function: ease-out;
		--l-left: calc((var(--l-loadding-ball-size,100%) * 2.1 - var(--l-loadding-ball-size,100%)) / 2 * -1);
		transform: translate3d(var(--l-left), 0, calc(var(--l-loadding-ball-size) * -1));
	}
	50% {
		animation-timing-function:ease-in;
		--l-left: calc((var(--l-loadding-ball-size,100%) * 2.1 - var(--l-loadding-ball-size,100%)) * -1);
		transform: translate3d(var(--l-left), 0, 0);
	}
	75% {
		animation-timing-function: ease-out;
		--l-left: calc((var(--l-loadding-ball-size,100%) * 2.1 - var(--l-loadding-ball-size,100%)) / 2 * -1);
		transform: translate3d(var(--l-left), 0, var(--l-loadding-ball-size));
	}
}
/* #endif */