@file:Suppress("UNCHECKED_CAST", "USELESS_CAST", "INAPPLICABLE_JVM_NAME", "UNUSED_ANONYMOUS_PARAMETER", "NAME_SHADOWING", "UNNECESSARY_NOT_NULL_ASSERTION")
package uni.UNI4AABA03
import io.dcloud.uniapp.*
import io.dcloud.uniapp.extapi.*
import io.dcloud.uniapp.framework.*
import io.dcloud.uniapp.runtime.*
import io.dcloud.uniapp.vue.*
import io.dcloud.uniapp.vue.shared.*
import io.dcloud.unicloud.*
import io.dcloud.uts.*
import io.dcloud.uts.Map
import io.dcloud.uts.Set
import io.dcloud.uts.UTSAndroid
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import uts.sdk.modules.limeSvg.NativeImage
import io.dcloud.uniapp.extapi.downloadFile as uni_downloadFile
open class GenUniModulesLimeSvgComponentsLSvgLSvg : VueComponent, LSvpProps {
    constructor(__ins: ComponentInternalInstance) : super(__ins) {}
    override var src: String by `$props`
    override var color: String by `$props`
    override var web: Boolean by `$props`
    override var inherit: Boolean by `$props`
    companion object {
        @Suppress("UNUSED_PARAMETER", "UNUSED_VARIABLE")
        var setup: (__props: GenUniModulesLimeSvgComponentsLSvgLSvg) -> Any? = fun(__props): Any? {
            val __ins = getCurrentInstance()!!
            val _ctx = __ins.proxy as GenUniModulesLimeSvgComponentsLSvgLSvg
            val _cache = __ins.renderCache
            var nativeImage: NativeImage? = null
            val props = __props
            fun emit(event: String, vararg do_not_transform_spread: Any?) {
                __ins.emit(event, *do_not_transform_spread)
            }
            val path = ref(props.src)
            val svgRef = ref<UniElement?>(null)
            val imageURL = ref("")
            val formatUrl = fun(url: String, action: String): String {
                if (url.indexOf("'") > 0) {
                    return "" + action + "(\"" + url + "\")"
                }
                return "" + action + "('" + url + "')"
            }
            val styles = computed(fun(): Map<String, String> {
                val style = Map<String, String>()
                if (props.color != "") {
                    style.set("color", props.color)
                }
                return style
            }
            )
            val errorDetaill = UniImageErrorEventDetail("加载失败")
            val errorEvent = UniImageErrorEvent("error", errorDetaill)
            val onError = fun(){
                emit("error", errorEvent)
            }
            val onLoad = fun(e: UniNativeViewEvent){
                val detail = ImageLoadEventDetail(512, 512)
                val loadEvent = UniImageLoadEvent("load", detail)
                emit("load", loadEvent)
            }
            val webRef = ref<UniWebViewElement?>(null)
            val setSvgSrc = fun(){
                if (path.value != "") {
                    webRef.value?.evalJS(formatUrl(path.value, "setSrc"))
                }
            }
            val setSvgColor = fun(){
                if (props.color != "" && path.value != "") {
                    webRef.value?.evalJS("setStyle({\"--color\": \"" + props.color + "\"})")
                }
            }
            val error = fun(_: UniWebViewErrorEvent){
                emit("error", errorEvent)
            }
            val loaded = fun(_: UniWebViewLoadEvent){
                watchEffect(fun(){
                    if (props.src == "" || !props.web) {
                        return
                    }
                    if (props.src.startsWith("<svg")) {
                        path.value = svgToDataUrl(props.src)
                        setSvgSrc()
                        setSvgColor()
                    } else if (props.src.startsWith("/static")) {
                        pathToDataUrl(props.src).then(fun(res){
                            path.value = res
                            setSvgSrc()
                            setSvgColor()
                        }).`catch`(fun(err){
                            emit("error", errorEvent)
                            console.warn("[lime-svg]" + props.src + JSON.stringify(err), " at uni_modules/lime-svg/components/l-svg/l-svg.uvue:167")
                        })
                    } else {
                        path.value = props.src
                        setSvgSrc()
                        setSvgColor()
                    }
                }
                )
            }
            val message = fun(event: UniWebViewMessageEvent){
                val data = UTSJSONObject.assign(UTSJSONObject(), event.detail.data[0] as UTSJSONObject)
                val type = data.getString("event")
                val detail = data.getJSON("data")?.getJSON("detail")
                if (type == "click") {
                    emit("click")
                } else if (type == "load") {
                    val width = detail?.getNumber("width") ?: 512
                    val height = detail?.getNumber("height") ?: 512
                    val loadDetail = ImageLoadEventDetail(width, height)
                    val loadEvent = UniImageLoadEvent("load", loadDetail)
                    emit(type, loadEvent)
                } else if (type == "error") {
                    emit(type, errorEvent)
                }
            }
            fun gen_onviewinit_fn(e: UniNativeViewInitEvent) {
                nativeImage = NativeImage(e.detail.element)
                nativeImage?.updateSrc(path.value)
                nativeImage?.updateColor(props.color)
            }
            val onviewinit = ::gen_onviewinit_fn
            val map = Map<String, String>()
            watchEffect(fun(){
                if (!props.web && props.src.startsWith("http")) {
                    if (map.has(props.src)) {
                        nativeImage?.updateSrc(map.get(props.src)!!)
                        return
                    }
                    uni_downloadFile(DownloadFileOptions(url = props.src, success = fun(res) {
                        map.set(props.src, res.tempFilePath)
                        nativeImage?.updateSrc(res.tempFilePath)
                    }))
                } else {
                    path.value = props.src
                    nativeImage?.updateSrc(props.src)
                }
            }
            )
            watchEffect(fun(){
                nativeImage?.updateColor(props.color)
            }
            )
            return fun(): Any? {
                return if (isTrue(_ctx.web)) {
                    createElementVNode("web-view", utsMapOf("key" to 0, "class" to "l-svg", "ref_key" to "webRef", "ref" to webRef, "onError" to error, "onLoad" to loaded, "onMessage" to message, "src" to "/uni_modules/lime-svg/hybrid/html/index.html?v=21"), null, 544)
                } else {
                    createElementVNode("native-view", mergeProps(utsMapOf("key" to 1, "class" to "l-svg"), _ctx.`$attrs`, utsMapOf("onInit" to onviewinit, "onError" to onError, "onLoad" to onLoad)), null, 16)
                }
            }
        }
        val styles: Map<String, Map<String, Map<String, Any>>> by lazy {
            normalizeCssStyles(utsArrayOf(
                styles0
            ))
        }
        val styles0: Map<String, Map<String, Map<String, Any>>>
            get() {
                return utsMapOf("l-svg" to padStyleMapOf(utsMapOf("width" to 24, "height" to 24)))
            }
        var inheritAttrs = true
        var inject: Map<String, Map<String, Any?>> = utsMapOf()
        var emits: Map<String, Any?> = utsMapOf("load" to null, "error" to null)
        var props = normalizePropsOptions(utsMapOf("src" to utsMapOf("type" to "String", "required" to true, "default" to ""), "color" to utsMapOf("type" to "String", "required" to true, "default" to ""), "web" to utsMapOf("type" to "Boolean", "required" to true, "default" to false), "inherit" to utsMapOf("type" to "Boolean", "required" to true, "default" to false)))
        var propsNeedCastKeys = utsArrayOf(
            "src",
            "color",
            "web",
            "inherit"
        )
        var components: Map<String, CreateVueComponent> = utsMapOf()
    }
}
