<template>
  <view class="form-item-wrapper" :style="getStyle">
    <!-- 输入框容器 -->
    <view class="input-container" :class="{ 'has-error': showError && !isValid }">
      <slot></slot>
    </view>
    <!-- 错误提示 - 在输入框下方显示，不影响布局 -->
    <view class="error-message-bottom" v-if="showError && !isValid">
      <!-- <view class="error-icon">!</view> -->
      <text class="error-text">{{ hintMessage }}</text>
    </view>
  </view>
</template>

<script lang="uts" setup>
    import { FormItemData,FormItemRule,FormItemVerifyResult } from "../types"
    import { useVerify,findParent } from "../utils"
    const isValid = ref(true)
    const parentLabelName = "u-form"
	  const hintMessage = ref('\u3000')
    const instance = getCurrentInstance()!;
    const props = defineProps({
        customStyle: {
          type: Object as PropType<UTSJSONObject>,
          default: {} as UTSJSONObject as UTSJSONObject
        },
        field: {
            type: String,
            default: ''
        },
        rule: {
          type: Object as PropType<FormItemRule>,
          default: (): FormItemRule =>{
            return {} as FormItemRule
          }
        },
        showError:{
          type:Boolean,
          default: false
        },
        marginTop:{
          type:Number,
          default:0
        }
    });

    const getStyle = computed(() => {
      console.log(props.marginTop,"props.marginTop");
      const mp:Map<string, string>  = new Map();
      console.log(props.marginTop,"props.marginTop");
      mp.set('margin-top', `${props.marginTop}rpx`);
      return mp;
    })
	function verify(value : any, callback : (res : FormItemVerifyResult) => void){
		return useVerify(isValid, hintMessage, props.field as string, props.rule as FormItemRule, value, callback)
	}
	function pushFormItemFieldToForm(){
			const that = instance.proxy!;
			//找到父组件form
			const parent = findParent(that, [parentLabelName]);
			console.log("==that==:",that.$parent?.$options.name)
			if (parent == null) {
				console.error('error:','u-form-item must be used inside u-form');
				return;
			}
			const item = {
				field: props.field as string,
				instance: that
			} as FormItemData;
			parent.$callMethod('pushFielditem', item)
	}
	onMounted(()=>{
		pushFormItemFieldToForm();
	})
	defineExpose({
		verify
	})
</script>

<style lang="scss" scoped>
/* 表单项包装器 */
.form-item-wrapper {
  margin-bottom: 24rpx;
  width: 100%;
}

/* 输入框容器 */
.input-container {
  width: 100%;
  border: 1px solid #EEEEEE;
  border-radius: 16rpx;
  transition: border-color 0.3s ease;
}

/* 错误提示样式 - 显示在输入框下方 */
.error-message-bottom {
  display: flex;
  align-items: flex-start;
  margin-top: 8rpx;
  padding: 12rpx 16rpx;
  background-color: #fff2f0;
  border-radius: 8rpx;
  border-left: 4rpx solid #ff4d4f;
  box-shadow: 0 2rpx 8rpx rgba(255, 77, 79, 0.1);

  /* 添加淡入动画 */
  animation: errorFadeIn 0.3s ease-out;
}

.has-error {
  border: 1px solid #ff4d4f;
}

/* 错误图标 */
// .error-icon {
//   display: flex;
//   justify-content: center;
//   align-items: center;
//   width: 32rpx;
//   height: 32rpx;
//   background-color: #ff4d4f;
//   color: white;
//   border-radius: 50%;
//   font-size: 20rpx;
//   font-weight: bold;
//   margin-right: 12rpx;
//   flex-shrink: 0;
//   margin-top: 2rpx;
// }

/* 错误文本 */
.error-text {
  color: #ff4d4f;
  font-size: 24rpx;
  line-height: 1.5;
  font-weight: 500;
  flex: 1;
  word-wrap: break-word;
  word-break: break-all;
}

/* 错误提示动画 - 淡入效果 */
@keyframes errorFadeIn {
  0% {
    opacity: 0;
    transform: translateY(-10rpx);
    max-height: 0;
  }

  100% {
    opacity: 1;
    transform: translateY(0);
    max-height: 200rpx;
  }
}

/* 响应式设计 */
@media (max-width: 575px) {
  .form-item-wrapper {
    margin-bottom: 16rpx;
  }

  .error-message-bottom {
    padding: 10rpx 14rpx;
    margin-top: 6rpx;
  }

  .error-text {
    font-size: 22rpx;
  }

  .error-icon {
    width: 28rpx;
    height: 28rpx;
    font-size: 18rpx;
    margin-right: 10rpx;
  }
}
</style>