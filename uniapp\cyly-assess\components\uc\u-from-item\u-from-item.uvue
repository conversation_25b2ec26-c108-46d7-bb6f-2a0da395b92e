<template>
  <view class="form-item-wrapper" :style="getStyle">
    <!-- 输入框和错误提示在同一行 -->
    <view class="form-item-row">
      <!-- 输入框容器 -->
      <view class="input-container" :class="{ 'has-error': showError && !isValid }">
        <slot></slot>
      </view>
      <!-- 错误提示 - 在输入框右侧显示 -->
      <view class="error-message-inline" v-if="showError && !isValid">
        <view class="error-icon">!</view>
        <text class="error-text">{{ hintMessage }}</text>
      </view>
    </view>
  </view>
</template>

<script lang="uts" setup>
    import { FormItemData,FormItemRule,FormItemVerifyResult } from "../types"
    import { useVerify,findParent } from "../utils"
    const isValid = ref(true)
    const parentLabelName = "u-form"
	  const hintMessage = ref('\u3000')
    const instance = getCurrentInstance()!;
    const props = defineProps({
        customStyle: {
          type: Object as PropType<UTSJSONObject>,
          default: {} as UTSJSONObject as UTSJSONObject
        },
        field: {
            type: String,
            default: ''
        },
        rule: {
          type: Object as PropType<FormItemRule>,
          default: (): FormItemRule =>{
            return {} as FormItemRule
          }
        },
        showError:{
          type:Boolean,
          default:true
        },
        marginTop:{
          type:Number,
          default:0
        }
    });

    const getStyle = computed(() => {
      console.log(props.marginTop,"props.marginTop");
      const mp:Map<string, string>  = new Map();
      console.log(props.marginTop,"props.marginTop");
      mp.set('margin-top', `${props.marginTop}rpx`);
      return mp;
    })
	function verify(value : any, callback : (res : FormItemVerifyResult) => void){
		return useVerify(isValid, hintMessage, props.field as string, props.rule as FormItemRule, value, callback)
	}
	function pushFormItemFieldToForm(){
			const that = instance.proxy!;
			//找到父组件form
			const parent = findParent(that, [parentLabelName]);
			console.log("==that==:",that.$parent?.$options.name)
			if (parent == null) {
				console.error('error:','u-form-item must be used inside u-form');
				return;
			}
			const item = {
				field: props.field as string,
				instance: that
			} as FormItemData;
			parent.$callMethod('pushFielditem', item)
	}
	onMounted(()=>{
		pushFormItemFieldToForm();
	})
	defineExpose({
		verify
	})
</script>

<style lang="scss" scoped>
/* 表单项包装器 */
.form-item-wrapper {
  margin-bottom: 24rpx;
  width: 100%;
}

/* 表单项行 - 输入框和错误提示在同一行 */
.form-item-row {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 16rpx;
  /* 输入框和错误提示之间的间距 */
}

/* 输入框容器 */
.input-container {
  flex: 1;
  min-width: 0;
  /* 防止flex子项溢出 */
}


/* 内联错误提示样式 */
.error-message-inline {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  /* 防止错误提示被压缩 */
  max-width: 240rpx;
  /* 限制最大宽度，防止过长 */
  padding: 8rpx 12rpx;
  background-color: rgba(255, 77, 79, 0.08);
  border-radius: 20rpx;
  border: 1px solid rgba(255, 77, 79, 0.2);

  /* 添加淡入动画 */
  animation: errorSlideIn 0.3s ease-out;
}

.has-error {
  border: 1px solid #ff4d4f;
}

/* 错误图标 */
.error-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 28rpx;
  height: 28rpx;
  background-color: #ff4d4f;
  color: white;
  border-radius: 50%;
  font-size: 18rpx;
  font-weight: bold;
  margin-right: 8rpx;
  flex-shrink: 0;
}

/* 错误文本 */
.error-text {
  color: #ff4d4f;
  font-size: 22rpx;
  line-height: 1.3;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 错误提示动画 - 从右侧滑入 */
@keyframes errorSlideIn {
  0% {
    opacity: 0;
    transform: translateX(20rpx);
  }

  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 响应式设计 */
@media (max-width: 575px) {
  .form-item-wrapper {
    margin-bottom: 16rpx;
  }

  .form-item-row {
    gap: 12rpx;
  }

  .error-message-inline {
    max-width: 180rpx;
    padding: 6rpx 10rpx;
  }

  .error-text {
    font-size: 20rpx;
  }

  .error-icon {
    width: 24rpx;
    height: 24rpx;
    font-size: 16rpx;
    margin-right: 6rpx;
  }
}

/* 超小屏幕适配 */
@media (max-width: 400px) {
  .form-item-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 8rpx;
  }

  .error-message-inline {
    max-width: 100%;
    align-self: flex-start;
  }
}
</style>