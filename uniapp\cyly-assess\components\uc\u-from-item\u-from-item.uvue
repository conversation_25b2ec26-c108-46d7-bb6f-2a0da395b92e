<template>
  <view class="ant-form-item" :style="[customStyle]">
    <view class="ant-form-item-control" :class="{ 'ant-form-item-has-error': showError && !isValid }">
      <view class="ant-form-item-control-input">
        <view class="ant-form-item-control-input-content">
          <slot></slot>
        </view>
      </view>
      <view class="ant-form-item-explain ant-form-item-explain-error" v-if="showError && !isValid">
        <view class="ant-form-item-explain-error-content">
          <text>{{ hintMessage }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="uts" setup>
    import { FormItemData,FormItemRule,FormItemVerifyResult } from "../types"
    import { useVerify,findParent } from "../utils"
    const isValid = ref(true)
    const parentLabelName = "u-form"
	  const hintMessage = ref('\u3000')
    const instance = getCurrentInstance()!;
    const props = defineProps({
        customStyle: {
          type: Object as PropType<UTSJSONObject>,
          default: {} as UTSJSONObject as UTSJSONObject
        },
        field: {
            type: String,
            default: ''
        },
        rule: {
          type: Object as PropType<FormItemRule>,
          default: (): FormItemRule =>{
            return {} as FormItemRule
          }
        },
        showError:{
          type:Boolean,
          default:true
        }
    });

	function verify(value : any, callback : (res : FormItemVerifyResult) => void){
		return useVerify(isValid, hintMessage, props.field as string, props.rule, value, callback)
	}
	function pushFormItemFieldToForm(){
			const that = instance.proxy!;
			//找到父组件form
			const parent = findParent(that, [parentLabelName,'UForm']);
			if (parent == null) {
				console.error('u-form-item must be used inside u-form');
				return;
			}
			const item = {
        field: props.field as string,
			  instance: that
			} as FormItemData;
			parent.$callMethod('pushFielditem', item)
	}
    pushFormItemFieldToForm();
	defineExpose({
		verify
	})
</script>

<style lang="scss" scoped>
/* Ant Design 风格的表单项样式 */
.ant-form-item {
  margin-bottom: 24rpx;
  vertical-align: top;
}

.ant-form-item-control {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 0;
}

.ant-form-item-control-input {
  position: relative;
  display: flex;
  align-items: center;
  min-height: 64rpx;
}

.ant-form-item-control-input-content {
  flex: auto;
  max-width: 100%;
}

/* 错误状态样式 */
.ant-form-item-has-error {
  /* 当表单项有错误时，影响内部的输入框样式 */
  ::v-deep .fui-input__input,
  ::v-deep input,
  ::v-deep textarea {
    border-color: #ff4d4f !important;
    box-shadow: 0 0 0 4rpx rgba(255, 77, 79, 0.1) !important;
  }

  ::v-deep .fui-input__input:focus,
  ::v-deep input:focus,
  ::v-deep textarea:focus {
    border-color: #ff4d4f !important;
    box-shadow: 0 0 0 4rpx rgba(255, 77, 79, 0.2) !important;
  }
}

/* Ant Design 错误提示样式 */
.ant-form-item-explain {
  font-size: 24rpx;
  line-height: 1.5715;
  transition: color 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
}

.ant-form-item-explain-error {
  color: #ff4d4f;
  margin-top: 8rpx;

  /* 添加淡入动画 */
  animation: ant-form-explain-appear 0.15s cubic-bezier(0.645, 0.045, 0.355, 1);
}

.ant-form-item-explain-error-content {
  position: relative;

  /* Ant Design 特有的错误图标 */
  &::before {
    content: "";
    display: inline-block;
    width: 24rpx;
    height: 24rpx;
    margin-right: 8rpx;
    background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill='%23ff4d4f' d='M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm-32 232c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V296zm32 440a48.01 48.01 0 0 1 0-96 48.01 48.01 0 0 1 0 96z'/%3E%3C/svg%3E");
    background-size: contain;
    background-repeat: no-repeat;
    vertical-align: top;
    margin-top: 2rpx;
  }

  text {
    color: #ff4d4f;
    font-size: 24rpx;
    line-height: 1.5715;
  }
}

/* Ant Design 动画效果 */
@keyframes ant-form-explain-appear {
  0% {
    opacity: 0;
    transform: translateY(-5rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 575px) {
  .ant-form-item {
    margin-bottom: 16rpx;
  }

  .ant-form-item-explain-error-content text {
    font-size: 22rpx;
  }
}
</style>
