import { ToastType, ToastPosition } from "../types"
  // import lSvg from "@/uni_modules/lime-svg/components/l-svg/l-svg"

  // Props 定义
  
const __sfc__ = defineComponent({
  __name: 'u-toast',
  props: {
    // 是否显示
    visible: {
      type: Boolean,
      default: false
    },
    // 消息内容
    message: {
      type: String,
      default: ''
    },
    // 提示类型
    type: {
      type: String as PropType<ToastType>,
      default: 'info' as ToastType
    },
    // 自定义背景色
    backgroundColor: {
      type: String,
      default: ''
    },
    // 自定义文字颜色
    textColor: {
      type: String,
      default: ''
    },
    // 图标
    icon: {
      type: String,
      default: ''
    },
    // 图标颜色（用于 SVG 图标）
    iconColor: {
      type: String,
      default: ''
    },
    // 是否显示图标
    showIcon: {
      type: Boolean,
      default: true
    },
    // 自动关闭时间（毫秒）
    duration: {
      type: Number,
      default: 3000
    },
    // 位置
    position: {
      type: String as PropType<ToastPosition>,
      default: 'top' as ToastPosition
    },
    // 距离顶部的距离
    top: {
      type: Number,
      default: 0
    },
    // 自定义样式
    customStyle: {
      type: Object as PropType<UTSJSONObject>,
      default: {} as UTSJSONObject
    }
  },
  emits: ['close', 'click'],
  setup(__props, { expose: __expose }: SetupContext): any | null {
const __ins = getCurrentInstance()!;
const _ctx = __ins.proxy as InstanceType<typeof __sfc__>;
const _cache = __ins.renderCache;

  const props = __props

  // Emits 定义
  function emit(event: string, ...do_not_transform_spread: Array<any | null>) {
__ins.emit(event, ...do_not_transform_spread)
}

  // 响应式数据
  const visible = ref(props.visible)
  const timer = ref<number | null>(null)

  // 计算属性
  const iconType = computed(() => {
    if (props.icon === '') return 'none'
    // 检查是否是单个字符（emoji 或文字图标）
    if (props.icon.length === 1 || /^[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/u.test(props.icon)) return 'text'
    return 'custom'
  })

  const toastClass = computed(() => {
    const classes = ['u-toast-' + props.type]
    if (props.position === 'top') classes.push('u-toast-top')
    if (props.position === 'bottom') classes.push('u-toast-bottom')
    return classes.join(' ')
  })

  const containerStyle = computed(() => {
    const style: UTSJSONObject = {__$originalPosition: new UTSSourceMapPosition("style", "components/uc/u-toast/u-toast.uvue", 113, 11),}
    const topValue = props.top != null ? props.top : 0
    if (props?.position === 'top') {
      style['top'] = topValue + 'rpx'
    } else if (props?.position === 'bottom') {
      style['bottom'] = topValue + 'rpx'
    }
    return style
  })

  const toastStyle = computed(() => {
    const style: UTSJSONObject = {__$originalPosition: new UTSSourceMapPosition("style", "components/uc/u-toast/u-toast.uvue", 124, 11),}
    if (props.backgroundColor != null && props.backgroundColor !== '') {
      style['background-color'] = props.backgroundColor
    }
    // 合并自定义样式
    for (const key in props.customStyle) {
      style[key] = props.customStyle[key]
    }
    return style
  })

  const messageStyle = computed(() => {
    const style: UTSJSONObject = {__$originalPosition: new UTSSourceMapPosition("style", "components/uc/u-toast/u-toast.uvue", 136, 11),}
    if (props.textColor != null && props.textColor !== '') {
      style['color'] = props.textColor
    }
    return style
  })

  // 方法 - 先声明 clearTimer
  function clearTimer() {
    const timerId = timer.value
    if (timerId != null) {
      clearTimeout(timerId)
      timer.value = null
    }
  }

  function hide() {
    visible.value = false
    clearTimer()
    emit('close')
  }

  function show() {
    visible.value = true
    if (props.duration > 0) {
      clearTimer()
      timer.value = setTimeout(() => {
        hide()
      }, props.duration)
    }
  }

  // 监听 props.visible 变化
  watchEffect(() => {
    if (props.visible) {
      show()
    } else {
      hide()
    }
  })

  // 组件卸载时清理定时器
  onUnmounted(() => {
    clearTimer()
  })

  // 暴露方法
  __expose({
    show,
    hide
  })

return (): any | null => {

  return isTrue(unref(visible))
    ? createElementVNode("view", utsMapOf({
        key: 0,
        class: "u-toast-container",
        style: normalizeStyle(unref(containerStyle))
      }), [
        createElementVNode("view", utsMapOf({
          class: normalizeClass(["u-toast", unref(toastClass)]),
          style: normalizeStyle(unref(toastStyle))
        }), [
          isTrue(_ctx.showIcon)
            ? createElementVNode("view", utsMapOf({
                key: 0,
                class: "u-toast-icon"
              }), [
                unref(iconType) === 'text'
                  ? createElementVNode("text", utsMapOf({
                      key: 0,
                      class: "u-toast-icon-text"
                    }), toDisplayString(_ctx.icon), 1 /* TEXT */)
                  : unref(iconType) === 'custom'
                    ? createElementVNode("view", utsMapOf({
                        key: 1,
                        class: "u-toast-icon-custom"
                      }), [
                        renderSlot(_ctx.$slots, "icon")
                      ])
                    : createCommentVNode("v-if", true)
              ])
            : createCommentVNode("v-if", true),
          createElementVNode("view", utsMapOf({ class: "u-toast-content" }), [
            createElementVNode("text", utsMapOf({
              class: "u-toast-message",
              style: normalizeStyle(unref(messageStyle))
            }), toDisplayString(_ctx.message), 5 /* TEXT, STYLE */)
          ])
        ], 6 /* CLASS, STYLE */)
      ], 4 /* STYLE */)
    : createCommentVNode("v-if", true)
}
}

})
export default __sfc__
const GenComponentsUcUToastUToastStyles = [utsMapOf([["u-toast-container", padStyleMapOf(utsMapOf([["position", "fixed"], ["left", 0], ["right", 0], ["zIndex", 9999], ["pointerEvents", "none"], ["display", "flex"], ["justifyContent", "center"], ["paddingTop", 0], ["paddingRight", "32rpx"], ["paddingBottom", 0], ["paddingLeft", "32rpx"]]))], ["u-toast", padStyleMapOf(utsMapOf([["display", "flex"], ["alignItems", "center"], ["minHeight", "88rpx"], ["paddingTop", "16rpx"], ["paddingRight", "24rpx"], ["paddingBottom", "16rpx"], ["paddingLeft", "24rpx"], ["borderTopLeftRadius", "12rpx"], ["borderTopRightRadius", "12rpx"], ["borderBottomRightRadius", "12rpx"], ["borderBottomLeftRadius", "12rpx"], ["boxShadow", "0 4rpx 12rpx rgba(0, 0, 0, 0.1)"], ["backdropFilter", "blur(10rpx)"], ["pointerEvents", "auto"], ["wordWrap", "break-word"], ["animation", "toastSlideIn 0.3s ease-out"]]))], ["u-toast-top", padStyleMapOf(utsMapOf([["transformOrigin", "top center"]]))], ["u-toast-bottom", padStyleMapOf(utsMapOf([["transformOrigin", "bottom center"]]))], ["u-toast-success", padStyleMapOf(utsMapOf([["backgroundColor", "rgba(82,196,26,0.9)"], ["color", "#FFFFFF"]]))], ["u-toast-error", padStyleMapOf(utsMapOf([["backgroundColor", "rgba(255,77,79,0.9)"], ["color", "#FFFFFF"]]))], ["u-toast-warning", padStyleMapOf(utsMapOf([["backgroundColor", "rgba(250,173,20,0.9)"], ["color", "#FFFFFF"]]))], ["u-toast-info", padStyleMapOf(utsMapOf([["backgroundColor", "rgba(24,144,255,0.9)"], ["color", "#FFFFFF"]]))], ["u-toast-default", padStyleMapOf(utsMapOf([["backgroundColor", "rgba(0,0,0,0.8)"], ["color", "#FFFFFF"]]))], ["u-toast-icon", padStyleMapOf(utsMapOf([["marginRight", "12rpx"], ["flexShrink", 0]]))], ["u-toast-icon-text", padStyleMapOf(utsMapOf([["fontSize", "32rpx"], ["lineHeight", 1]]))], ["u-toast-content", padStyleMapOf(utsMapOf([["flex", 1], ["minWidth", 0]]))], ["u-toast-message", padStyleMapOf(utsMapOf([["fontSize", "28rpx"], ["lineHeight", 1.4], ["wordWrap", "break-word"]]))], ["@FONT-FACE", utsMapOf([["0", utsMapOf([])], ["1", utsMapOf([["u-toast", utsMapOf([["", utsMapOf([["minHeight", "80rpx"], ["paddingTop", "14rpx"], ["paddingRight", "20rpx"], ["paddingBottom", "14rpx"], ["paddingLeft", "20rpx"]])]])], ["u-toast-message", utsMapOf([["", utsMapOf([["fontSize", "26rpx"]])]])], ["u-toast-icon-text", utsMapOf([["", utsMapOf([["fontSize", "28rpx"]])]])]])]])]])]
