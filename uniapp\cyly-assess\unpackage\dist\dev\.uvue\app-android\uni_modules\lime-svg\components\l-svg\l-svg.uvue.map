{"version": 3, "sources": ["uni_modules/lime-svg/components/l-svg/l-svg.uvue"], "names": [], "mappings": "AAuCC,OAAO,EAAE,SAAS,EAAE,MAAM,QAAQ,CAAA;AAKlC,OAAO,EAAE,aAAa,EAAE,YAAY,EAAE,MAAM,SAAS,CAAA;AAGrD,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;;;;;;;;;;;;;;;QAtBrD;;;;;;;;;;;;WAYG;QAWH,IAAI,WAAW,EAAG,WAAW,GAAG,IAAI,GAAG,IAAI,CAAA;QAI3C,MAAM,KAAK,GAAG,OAKZ,CAAA;QAEF,SAAA,IAAA,CAAA,KAAA,EAAA,MAAA,EAAA,GAAA,uBAAA,EAAA,KAAA,CAAA,GAAA,GAAA,IAAA,CAAA;YAAA,KAAA,CAAA,IAAA,CAAA,KAAA,EAAA,GAAA,uBAAA,CAAA,CAAA;QAAA,CAA2C;QAC3C,MAAM,IAAI,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QAC3B,MAAM,MAAM,GAAG,GAAG,CAAC,UAAU,GAAG,IAAI,EAAE,IAAI,CAAC,CAAA;QAQ3C,MAAM,QAAQ,GAAG,GAAG,CAAC,EAAE,CAAC,CAAA;QACxB,MAAM,SAAS,GAAG,CAAC,GAAG,EAAG,MAAM,EAAE,MAAM,EAAG,MAAM,GAAI,MAAM,CAAC,EAAE;YAC5D,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC;gBAAE,OAAO,GAAG,MAAM,KAAK,GAAG,IAAI,CAAA;YACtD,OAAO,GAAG,MAAM,KAAK,GAAG,IAAI,CAAA;QAC7B,CAAC,CAAA;QACD,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAK,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,EAAE;YAClD,MAAM,KAAK,GAAG,IAAI,GAAG,CAAC,MAAM,EAAE,MAAM,GAAG,CAAA;YAavC,IAAI,KAAK,CAAC,KAAK,IAAI,EAAE,EAAE;gBACtB,KAAK,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,KAAK,CAAC,CAAA;aAC/B;YACD,OAAO,KAAK,CAAA;QACb,CAAC,CAAC,CAAA;QAOF,MAAM,YAAY,GAAG,IAAI,wBAAwB,CAAC,MAAM,CAAC,CAAA;QACzD,MAAM,UAAU,GAAG,IAAI,kBAAkB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAA;QAYhE,MAAM,OAAO,GAAG,GAAG,EAAE;YACpB,IAAI,CAAC,OAAO,EAAE,UAAU,CAAC,CAAA;QAC1B,CAAC,CAAA;QACD,MAAM,MAAM,GAAG,CAAC,CAAC,EAAG,kBAAkB,EAAE,EAAE;YAMzC,MAAM,MAAM,GAAG,IAAI,oBAAoB,CAAC,GAAG,EAAE,GAAG,CAAC,CAAA;YACjD,MAAM,SAAS,GAAG,IAAI,iBAAiB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;YAYvD,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC,CAAA;QACxB,CAAC,CAAA;QACD,MAAM;QAEN,MAAM,MAAM,GAAG,GAAG,CAAC,iBAAiB,GAAG,IAAI,EAAE,IAAI,CAAC,CAAA;QAClD,MAAM,SAAS,GAAG,GAAG,EAAE;YACtB,IAAI,IAAI,CAAC,KAAK,IAAI,EAAE,EAAE;gBACrB,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAC;aACtD;QACF,CAAC,CAAA;QACD,MAAM,WAAW,GAAG,GAAG,EAAE;YACxB,IAAI,KAAK,CAAC,KAAK,IAAI,EAAE,IAAI,IAAI,CAAC,KAAK,IAAI,EAAE,EAAE;gBAC1C,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,yBAAyB,KAAK,CAAC,KAAK,KAAK,CAAC,CAAC;aAChE;QACF,CAAC,CAAA;QACD,MAAM,KAAK,GAAG,CAAC,CAAC,EAAG,oBAAoB,EAAE,EAAE;YAC1C,IAAI,CAAC,OAAO,EAAE,UAAU,CAAC,CAAA;QAC1B,CAAC,CAAA;QACD,MAAM,MAAM,GAAG,CAAC,CAAC,EAAG,mBAAmB,EAAE,EAAE;YAC1C,WAAW,CAAC,GAAG,EAAE;gBAChB,IAAI,KAAK,CAAC,GAAG,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG;oBAAE,OAAM;gBACzC,IAAI,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE;oBACjC,IAAI,CAAC,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;oBACpC,SAAS,EAAE,CAAA;oBACX,WAAW,EAAE,CAAA;iBACb;qBAAM,IAAI,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE;oBAC3C,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;wBACnC,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC;wBACjB,SAAS,EAAE,CAAA;wBACX,WAAW,EAAE,CAAA;oBACd,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;wBACd,IAAI,CAAC,OAAO,EAAE,UAAU,CAAC,CAAA;wBACzB,OAAO,CAAC,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAA,EAAA,0DAAC,CAAC,CAAA;oBAC7D,CAAC,CAAC,CAAA;iBACF;qBAAM;oBACN,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,GAAG,CAAA;oBACtB,SAAS,EAAE,CAAA;oBACX,WAAW,EAAE,CAAA;iBACb;YACF,CAAC,CAAC,CAAA;QACH,CAAC,CAAA;QACD,MAAM,OAAO,GAAG,CAAC,KAAK,EAAG,sBAAsB,EAAE,EAAE;YAClD,MAAM,IAAI,GAAG,aAAa,CAAC,MAAM,CAAC,EAAE,EAAE,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,aAAa,CAAC,CAAC,CAAE,uCAAuC;YACtH,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAA;YAEpC,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAA;YAKtD,IAAI,IAAI,IAAI,OAAO,EAAE;gBACpB,IAAI,CAAC,OAAO,CAAC,CAAA;aACb;iBAAM,IAAI,IAAI,IAAI,MAAM,EAAE;gBAC1B,MAAM,KAAK,GAAG,MAAM,EAAE,SAAS,CAAC,OAAO,CAAC,IAAI,GAAG,CAAA;gBAC/C,MAAM,MAAM,GAAG,MAAM,EAAE,SAAS,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAA;gBAEjD,MAAM,UAAU,GAAG,IAAI,oBAAoB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAA;gBAC1D,MAAM,SAAS,GAAG,IAAI,iBAAiB,CAAC,MAAM,EAAE,UAAU,CAAC,CAAA;gBAY3D,IAAI,CAAC,IAAI,EAAE,SAAS,CAAC,CAAA;aACrB;iBAAM,IAAI,IAAI,IAAI,OAAO,EAAE;gBAC3B,IAAI,CAAC,IAAI,EAAE,UAAU,CAAC,CAAA;aACtB;QACF,CAAC,CAAA;QAKD,SAAS,UAAU,CAAC,CAAC,EAAG,sBAAsB;YAC7C,WAAW,GAAG,IAAI,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAChD,WAAW,EAAE,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;YAClC,WAAW,EAAE,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;QACtC,CAAC;QACD,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,MAAM,EAAE,MAAM,GAAG,CAAA;QACrC,WAAW,CAAC,GAAG,EAAE;YAEhB,mCAAmC;YACnC,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE;gBAC/C,IAAG,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;oBACtB,WAAW,EAAE,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;oBAC3C,OAAM;iBACN;gBACD,GAAG,CAAC,YAAY,CAAC;oBAChB,GAAG,EAAE,KAAK,CAAC,GAAG;oBACd,OAAO,CAAC,GAAG;wBACV,gCAAgC;wBAChC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,CAAC,YAAY,CAAC,CAAA;wBACpC,WAAW,EAAE,SAAS,CAAC,GAAG,CAAC,YAAY,CAAC,CAAA;oBACzC,CAAC;iBACD,CAAC,CAAA;aACF;iBAAM;gBACN,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC;gBACvB,WAAW,EAAE,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;aACjC;QAMF,CAAC,CAAC,CAAA;QACF,WAAW,CAAC,GAAG,EAAE;YAChB,WAAW,EAAE,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;QACtC,CAAC,CAAC,CAAA;QAKF,MAAM;;mBAzPN,MAAA,CAAA,IAAA,CAAA,GAAA,CACoE;;oBAD1D,GAAA,EAAK,CAAA;kCAAa;oBAAJ,OAAI,EAAA,QAAQ;oBAAa,GAAA,EAAA,MAAO;oBAAQ,OAAI,EAAE,KAAM;oBAAG,MAAA,EAAA,MAAS;oBACvF,SAAI,EAAA,OAAA;;iBAGL,CAAA,EAAA,IAAA,EAAA,GAAA,CAAA,gCAAmH,CAAA;;oBAAtG,GAAA,EAAK,CAAA;yBAAwB,EAAA,OAAM;kBAAG,EAAA,IAAI,CAAA,MAAE,EAAA,QAAU,CAAA;oBAAG,MAAA,EAAK,UAAS;oBAAG,OAAI,EAAE,OAAM", "sourcesContent": ["<template>\r\n\r\n\t<web-view class=\"l-svg\" ref=\"webRef\" v-if=\"web\" @error=\"error\" @load=\"loaded\" @message=\"message\"\r\n\t\tsrc=\"/uni_modules/lime-svg/hybrid/html/index.html?v=21\"></web-view>\r\n\t<!-- <l-svg-x class=\"l-svg\" v-else :src=\"path\" :color=\"color\" @error=\"onError\" @load=\"onLoad\"\r\n\t\t@click=\"$emit('click')\"></l-svg-x> -->\r\n\t<native-view class=\"l-svg\" v-else v-bind=\"$attrs\" @init=\"onviewinit\" @error=\"onError\" @load=\"onLoad\"></native-view>\t\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n</template>\r\n\r\n<script setup lang=\"uts\">\r\n\t/**\r\n\t * Svg SVG组件\r\n\t * @description 用于渲染SVG路径元素，支持动态颜色和继承属性\r\n\t * <br>插件类型：LSvpComponentPublicInstance \r\n\t * @tutorial https://ext.dcloud.net.cn/plugin?name=lime-svg\r\n\t * \r\n\t * @property {string} src SVG路径\r\n\t * @property {string} color 路径颜色（默认：\"currentColor\"）\r\n\t * @property {boolean} web 是否启用Web优化模式（默认：false）\r\n\t * @property {boolean} inherit 是否继承父级SVG属性（默认：true）\r\n\t * @event {Function} load SVG路径加载完成时触发\r\n\t * @event {Function} error SVG路径加载失败时触发\r\n\t */\r\n\t\r\n\timport { LSvpProps } from './type'\r\n\r\n\r\n\r\n\r\n\timport { pathToDataUrl, svgToDataUrl } from './utils'\r\n\r\n\r\n\timport { NativeImage } from \"@/uni_modules/lime-svg\";\r\n\tlet nativeImage : NativeImage | null = null\r\n\r\n\r\n\r\n\tconst props = withDefaults(defineProps<LSvpProps>(), {\r\n\t\tsrc: '',\r\n\t\tcolor: '',\r\n\t\tweb: false,\r\n\t\tinherit: false\r\n\t})\r\n\t\r\n\tconst emit = defineEmits(['load', 'error'])\r\n\tconst path = ref(props.src)\r\n\tconst svgRef = ref<UniElement | null>(null)\r\n\r\n\r\n\r\n\r\n\r\n\t\r\n\t\r\n\tconst imageURL = ref('')\r\n\tconst formatUrl = (url : string, action : string) : string => {\r\n\t\tif (url.indexOf(`'`) > 0) return `${action}(\"${url}\")`\r\n\t\treturn `${action}('${url}')`\r\n\t}\r\n\tconst styles = computed(() : Map<string, string> => {\r\n\t\tconst style = new Map<string, string>()\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\t\tif (props.color != '') {\r\n\t\t\tstyle.set('color', props.color)\r\n\t\t}\r\n\t\treturn style\r\n\t})\r\n\r\n\t\r\n\r\n\r\n\r\n\r\n\tconst errorDetaill = new UniImageErrorEventDetail('加载失败')\r\n\tconst errorEvent = new UniImageErrorEvent('error', errorDetaill)\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\tconst onError = () => {\r\n\t\temit('error', errorEvent)\r\n\t}\r\n\tconst onLoad = (e : UniNativeViewEvent) => {\r\n\r\n\r\n\r\n\r\n\r\n\t\tconst detail = new ImageLoadEventDetail(512, 512)\r\n\t\tconst loadEvent = new UniImageLoadEvent('load', detail)\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\t\temit('load', loadEvent)\r\n\t}\r\n\t// app\r\n\r\n\tconst webRef = ref<UniWebViewElement | null>(null)\r\n\tconst setSvgSrc = () => {\r\n\t\tif (path.value != '') {\r\n\t\t\twebRef.value?.evalJS(formatUrl(path.value, 'setSrc'));\r\n\t\t}\r\n\t}\r\n\tconst setSvgColor = () => {\r\n\t\tif (props.color != '' && path.value != '') {\r\n\t\t\twebRef.value?.evalJS(`setStyle({\"--color\": \"${props.color}\"})`);\r\n\t\t}\r\n\t}\r\n\tconst error = (_ : UniWebViewErrorEvent) => {\r\n\t\temit('error', errorEvent)\r\n\t}\r\n\tconst loaded = (_ : UniWebViewLoadEvent) => {\r\n\t\twatchEffect(() => {\r\n\t\t\tif (props.src == '' || !props.web) return\r\n\t\t\tif (props.src.startsWith('<svg')) {\r\n\t\t\t\tpath.value = svgToDataUrl(props.src)\r\n\t\t\t\tsetSvgSrc()\r\n\t\t\t\tsetSvgColor()\r\n\t\t\t} else if (props.src.startsWith('/static')) {\r\n\t\t\t\tpathToDataUrl(props.src).then(res => {\r\n\t\t\t\t\tpath.value = res;\r\n\t\t\t\t\tsetSvgSrc()\r\n\t\t\t\t\tsetSvgColor()\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\temit('error', errorEvent)\r\n\t\t\t\t\tconsole.warn(\"[lime-svg]\" + props.src + JSON.stringify(err))\r\n\t\t\t\t})\r\n\t\t\t} else {\r\n\t\t\t\tpath.value = props.src\r\n\t\t\t\tsetSvgSrc()\r\n\t\t\t\tsetSvgColor()\r\n\t\t\t}\r\n\t\t})\r\n\t}\r\n\tconst message = (event : UniWebViewMessageEvent) => {\r\n\t\tconst data = UTSJSONObject.assign({}, event.detail.data[0] as UTSJSONObject);  //event.detail.data[0] as UTSJSONObject\r\n\t\tconst type = data.getString('event')\r\n\r\n\t\tconst detail = data.getJSON('data')?.getJSON('detail')\r\n\r\n\r\n\r\n\r\n\t\tif (type == 'click') {\r\n\t\t\temit('click')\r\n\t\t} else if (type == 'load') {\r\n\t\t\tconst width = detail?.getNumber('width') ?? 512\r\n\t\t\tconst height = detail?.getNumber('height') ?? 512\r\n\r\n\t\t\tconst loadDetail = new ImageLoadEventDetail(width, height)\r\n\t\t\tconst loadEvent = new UniImageLoadEvent('load', loadDetail)\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\t\t\temit(type, loadEvent)\r\n\t\t} else if (type == 'error') {\r\n\t\t\temit(type, errorEvent)\r\n\t\t}\r\n\t}\r\n\r\n\r\n\r\n\r\n\tfunction onviewinit(e : UniNativeViewInitEvent) {\r\n\t\tnativeImage = new NativeImage(e.detail.element);\r\n\t\tnativeImage?.updateSrc(path.value)\r\n\t\tnativeImage?.updateColor(props.color)\r\n\t}\r\n\tconst map = new Map<string, string>()\r\n\twatchEffect(() => {\r\n\r\n\t\t// ios uts组件使用uni.request会报错，故在这里使用\r\n\t\tif (!props.web && props.src.startsWith('http')) {\r\n\t\t\tif(map.has(props.src)) {\r\n\t\t\t\tnativeImage?.updateSrc(map.get(props.src)!)\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t\tuni.downloadFile({\r\n\t\t\t\turl: props.src,\r\n\t\t\t\tsuccess(res) {\r\n\t\t\t\t\t// path.value = res.tempFilePath\r\n\t\t\t\t\tmap.set(props.src, res.tempFilePath)\r\n\t\t\t\t\tnativeImage?.updateSrc(res.tempFilePath)\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t} else {\r\n\t\t\tpath.value = props.src;\r\n\t\t\tnativeImage?.updateSrc(props.src)\r\n\t\t}\r\n\r\n\r\n\r\n\r\n\r\n\t})\r\n\twatchEffect(() => {\r\n\t\tnativeImage?.updateColor(props.color)\r\n\t})\r\n\r\n\r\n\t\r\n\r\n\t// 小程序\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.l-svg {\r\n\t\t// align-self: flex-start;\r\n\r\n\t\twidth: 24px;\r\n\t\theight: 24px;\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\t}\r\n</style>"]}