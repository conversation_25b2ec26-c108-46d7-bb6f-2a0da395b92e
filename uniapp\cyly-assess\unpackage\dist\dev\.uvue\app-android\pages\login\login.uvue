import fuiStatusBar from "@/components/firstui/fui-status-bar/fui-status-bar";
import fuiFooter from "@/components/firstui/fui-footer/fui-footer";
import fuiRow from "@/components/firstui/fui-row/fui-row";
import fuiTabs from "@/components/firstui/fui-tabs/fui-tabs";
import fuiInput from "@/components/firstui/fui-input/fui-input";
import fuiButton from "@/components/firstui/fui-button/fui-button";
import fuiIcon from "@/components/firstui/fui-icon/fui-icon";
import fuiCheckbox from "@/components/firstui/fui-checkbox/fui-checkbox";
import { FuiTabsItemParam } from "@/components/firstui/fui-types/index.uts";
import uForm from "@/components/uc/u-form/u-form";
import uFromItem from "@/components/uc/u-from-item/u-from-item";
import { phoneModelType } from "./types";
import { FormItemVerifyResult, FormValidResultItem } from "@/components/uc/types/index.uts";
import { ComponentPublicInstance } from 'vue';
const __sfc__ = defineComponent({
    __name: 'login',
    setup(__props): any | null {
        const __ins = getCurrentInstance()!;
        const _ctx = __ins.proxy as InstanceType<typeof __sfc__>;
        const _cache = __ins.renderCache;
        const inputStyle = { __$originalPosition: new UTSSourceMapPosition("inputStyle", "pages/login/login.uvue", 75, 7),
            inputBorder: true,
            size: 28,
            radius: 12,
            marginTop: 16,
            type: "number"
        } as UTSJSONObject;
        // 引入组件
        const loginForm = ref<ComponentPublicInstance | null>(null);
        function onBlur(field: string) {
            console.log("field:", field, " at pages/login/login.uvue:100");
            // 将 loginForm.value 赋值给局部变量以避免智能转换问题
            const formInstance = loginForm.value;
            if (formInstance != null) {
                // 使用 $callMethod 调用组件方法
                formInstance.$callMethod('validItem', field, {
                    success() {
                        console.log("success", " at pages/login/login.uvue:108");
                    },
                    fail(res: FormItemVerifyResult | null) {
                        console.log("fail:", res, " at pages/login/login.uvue:111");
                    }
                });
            }
        }
        // 标签页数据
        const tabsList = ref([
            { name: '手机号登录', id: 0 },
            { name: '账号登录', id: 1 },
        ]);
        // 当前选中的标签页
        const current = ref(0);
        function handleChangeTab(e: FuiTabsItemParam) {
            console.log("handleChangeTab:", e, " at pages/login/login.uvue:125");
            if (e.index != null) {
                current.value = e.index as number;
            }
        }
        // 手机号登录表单
        const phoneModel = reactive<phoneModelType>({
            phone: '',
            code: ''
        } as phoneModelType);
        // 表单配置
        const phoneRules = ref({
            'phone': {
                type: 'string',
                required: true,
                message: '请输入手机号',
                trigger: ['blur', 'change']
            },
            'code': {
                type: 'string',
                required: true,
                message: '请输入验证码',
                trigger: ['blur', 'change']
            }
        });
        // 国家区号
        const countryCode = ref('86');
        // 验证码相关
        const codeText = ref('获取验证码');
        const canGetCode = ref(true);
        //-------
        // 账号登录表单
        const accountForm = reactive({
            username: '',
            password: '',
        });
        // 协议同意
        const isAgreeProtocol = ref(false);
        const isDisabledloginButton = computed((): boolean => {
            return !isAgreeProtocol.value;
        });
        // 登录按钮是否禁用
        function ChangeIsAgreeProtocol() {
            isAgreeProtocol.value = !isAgreeProtocol.value;
        }
        function onSubmit(e: any) {
            console.log("onSubmit:", e, " at pages/login/login.uvue:174");
        }
        return (): any | null => {
            const _component_block = resolveComponent("block");
            return createElementVNode("view", utsMapOf({ class: "login-container" }), [
                createVNode(unref(fuiStatusBar)),
                createElementVNode("view", utsMapOf({ class: "gradient-circle top-right" })),
                createElementVNode("view", utsMapOf({ class: "gradient-circle bottom-left" })),
                createElementVNode("view", utsMapOf({ class: "form-container" }), [
                    createElementVNode("view", utsMapOf({ class: "logo-title" }), [
                        createElementVNode("text", utsMapOf({ class: "title-text" }), "登录")
                    ]),
                    createVNode(unref(fuiTabs), utsMapOf({
                        class: "form-login-tabs",
                        tabs: unref(tabsList),
                        short: false,
                        center: "",
                        current: unref(current),
                        onChange: handleChangeTab
                    }), null, 8 /* PROPS */, ["tabs", "current"]),
                    createElementVNode("view", utsMapOf({ class: "login-form-container" }), [
                        unref(current) === 0
                            ? createVNode(_component_block, utsMapOf({ key: 0 }), utsMapOf({
                                default: withSlotCtx((): any[] => [
                                    createVNode(unref(uForm), utsMapOf({
                                        ref_key: "loginForm",
                                        ref: loginForm,
                                        onSubmited: onSubmit,
                                        triggerChange: "",
                                        modelValue: unref(phoneModel),
                                        "onUpdate:modelValue": $event => { trySetRefValue(phoneModel, $event); }
                                    }), utsMapOf({
                                        default: withSlotCtx((): any[] => [
                                            createVNode(unref(uFromItem), utsMapOf({
                                                field: "phone",
                                                rule: {
                                                    required: true,
                                                    type: 'phone',
                                                    message: '请输入正确的手机号'
                                                }
                                            }), utsMapOf({
                                                default: withSlotCtx((): any[] => [
                                                    createVNode(unref(fuiInput), utsMapOf({
                                                        onBlur: () => { onBlur('phone'); },
                                                        inputBorder: "",
                                                        size: 28,
                                                        radius: 12,
                                                        marginTop: 16,
                                                        type: "number",
                                                        placeholder: "请输入手机号",
                                                        modelValue: unref(phoneModel).phone,
                                                        "onUpdate:modelValue": ($event: string) => { (unref(phoneModel).phone) = $event; }
                                                    }), null, 8 /* PROPS */, ["onBlur", "modelValue", "onUpdate:modelValue"])
                                                ]),
                                                _: 1 /* STABLE */
                                            })),
                                            createVNode(unref(uFromItem), utsMapOf({
                                                field: "code",
                                                rule: {
                                                    required: true,
                                                    message: '请输入验证码'
                                                }
                                            }), utsMapOf({
                                                default: withSlotCtx((): any[] => [
                                                    createVNode(unref(fuiInput), utsMapOf({
                                                        onBlur: () => { onBlur('code'); },
                                                        inputBorder: "",
                                                        size: 28,
                                                        radius: 12,
                                                        marginTop: 16,
                                                        type: "number",
                                                        placeholder: "请输入验证码",
                                                        modelValue: unref(phoneModel).code,
                                                        "onUpdate:modelValue": ($event: string) => { (unref(phoneModel).code) = $event; }
                                                    }), null, 8 /* PROPS */, ["onBlur", "modelValue", "onUpdate:modelValue"])
                                                ]),
                                                _: 1 /* STABLE */
                                            }))
                                        ]),
                                        _: 1 /* STABLE */
                                    }), 8 /* PROPS */, ["modelValue"])
                                ]),
                                _: 1 /* STABLE */
                            }))
                            : createVNode(_component_block, utsMapOf({ key: 1 }), utsMapOf({
                                default: withSlotCtx((): any[] => [" 2 "]),
                                _: 1 /* STABLE */
                            }))
                    ]),
                    createElementVNode("view", utsMapOf({ class: "agreement" }), [
                        createVNode(unref(fuiRow), utsMapOf({
                            justify: "center",
                            class: "agreement-row"
                        }), utsMapOf({
                            default: withSlotCtx((): any[] => [
                                createVNode(unref(fuiCheckbox), utsMapOf({
                                    class: "agreement-checkbox",
                                    checked: unref(isAgreeProtocol),
                                    borderRadius: "8rpx",
                                    scaleRatio: 0.78,
                                    onChange: ChangeIsAgreeProtocol
                                }), null, 8 /* PROPS */, ["checked"]),
                                createElementVNode("text", utsMapOf({ class: "agreement-text" }), "我已阅读并同意"),
                                createElementVNode("text", utsMapOf({ class: "agreement-text agreement-link" }), "《用户服务条款》"),
                                createElementVNode("text", utsMapOf({ class: "agreement-text" }), "和"),
                                createElementVNode("text", utsMapOf({ class: "agreement-text agreement-link" }), "《隐私协议》")
                            ]),
                            _: 1 /* STABLE */
                        }))
                    ])
                ]),
                createElementVNode("view", utsMapOf({ class: "footer" }), [
                    createVNode(unref(fuiFooter), utsMapOf({ text: "by@海南长养乔智能科技有限责任公司" }))
                ])
            ]);
        };
    }
});
export default __sfc__;
const GenPagesLoginLoginStyles = [utsMapOf([["login-container", padStyleMapOf(utsMapOf([["height", "100%"], ["display", "flex"], ["flexDirection", "column"], ["alignItems", "center"], ["position", "relative"]]))], ["gradient-circle", padStyleMapOf(utsMapOf([["position", "absolute"], ["borderTopLeftRadius", "200rpx"], ["borderTopRightRadius", "200rpx"], ["borderBottomRightRadius", "200rpx"], ["borderBottomLeftRadius", "200rpx"], ["backgroundColor", "#33a1fd"], ["zIndex", 0], ["fontSize::after", 12], ["position::after", "absolute"], ["top::after", 0], ["left::after", 0]]))], ["top-right", padStyleMapOf(utsMapOf([["top", "-80rpx"], ["right", "-30rpx"], ["width", "300rpx"], ["height", "300rpx"], ["opacity", 0.08]]))], ["bottom-left", padStyleMapOf(utsMapOf([["bottom", "-60rpx"], ["left", "-60rpx"], ["width", "280rpx"], ["height", "280rpx"], ["opacity", 0.1]]))], ["logo-title", padStyleMapOf(utsMapOf([["textAlign", "center"], ["marginTop", "10rpx"], ["marginRight", 0], ["marginBottom", "30rpx"], ["marginLeft", 0]]))], ["title-text", padStyleMapOf(utsMapOf([["fontSize", "58rpx"], ["fontWeight", "bold"], ["color", "#465CFF"]]))], ["form-container", padStyleMapOf(utsMapOf([["display", "flex"], ["justifyContent", "center"], ["alignItems", "center"], ["width", "100%"], ["height", "70%"]]))], ["login-form-container", utsMapOf([[".form-container ", utsMapOf([["paddingTop", 0], ["paddingRight", "16rpx"], ["paddingBottom", 0], ["paddingLeft", "16rpx"], ["display", "flex"], ["minWidth", "580rpx"]])]])], ["agreement", padStyleMapOf(utsMapOf([["display", "flex"], ["alignItems", "flex-start"], ["marginTop", "24rpx"], ["color", "#CCCCCC"]]))], ["agreement-row", utsMapOf([[".agreement ", utsMapOf([["alignItems", "center"]])]])], ["agreement-text", padStyleMapOf(utsMapOf([["fontSize", "24rpx"]]))], ["agreement-link", padStyleMapOf(utsMapOf([["color", "#465CFF"], ["textDecoration", "none"]]))], ["footer", padStyleMapOf(utsMapOf([["textAlign", "center"], ["paddingTop", "16rpx"], ["paddingRight", "16rpx"], ["paddingBottom", "16rpx"], ["paddingLeft", "16rpx"], ["color", "#CCCCCC"], ["fontSize", "32rpx"], ["marginTop", "20rpx"]]))]])];
//# sourceMappingURL=login.uvue.map