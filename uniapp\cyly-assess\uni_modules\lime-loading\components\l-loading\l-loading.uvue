<template>
	<view class="l-loading" :class="classes">
		<!-- #ifndef APP-ANDROID || APP-IOS || APP-HARMONY -->
		<view class="l-loading__ball" v-if="type == 'ball'" :style="[spinnerStyle]"></view>
		<view class="l-loading__circular" v-if="type == 'circular'" :style="[spinnerStyle]"></view>
		<view class="l-loading__spinner" v-if="type == 'spinner'" :style="[spinnerStyle]">
			<view class="l-loading__dot" v-for="item in 12" :key="item" :style="{'--l-loading-dot': item}"></view>
		</view>
		<!-- #endif -->
		<!-- #ifdef APP-ANDROID || APP-IOS || APP-HARMONY -->
		<view class="l-loading__view" ref="loadingRef" :style="spinnerStyle"></view>
		<!-- #endif -->
		<text class="l-loading__text" v-if="$slots['default'] != null || text != null" :style="textStyle">
			<slot>{{text}}</slot>
		</text>
	</view>
</template>
<script lang="uts" setup>
	/**
	 * Loading 加载指示器
	 * @description 用于表示加载中的过渡状态，支持多种动画类型和布局方式
	 * <br> 插件类型：LLoadingComponentPublicInstance 
	 * @tutorial https://ext.dcloud.net.cn/plugin?name=lime-loading
	 * 
	 * @property {string} color 加载图标颜色（默认：主题色）
	 * @property {'circular' | 'spinner' | 'failed'} type 加载状态类型
	 * @value circular  环形旋转动画（默认）
	 * @value spinner   菊花转动画
	 * @value failed    加载失败提示
	 * @property {string} text 提示文字内容
	 * @property {string} textColor 文字颜色（默认同color）
	 * @property {string} textSize 文字字号（默认：14px）
	 * @property {boolean} vertical 是否垂直排列图标和文字
	 * @property {boolean} animated 是否启用旋转动画（failed类型自动禁用）
	 * @property {string} size 图标尺寸（默认：'40px'）
	 */
	import { LoadingProps } from './type'
	// #ifdef APP
	// import {useLoading} from './useLoading'
	import {useLoading} from '@/uni_modules/lime-loading'
	// #endif
	const name = 'l-loading'
	const props = withDefaults(defineProps<LoadingProps>(), {
		// #ifdef APP
		size: '40rpx',
		// #endif
		type: 'circular',
		animated: true,
		vertical: false,
	})
	
	
	const classes = computed<Map<string,any>>(():Map<string,any> => {
		const cls = new Map<string,any>()
		cls.set(name + '--' + props.type, true)
		if (props.vertical) {
			cls.set('is-vertical', props.vertical)
		} else {
			cls.set('is-horizontal', !props.vertical)
		}
		return cls
	})
	
	const spinnerStyle = computed<Map<string,any>>(():Map<string,any> => {
		const style = new Map<string,any>()
		style.set('width', props.size)
		style.set('height', props.size)
		// #ifndef APP
		style.set('color', props.color)
		style.set('--l-play-state', props.animated ? 'running' : 'paused')
		// #endif
		return style
	})
	
	const textStyle = computed<Map<string,any>>(():Map<string,any> => {
		const style = new Map<string,any>()
		if (props.textColor != null) {
			style.set('color', props.textColor!)
		}
		if (props.textSize != null) {
			style.set('font-size', props.textSize!)
		}
		return style
	})
	// #ifdef APP
	const loadingRef = ref<UniElement|null>(null)
	// const {state, color} =  useLoading(loadingRef, props.type, props.color, 1)
	const loading = useLoading(loadingRef)
	loading.type = props.type;
	if(props.animated){
		loading.play()
	}
	
	// state.value = true
	watchEffect(()=>{
		if(props.color != null){
			loading.color = (props.color ?? loadingRef.value?.style.getPropertyValue('border-left-color') ?? '#1677ff') as string;
		}
		if(props.animated){
			loading.play()
		} else {
			loading.pause()
		}
	})
	// #endif
	
</script>

<style lang="scss">
	@import './index-u.scss';
</style>