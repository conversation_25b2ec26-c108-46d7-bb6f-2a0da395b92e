{"version": 3, "sources": ["components/uc/u-from-item/u-from-item.uvue"], "names": [], "mappings": "AAkBI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnD,CAAC,CAAC,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;AAHsB;AACzB,CAAC,CAAC,CAAC,CAEC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAmBZ,CAAC;AACP;AACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5G,CAAC,CAAC;AACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,sDAAC,CAAC;AACvD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,sDAAC,CAAC,CAAC;AACrE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACX,CAAC,CAAC,CAAC,CAAC;AACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5C,CAAC,CAAC,CAAC;AACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7B,CAAC,CAAC,CAAC;AACH,CAAC,QAAY,CAAC,CAAC;AACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACR,CAAC,CAAC,CAAC;;;SAnED,mBAaO;IAbD,KAAK,EAAC,mBAAmB;IAAE,KAAK,kBAAG,gBAAW;;IAElD,mBAUO;MAVD,KAAK,kBAAC,eAAe,EAAS,4DAAsC;;MAExE,mBAEO,mBAFD,KAAK,EAAC,iBAAiB;QAC3B,WAAa;;aAG0B,cAAS,KAAK,cAAO;UAA9D,mBAGO;;YAHD,KAAK,EAAC,sBAAsB;;YAChC,mBAAiC,mBAA3B,KAAK,EAAC,YAAY,KAAC,GAAC;YAC1B,mBAAiD,mBAA3C,KAAK,EAAC,YAAY,qBAAI,kBAAW", "sourcesContent": ["<template>\n  <view class=\"form-item-wrapper\" :style=\"[customStyle]\">\n    <!-- 输入框和错误提示在同一行 -->\n    <view class=\"form-item-row\" :class=\"{ 'has-error': showError && !isValid }\">\n      <!-- 输入框容器 -->\n      <view class=\"input-container\">\n        <slot></slot>\n      </view>\n      <!-- 错误提示 - 在输入框右侧显示 -->\n      <view class=\"error-message-inline\" v-if=\"showError && !isValid\">\n        <view class=\"error-icon\">!</view>\n        <text class=\"error-text\">{{ hintMessage }}</text>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script lang=\"uts\" setup>\n    import { FormItemData,FormItemRule,FormItemVerifyResult } from \"../types\"\n    import { useVerify,findParent } from \"../utils\"\n    const isValid = ref(true)\n    const parentLabelName = \"u-form\"\n\t  const hintMessage = ref('\\u3000')\n    const instance = getCurrentInstance()!;\n    const props = defineProps({\n        customStyle: {\n          type: Object as PropType<UTSJSONObject>,\n          default: {} as UTSJSONObject as UTSJSONObject\n        },\n        field: {\n            type: String,\n            default: ''\n        },\n        rule: {\n          type: Object as PropType<FormItemRule>,\n          default: (): FormItemRule =>{\n            return {} as FormItemRule\n          }\n        },\n        showError:{\n          type:Boolean,\n          default:true\n        }\n    });\n\n\tfunction verify(value : any, callback : (res : FormItemVerifyResult) => void){\n\t\treturn useVerify(isValid, hintMessage, props.field as string, props.rule as FormItemRule, value, callback)\n\t}\n\tfunction pushFormItemFieldToForm(){\n\t\t\tconst that = instance.proxy!;\n\t\t\t//找到父组件form\n\t\t\tconst parent = findParent(that, [parentLabelName]);\r\n\t\t\tconsole.log(\"==that==:\",that.$parent?.$options.name)\n\t\t\tif (parent == null) {\n\t\t\t\tconsole.error('error:','u-form-item must be used inside u-form');\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tconst item = {\n\t\t\t\tfield: props.field as string,\n\t\t\t\tinstance: that\n\t\t\t} as FormItemData;\n\t\t\tparent.$callMethod('pushFielditem', item)\n\t}\r\n\tonMounted(()=>{\r\n\t\tpushFormItemFieldToForm();\r\n\t})\n\tdefineExpose({\n\t\tverify\n\t})\n</script>\n\n<style lang=\"scss\" scoped>\n/* 表单项包装器 */\n.form-item-wrapper {\n  margin-bottom: 24rpx;\n  width: 100%;\n}\n\n/* 表单项行 - 输入框和错误提示在同一行 */\n.form-item-row {\n  display: flex;\n  align-items: center;\n  width: 100%;\n  gap: 16rpx; /* 输入框和错误提示之间的间距 */\n}\n\n/* 输入框容器 */\n.input-container {\n  flex: 1;\n  min-width: 0; /* 防止flex子项溢出 */\n}\n\n/* 错误状态样式 */\n.has-error {\n  /* 当表单项有错误时，影响内部的输入框样式 */\n  ::v-deep .fui-input__wrap {\n    border-color: #ff4d4f !important;\n  }\n\n  ::v-deep .fui-input__border {\n    border-color: #ff4d4f !important;\n  }\n\n  ::v-deep .fui-input__border-color {\n    border-color: #ff4d4f !important;\n  }\n\n  ::v-deep .fui-input__self {\n    caret-color: #ff4d4f !important;\n  }\n}\n\n/* 内联错误提示样式 */\n.error-message-inline {\n  display: flex;\n  align-items: center;\n  flex-shrink: 0; /* 防止错误提示被压缩 */\n  max-width: 240rpx; /* 限制最大宽度，防止过长 */\n  padding: 8rpx 12rpx;\n  background-color: rgba(255, 77, 79, 0.08);\n  border-radius: 20rpx;\n  border: 1px solid rgba(255, 77, 79, 0.2);\n\n  /* 添加淡入动画 */\n  animation: errorSlideIn 0.3s ease-out;\n}\n\n/* 错误图标 */\n.error-icon {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  width: 28rpx;\n  height: 28rpx;\n  background-color: #ff4d4f;\n  color: white;\n  border-radius: 50%;\n  font-size: 18rpx;\n  font-weight: bold;\n  margin-right: 8rpx;\n  flex-shrink: 0;\n}\n\n/* 错误文本 */\n.error-text {\n  color: #ff4d4f;\n  font-size: 22rpx;\n  line-height: 1.3;\n  font-weight: 500;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n/* 错误提示动画 - 从右侧滑入 */\n@keyframes errorSlideIn {\n  0% {\n    opacity: 0;\n    transform: translateX(20rpx);\n  }\n  100% {\n    opacity: 1;\n    transform: translateX(0);\n  }\n}\n\n/* 响应式设计 */\n@media (max-width: 575px) {\n  .form-item-wrapper {\n    margin-bottom: 16rpx;\n  }\n\n  .form-item-row {\n    gap: 12rpx;\n  }\n\n  .error-message-inline {\n    max-width: 180rpx;\n    padding: 6rpx 10rpx;\n  }\n\n  .error-text {\n    font-size: 20rpx;\n  }\n\n  .error-icon {\n    width: 24rpx;\n    height: 24rpx;\n    font-size: 16rpx;\n    margin-right: 6rpx;\n  }\n}\n\n/* 超小屏幕适配 */\n@media (max-width: 400px) {\n  .form-item-row {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 8rpx;\n  }\n\n  .error-message-inline {\n    max-width: 100%;\n    align-self: flex-start;\n  }\n}\n</style>"]}