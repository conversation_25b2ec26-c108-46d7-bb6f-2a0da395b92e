import { FormItemData,FormItemRule,FormItemVerifyResult } from "../types"
    import { useVerify,findParent } from "../utils"
    
const __sfc__ = defineComponent({
  __name: 'u-from-item',
  props: {
        customStyle: {
          type: Object as PropType<UTSJSONObject>,
          default: {} as UTSJSONObject as UTSJSONObject
        },
        field: {
            type: String,
            default: ''
        },
        rule: {
          type: Object as PropType<FormItemRule>,
          default: (): FormItemRule =>{
            return {} as FormItemRule
          }
        },
        showError:{
          type:Boolean,
          default:true
        }
    },
  setup(__props, { expose: __expose }: SetupContext): any | null {
const __ins = getCurrentInstance()!;
const _ctx = __ins.proxy as InstanceType<typeof __sfc__>;
const _cache = __ins.renderCache;

    const isValid = ref(true)
    const parentLabelName = "u-form"
	  const hintMessage = ref('\u3000')
    const instance = getCurrentInstance()!;
    const props = __props;

	function verify(value : any, callback : (res : FormItemVerifyResult) => void){
		return useVerify(isValid, hintMessage, props.field as string, props.rule as FormItemRule, value, callback)
	}
	function pushFormItemFieldToForm(){
			const that = instance.proxy!;
			//找到父组件form
			const parent = findParent(that, [parentLabelName]);
			console.log("==that==:",that.$parent?.$options.name, " at components/uc/u-from-item/u-from-item.uvue:53")
			if (parent == null) {
				console.error('error:','u-form-item must be used inside u-form', " at components/uc/u-from-item/u-from-item.uvue:55");
				return;
			}
			const item = {
				field: props.field as string,
				instance: that
			} as FormItemData;
			parent.$callMethod('pushFielditem', item)
	}
	onMounted(()=>{
		pushFormItemFieldToForm();
	})
	__expose({
		verify
	})

return (): any | null => {

  return createElementVNode("view", utsMapOf({
    class: "form-item-wrapper",
    style: normalizeStyle([_ctx.customStyle])
  }), [
    createElementVNode("view", utsMapOf({
      class: normalizeClass(["form-item-row", utsMapOf({ 'has-error': _ctx.showError && !unref(isValid) })])
    }), [
      createElementVNode("view", utsMapOf({ class: "input-container" }), [
        renderSlot(_ctx.$slots, "default")
      ]),
      isTrue(_ctx.showError && !unref(isValid))
        ? createElementVNode("view", utsMapOf({
            key: 0,
            class: "error-message-inline"
          }), [
            createElementVNode("view", utsMapOf({ class: "error-icon" }), "!"),
            createElementVNode("text", utsMapOf({ class: "error-text" }), toDisplayString(unref(hintMessage)), 1 /* TEXT */)
          ])
        : createCommentVNode("v-if", true)
    ], 2 /* CLASS */)
  ], 4 /* STYLE */)
}
}

})
export default __sfc__
const GenComponentsUcUFromItemUFromItemStyles = [utsMapOf([["form-item-wrapper", padStyleMapOf(utsMapOf([["marginBottom", "24rpx"], ["width", "100%"]]))], ["form-item-row", padStyleMapOf(utsMapOf([["display", "flex"], ["alignItems", "center"], ["width", "100%"], ["gap", "16rpx"]]))], ["input-container", padStyleMapOf(utsMapOf([["flex", 1], ["minWidth", 0]]))], ["error-message-inline", padStyleMapOf(utsMapOf([["display", "flex"], ["alignItems", "center"], ["flexShrink", 0], ["maxWidth", "240rpx"], ["paddingTop", "8rpx"], ["paddingRight", "12rpx"], ["paddingBottom", "8rpx"], ["paddingLeft", "12rpx"], ["backgroundColor", "rgba(255,77,79,0.08)"], ["borderTopLeftRadius", "20rpx"], ["borderTopRightRadius", "20rpx"], ["borderBottomRightRadius", "20rpx"], ["borderBottomLeftRadius", "20rpx"], ["borderTopWidth", 1], ["borderRightWidth", 1], ["borderBottomWidth", 1], ["borderLeftWidth", 1], ["borderTopStyle", "solid"], ["borderRightStyle", "solid"], ["borderBottomStyle", "solid"], ["borderLeftStyle", "solid"], ["borderTopColor", "rgba(255,77,79,0.2)"], ["borderRightColor", "rgba(255,77,79,0.2)"], ["borderBottomColor", "rgba(255,77,79,0.2)"], ["borderLeftColor", "rgba(255,77,79,0.2)"], ["animation", "errorSlideIn 0.3s ease-out"]]))], ["error-icon", padStyleMapOf(utsMapOf([["display", "flex"], ["justifyContent", "center"], ["alignItems", "center"], ["width", "28rpx"], ["height", "28rpx"], ["backgroundColor", "#ff4d4f"], ["color", "#FFFFFF"], ["fontSize", "18rpx"], ["fontWeight", "bold"], ["marginRight", "8rpx"], ["flexShrink", 0]]))], ["error-text", padStyleMapOf(utsMapOf([["color", "#ff4d4f"], ["fontSize", "22rpx"], ["lineHeight", 1.3], ["whiteSpace", "nowrap"], ["overflow", "hidden"], ["textOverflow", "ellipsis"]]))], ["@FONT-FACE", utsMapOf([["0", utsMapOf([])], ["1", utsMapOf([["form-item-wrapper", utsMapOf([["", utsMapOf([["marginBottom", "16rpx"]])]])], ["form-item-row", utsMapOf([["", utsMapOf([["gap", "12rpx"]])]])], ["error-message-inline", utsMapOf([["", utsMapOf([["maxWidth", "180rpx"], ["paddingTop", "6rpx"], ["paddingRight", "10rpx"], ["paddingBottom", "6rpx"], ["paddingLeft", "10rpx"]])]])], ["error-text", utsMapOf([["", utsMapOf([["fontSize", "20rpx"]])]])], ["error-icon", utsMapOf([["", utsMapOf([["width", "24rpx"], ["height", "24rpx"], ["fontSize", "16rpx"], ["marginRight", "6rpx"]])]])]])], ["2", utsMapOf([["form-item-row", utsMapOf([["", utsMapOf([["flexDirection", "column"], ["alignItems", "flex-start"], ["gap", "8rpx"]])]])], ["error-message-inline", utsMapOf([["", utsMapOf([["alignSelf", "flex-start"]])]])]])]])]])]
