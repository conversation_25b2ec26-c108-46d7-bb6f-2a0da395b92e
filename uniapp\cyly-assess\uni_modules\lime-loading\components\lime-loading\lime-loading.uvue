<template>
	<view class="demo-block">
		<text class="demo-block__title-text ultra">加载</text>
		<text class="demo-block__desc-text">用于表示加载中的过渡状态。</text>
		<view class="demo-block__body">

			<view class="demo-block card">
				<text class="demo-block__title-text large">基础</text>
				<view class="demo-block__body">
					<view class="row">
						<l-loading></l-loading>
						<l-loading type="spinner" :color="color"></l-loading>
					</view>
				</view>
			</view>

			<view class="demo-block card">
				<text class="demo-block__title-text large">文字</text>
				<view class="demo-block__body">
					<l-loading>加载中…</l-loading>
					<l-loading text="加载中…"></l-loading>
				</view>
			</view>

			<view class="demo-block card">
				<text class="demo-block__title-text large">垂直</text>
				<view class="demo-block__body">
					<l-loading :vertical="true">加载中…</l-loading>
					<l-loading :vertical="true" text="加载中…"></l-loading>
				</view>
			</view>

			<view class="demo-block card">
				<text class="demo-block__title-text large">尺寸</text>
				<view class="demo-block__body">
					<view class="row">
						<l-loading size="40rpx"></l-loading>
						<l-loading size="60rpx"></l-loading>
					</view>
				</view>
			</view>

			<view class="demo-block card">
				<text class="demo-block__title-text large">颜色</text>
				<view class="demo-block__body">
					<view class="row">
						<l-loading color="red"></l-loading>
						<l-loading color="red" type="spinner"></l-loading>
					</view>
				</view>
			</view>

		</view>
	</view>
</template>
<script setup lang="uts">
	const color = ref('')
	
	
	setTimeout(()=>{
		color.value = '#34c471'
	},2000)
</script>
<style lang="scss">
	.row {
		display: flex;
		flex-direction: row;
		/* #ifndef UNI-APP-X */
		gap: 50rpx;
		/* #endif */
	}

	.demo-block {
		margin: 32px 10px 0;
		overflow: visible;
		&.card{
			padding: 30rpx;
			background-color: white;
			margin-bottom: 20rpx !important;
		}
		&__title {
			margin: 0;
			margin-top: 8px;

			&-text {
				color: rgba(0, 0, 0, 0.6);
				font-weight: 400;
				font-size: 14px;
				line-height: 16px;

				&.large {
					color: rgba(0, 0, 0, 0.9);
					font-size: 18px;
					font-weight: 700;
					line-height: 26px;
				}

				&.ultra {
					color: rgba(0, 0, 0, 0.9);
					font-size: 24px;
					font-weight: 700;
					line-height: 32px;
				}
			}
		}

		&__desc-text {
			color: rgba(0, 0, 0, 0.6);
			margin: 8px 16px 0 0;
			font-size: 14px;
			line-height: 22px;
		}

		&__body {
			margin: 16px 0;
			overflow: visible;

			.demo-block {
				// margin-top: 0px;
				margin: 0;
			}
		}
	}
</style>