<template>
	<view class="u-form" :style="[customStyle]">
		<slot></slot>
	</view>
</template>

<script lang="uts" setup>
	import { FormItemData, FormItemVerifyResult, FormValidResult,FormValidResultItem } from "../types";
	const props = defineProps({
		customStyle: {
			type: Object as PropType<UTSJSONObject>,
			default: {} as UTSJSONObject as UTSJSONObject
		},
		triggerChange: {
			type: Boolean,
			default: false
		}
	});
	const fieldItems = ref<FormItemData[]>([])

	const emit = defineEmits(['submited', 'change', 'focus'])

	const model = defineModel({
		type: Object as PropType<UTSJSONObject>,
		default: {} as UTSJSONObject
	})
	function onSubmit(callback : FormValidResult) {
		valid({
			success: () => {
				if (callback.success != null) {
					emit('submited')
					callback.success!()
				}
			},
			fail: (failResults : FormItemVerifyResult[]) => {
				if (callback.fail != null) {
					callback.fail!(failResults)
				}
			}
		} as FormValidResult)
	}
	function pushFielditem(fieldItem : FormItemData) {
    if(fieldItems.value.find((item : FormItemData) : boolean => item.field == fieldItem.field) != null){
      return;
    }
    console.log(fieldItem);
		fieldItems.value.push(fieldItem);
    console.log("fieldItems:",fieldItems);

	}
	function valid(callback : FormValidResult) {
    console.log("verifyverifyverify");
		let allow = true
		let verifyRes = [] as FormItemVerifyResult[]
		fieldItems.value.forEach((item : FormItemData) => {
			let field = item.field
			let value = (model.value as UTSJSONObject)[field]
			if (value != null) {
				let _item = fieldItems.value.find((it : FormItemData) : boolean => it.field == field)
				if (_item != null) {
					item.instance.$callMethod('verify', value, (res : FormItemVerifyResult) => {
						if (!res.valid) {
							allow = false
							verifyRes.push(res)
						}
					})
				}
			} else {
				allow = false
			}
		})
		if (allow) {
			if (callback.success != null) {
				callback.success!()
			}
		} else {
			if (callback.fail != null) {
				callback.fail!(verifyRes)
			}
		}
	}
	function validItem(field : string, callback : FormValidResultItem) {
		fieldItems.value.forEach((item : FormItemData) => {
			let value = (model.value as UTSJSONObject)[field]
			if (value != null) {
				let _item = fieldItems.value.find((it : FormItemData) : boolean => it.field == field)
        console.log("_item:",_item);
				if (_item != null) {
					item.instance.$callMethod('verify', value, (res : FormItemVerifyResult) => {
						if (!res.valid) {
							callback.fail!(res)
              return;
						}
						callback.success!()
					})
				}
			} else {
				callback.fail!()
			}
		})
	}
	watch(model, (value : UTSJSONObject) => {
		emit('change', value)
		//TODO 测试

		if (props.triggerChange) {
			valid({
				success() {
					console.log("?");
				},
				fail(res) {
					console.log("???", res);
				}
			})
		}
	}, {
		deep: true
	})
	defineExpose({
		onSubmit,
		pushFielditem,
    validItem
	});
</script>