<template>
	<view class="u-form" :style="[customStyle]">
		<!-- Toast 提示框 -->
		<uToast ref="toastRef" :visible="toastVisible" :message="toastMessage" :type="toastType" :icon="toastIcon"
			:duration="toastDuration" :position="toastPosition" :top="toastTop" @close="onToastClose" />
		<slot></slot>
	</view>
</template>

<script lang="uts" setup>
	import { FormItemData, FormItemVerifyResult, FormValidResult, FormValidResultItem, ToastType, ToastPosition } from "../types";
	import uToast from "../u-toast/u-toast";
	import { ComponentPublicInstance } from 'vue'

	defineOptions({
		name: 'u-form'
	})
	const props = defineProps({
		customStyle: {
			type: Object as PropType<UTSJSONObject>,
			default: {} as UTSJSONObject as UTSJSONObject
		},
		triggerChange: {
			type: Boolean,
			default: false
		},
		// Toast 相关配置
		showToast: {
			type: Boolean,
			default: true
		},
		toastPosition: {
			type: String as PropType<ToastPosition>,
			default: 'top' as ToastPosition
		},
		toastTop: {
			type: Number,
			default: 100
		},
		toastDuration: {
			type: Number,
			default: 3000
		}
	});
	const fieldItems = ref<FormItemData[]>([])

	const emit = defineEmits(['submited', 'change', 'focus'])

	const model = defineModel({
		type: Object as PropType<UTSJSONObject>,
		default: {} as UTSJSONObject
	})

	// Toast 相关数据
	const toastRef = ref<ComponentPublicInstance | null>(null)
	const toastVisible = ref(false)
	const toastMessage = ref('')
	const toastType = ref<ToastType>('error')
	const toastIcon = ref('✕')

	// Toast 方法
	function showErrorToast(message: string) {
		if (!props.showToast) return

		toastMessage.value = message
		toastType.value = 'error'
		toastIcon.value = '✕'  // 使用文字图标
		toastVisible.value = true
	}

	function onToastClose() {
		toastVisible.value = false
	}

	// 先定义 valid 函数
	function valid(callback : FormValidResult) {
		let allow = true
		let verifyRes = [] as FormItemVerifyResult[]
		fieldItems.value.forEach((item : FormItemData) => {
			let field = item.field
			let value = (model.value as UTSJSONObject)[field]
			if (value != null) {
				let _item = fieldItems.value.find((it : FormItemData) : boolean => it.field == field)
				if (_item != null) {
					item.instance.$callMethod('verify', value, (res : FormItemVerifyResult) => {
						if (!res.valid) {
							allow = false
							verifyRes.push(res)
						}
					})
				}
			} else {
				allow = false
			}
		})
		if (allow) {
			if (callback.success != null) {
				callback.success!()
			}
		} else {
			// 显示第一个验证失败的错误信息
			if (verifyRes.length > 0) {
				const errorMessage = verifyRes[0].message
				showErrorToast(errorMessage != null ? errorMessage : '验证失败')
			}
			if (callback.fail != null) {
				callback.fail!(verifyRes)
			}
		}
	}

	function onSubmit(callback : FormValidResult) {
		valid({
			success: () => {
				if (callback.success != null) {
					emit('submited')
					callback.success!()
				}
			},
			fail: (failResults : FormItemVerifyResult[]) => {
				if (callback.fail != null) {
					callback.fail!(failResults)
				}
			}
		} as FormValidResult)
	}

	function pushFielditem(fieldItem : FormItemData) {
		if (fieldItems.value.find((item : FormItemData) : boolean => item.field == fieldItem.field) != null) {
			return;
		}
		console.log(fieldItem);
		fieldItems.value.push(fieldItem);
		console.log("pushFielditem-fieldItems:", fieldItems.value.map(v=>v.field));
	}
	function validItem(field : string, callback : FormValidResultItem) {
		const fieldItem = fieldItems.value.find(item => item.field == field);
		console.log("===fieldItems===:", fieldItems);
		console.log("model:", model);
		const value = (model.value as UTSObject)[field]
		if (fieldItem != null) {
			fieldItem.instance.$callMethod('verify', value, (res : FormItemVerifyResult) => {
				if (!res.valid) {
					// 显示验证失败的错误信息
					const errorMessage = res.message
					showErrorToast(errorMessage != null ? errorMessage : '验证失败')
					if (callback.fail != null) {
						callback.fail!(res)
					}
					return;
				}
				if (callback.success != null) {
					callback.success!()
				}
			})
		} else {
			// 如果找不到字段项，调用失败回调
			const errorMsg = `字段 ${field} 不存在`
			showErrorToast(errorMsg)
			if (callback.fail != null) {
				callback.fail!({
					valid: false,
					message: errorMsg,
					field: field
				} as FormItemVerifyResult)
			}
		}
	}
	watch(model, (value : UTSJSONObject) => {
		emit('change', value)
		//TODO 测试

		if (props.triggerChange) {
			valid({
				success() {
					console.log("?");
				},
				fail(res) {
					console.log("???", res);
				}
			})
		}
	}, {
		deep: true
	})
	defineExpose({
		onSubmit,
		pushFielditem,
		validItem,
		valid
	});
</script>