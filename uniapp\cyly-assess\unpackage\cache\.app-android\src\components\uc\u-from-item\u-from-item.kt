@file:Suppress("UNCHECKED_CAST", "USELESS_CAST", "INAPPLICABLE_JVM_NAME", "UNUSED_ANONYMOUS_PARAMETER", "NAME_SHADOWING", "UNNECESSARY_NOT_NULL_ASSERTION")
package uni.UNI4AABA03
import io.dcloud.uniapp.*
import io.dcloud.uniapp.extapi.*
import io.dcloud.uniapp.framework.*
import io.dcloud.uniapp.runtime.*
import io.dcloud.uniapp.vue.*
import io.dcloud.uniapp.vue.shared.*
import io.dcloud.unicloud.*
import io.dcloud.uts.*
import io.dcloud.uts.Map
import io.dcloud.uts.Set
import io.dcloud.uts.UTSAndroid
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
open class GenComponentsUcUFromItemUFromItem : VueComponent {
    constructor(__ins: ComponentInternalInstance) : super(__ins) {}
    open var customStyle: UTSJSONObject by `$props`
    open var field: String by `$props`
    open var rule: FormItemRule by `$props`
    open var showError: Boolean by `$props`
    open var verify: (value: Any, callback: (res: FormItemVerifyResult) -> Unit) -> Unit
        get() {
            return unref(this.`$exposed`["verify"]) as (value: Any, callback: (res: FormItemVerifyResult) -> Unit) -> Unit
        }
        set(value) {
            setRefValue(this.`$exposed`, "verify", value)
        }
    companion object {
        @Suppress("UNUSED_PARAMETER", "UNUSED_VARIABLE")
        var setup: (__props: GenComponentsUcUFromItemUFromItem, _arg1: SetupContext) -> Any? = fun(__props, ref1): Any? {
            var __expose = ref1.expose
            val __ins = getCurrentInstance()!!
            val _ctx = __ins.proxy as GenComponentsUcUFromItemUFromItem
            val _cache = __ins.renderCache
            val isValid = ref(true)
            val parentLabelName = "u-form"
            val hintMessage = ref("\u3000")
            val instance = getCurrentInstance()!!
            val props = __props
            fun gen_verify_fn(value: Any, callback: (res: FormItemVerifyResult) -> Unit) {
                return useVerify(isValid, hintMessage, props.field as String, props.rule as FormItemRule, value, callback)
            }
            val verify = ::gen_verify_fn
            fun gen_pushFormItemFieldToForm_fn() {
                val that = instance.proxy!!
                val parent = findParent(that, utsArrayOf(
                    parentLabelName
                ))
                console.log("==that==:", that.`$parent`?.`$options`?.name, " at components/uc/u-from-item/u-from-item.uvue:53")
                if (parent == null) {
                    console.error("error:", "u-form-item must be used inside u-form", " at components/uc/u-from-item/u-from-item.uvue:55")
                    return
                }
                val item = FormItemData(field = props.field as String, instance = that)
                parent.`$callMethod`("pushFielditem", item)
            }
            val pushFormItemFieldToForm = ::gen_pushFormItemFieldToForm_fn
            onMounted(fun(){
                pushFormItemFieldToForm()
            }
            )
            __expose(utsMapOf("verify" to verify))
            return fun(): Any? {
                return createElementVNode("view", utsMapOf("class" to "form-item-wrapper", "style" to normalizeStyle(utsArrayOf(
                    _ctx.customStyle
                ))), utsArrayOf(
                    createElementVNode("view", utsMapOf("class" to normalizeClass(utsArrayOf(
                        "form-item-row",
                        utsMapOf("has-error" to (_ctx.showError && !unref(isValid)))
                    ))), utsArrayOf(
                        createElementVNode("view", utsMapOf("class" to "input-container"), utsArrayOf(
                            renderSlot(_ctx.`$slots`, "default")
                        )),
                        if (isTrue(_ctx.showError && !unref(isValid))) {
                            createElementVNode("view", utsMapOf("key" to 0, "class" to "error-message-inline"), utsArrayOf(
                                createElementVNode("view", utsMapOf("class" to "error-icon"), "!"),
                                createElementVNode("text", utsMapOf("class" to "error-text"), toDisplayString(unref(hintMessage)), 1)
                            ))
                        } else {
                            createCommentVNode("v-if", true)
                        }
                    ), 2)
                ), 4)
            }
        }
        val styles: Map<String, Map<String, Map<String, Any>>> by lazy {
            normalizeCssStyles(utsArrayOf(
                styles0
            ))
        }
        val styles0: Map<String, Map<String, Map<String, Any>>>
            get() {
                return utsMapOf("form-item-wrapper" to padStyleMapOf(utsMapOf("marginBottom" to "24rpx", "width" to "100%")), "form-item-row" to padStyleMapOf(utsMapOf("display" to "flex", "alignItems" to "center", "width" to "100%", "gap" to "16rpx")), "input-container" to padStyleMapOf(utsMapOf("flex" to 1, "minWidth" to 0)), "error-message-inline" to padStyleMapOf(utsMapOf("display" to "flex", "alignItems" to "center", "flexShrink" to 0, "maxWidth" to "240rpx", "paddingTop" to "8rpx", "paddingRight" to "12rpx", "paddingBottom" to "8rpx", "paddingLeft" to "12rpx", "backgroundColor" to "rgba(255,77,79,0.08)", "borderTopLeftRadius" to "20rpx", "borderTopRightRadius" to "20rpx", "borderBottomRightRadius" to "20rpx", "borderBottomLeftRadius" to "20rpx", "borderTopWidth" to 1, "borderRightWidth" to 1, "borderBottomWidth" to 1, "borderLeftWidth" to 1, "borderTopStyle" to "solid", "borderRightStyle" to "solid", "borderBottomStyle" to "solid", "borderLeftStyle" to "solid", "borderTopColor" to "rgba(255,77,79,0.2)", "borderRightColor" to "rgba(255,77,79,0.2)", "borderBottomColor" to "rgba(255,77,79,0.2)", "borderLeftColor" to "rgba(255,77,79,0.2)", "animation" to "errorSlideIn 0.3s ease-out")), "error-icon" to padStyleMapOf(utsMapOf("display" to "flex", "justifyContent" to "center", "alignItems" to "center", "width" to "28rpx", "height" to "28rpx", "backgroundColor" to "#ff4d4f", "color" to "#FFFFFF", "fontSize" to "18rpx", "fontWeight" to "bold", "marginRight" to "8rpx", "flexShrink" to 0)), "error-text" to padStyleMapOf(utsMapOf("color" to "#ff4d4f", "fontSize" to "22rpx", "lineHeight" to 1.3, "whiteSpace" to "nowrap", "overflow" to "hidden", "textOverflow" to "ellipsis")), "@FONT-FACE" to utsMapOf("0" to utsMapOf(), "1" to utsMapOf("form-item-wrapper" to utsMapOf("" to utsMapOf("marginBottom" to "16rpx")), "form-item-row" to utsMapOf("" to utsMapOf("gap" to "12rpx")), "error-message-inline" to utsMapOf("" to utsMapOf("maxWidth" to "180rpx", "paddingTop" to "6rpx", "paddingRight" to "10rpx", "paddingBottom" to "6rpx", "paddingLeft" to "10rpx")), "error-text" to utsMapOf("" to utsMapOf("fontSize" to "20rpx")), "error-icon" to utsMapOf("" to utsMapOf("width" to "24rpx", "height" to "24rpx", "fontSize" to "16rpx", "marginRight" to "6rpx"))), "2" to utsMapOf("form-item-row" to utsMapOf("" to utsMapOf("flexDirection" to "column", "alignItems" to "flex-start", "gap" to "8rpx")), "error-message-inline" to utsMapOf("" to utsMapOf("alignSelf" to "flex-start")))))
            }
        var inheritAttrs = true
        var inject: Map<String, Map<String, Any?>> = utsMapOf()
        var emits: Map<String, Any?> = utsMapOf()
        var props = normalizePropsOptions(utsMapOf("customStyle" to utsMapOf("type" to "Object", "default" to UTSJSONObject() as UTSJSONObject), "field" to utsMapOf("type" to "String", "default" to ""), "rule" to utsMapOf("type" to "Object", "default" to fun(): FormItemRule {
            return FormItemRule()
        }
        ), "showError" to utsMapOf("type" to "Boolean", "default" to true)))
        var propsNeedCastKeys = utsArrayOf(
            "customStyle",
            "field",
            "rule",
            "showError"
        )
        var components: Map<String, CreateVueComponent> = utsMapOf()
    }
}
