@file:Suppress("UNCHECKED_CAST", "USELESS_CAST", "INAPPLICABLE_JVM_NAME", "UNUSED_ANONYMOUS_PARAMETER", "NAME_SHADOWING", "UNNECESSARY_NOT_NULL_ASSERTION")
package uni.UNI4AABA03
import io.dcloud.uniapp.*
import io.dcloud.uniapp.extapi.*
import io.dcloud.uniapp.framework.*
import io.dcloud.uniapp.runtime.*
import io.dcloud.uniapp.vue.*
import io.dcloud.uniapp.vue.shared.*
import io.dcloud.unicloud.*
import io.dcloud.uts.*
import io.dcloud.uts.Map
import io.dcloud.uts.Set
import io.dcloud.uts.UTSAndroid
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
open class GenComponentsUcUFromItemUFromItem : VueComponent {
    constructor(__ins: ComponentInternalInstance) : super(__ins) {}
    open var customStyle: UTSJSONObject by `$props`
    open var field: String by `$props`
    open var rule: FormItemRule by `$props`
    open var showError: Boolean by `$props`
    open var marginTop: Number by `$props`
    open var verify: (value: Any, callback: (res: FormItemVerifyResult) -> Unit) -> Unit
        get() {
            return unref(this.`$exposed`["verify"]) as (value: Any, callback: (res: FormItemVerifyResult) -> Unit) -> Unit
        }
        set(value) {
            setRefValue(this.`$exposed`, "verify", value)
        }
    companion object {
        @Suppress("UNUSED_PARAMETER", "UNUSED_VARIABLE")
        var setup: (__props: GenComponentsUcUFromItemUFromItem, _arg1: SetupContext) -> Any? = fun(__props, ref1): Any? {
            var __expose = ref1.expose
            val __ins = getCurrentInstance()!!
            val _ctx = __ins.proxy as GenComponentsUcUFromItemUFromItem
            val _cache = __ins.renderCache
            val isValid = ref(true)
            val parentLabelName = "u-form"
            val hintMessage = ref("\u3000")
            val instance = getCurrentInstance()!!
            val props = __props
            val getStyle = computed(fun(): Map<String, String> {
                console.log(props.marginTop, "props.marginTop", " at components/uc/u-from-item/u-from-item.uvue:48")
                val mp: Map<String, String> = Map()
                console.log(props.marginTop, "props.marginTop", " at components/uc/u-from-item/u-from-item.uvue:50")
                mp.set("margin-top", "" + props.marginTop + "rpx")
                return mp
            }
            )
            fun gen_verify_fn(value: Any, callback: (res: FormItemVerifyResult) -> Unit) {
                return useVerify(isValid, hintMessage, props.field as String, props.rule as FormItemRule, value, callback)
            }
            val verify = ::gen_verify_fn
            fun gen_pushFormItemFieldToForm_fn() {
                val that = instance.proxy!!
                val parent = findParent(that, utsArrayOf(
                    parentLabelName
                ))
                console.log("==that==:", that.`$parent`?.`$options`?.name, " at components/uc/u-from-item/u-from-item.uvue:61")
                if (parent == null) {
                    console.error("error:", "u-form-item must be used inside u-form", " at components/uc/u-from-item/u-from-item.uvue:63")
                    return
                }
                val item = FormItemData(field = props.field as String, instance = that)
                parent.`$callMethod`("pushFielditem", item)
            }
            val pushFormItemFieldToForm = ::gen_pushFormItemFieldToForm_fn
            onMounted(fun(){
                pushFormItemFieldToForm()
            }
            )
            __expose(utsMapOf("verify" to verify))
            return fun(): Any? {
                return createElementVNode("view", utsMapOf("class" to "form-item-wrapper", "style" to normalizeStyle(unref(getStyle))), utsArrayOf(
                    createElementVNode("view", utsMapOf("class" to normalizeClass(utsArrayOf(
                        "input-container",
                        utsMapOf("has-error" to (_ctx.showError && !unref(isValid)))
                    ))), utsArrayOf(
                        renderSlot(_ctx.`$slots`, "default")
                    ), 2),
                    if (isTrue(_ctx.showError && !unref(isValid))) {
                        createElementVNode("view", utsMapOf("key" to 0, "class" to "error-message-bottom"), utsArrayOf(
                            createElementVNode("text", utsMapOf("class" to "error-text"), toDisplayString(unref(hintMessage)), 1)
                        ))
                    } else {
                        createCommentVNode("v-if", true)
                    }
                ), 4)
            }
        }
        val styles: Map<String, Map<String, Map<String, Any>>> by lazy {
            normalizeCssStyles(utsArrayOf(
                styles0
            ))
        }
        val styles0: Map<String, Map<String, Map<String, Any>>>
            get() {
                return utsMapOf("form-item-wrapper" to padStyleMapOf(utsMapOf("marginBottom" to "24rpx", "width" to "100%")), "input-container" to padStyleMapOf(utsMapOf("width" to "100%", "borderTopWidth" to 1, "borderRightWidth" to 1, "borderBottomWidth" to 1, "borderLeftWidth" to 1, "borderTopStyle" to "solid", "borderRightStyle" to "solid", "borderBottomStyle" to "solid", "borderLeftStyle" to "solid", "borderTopColor" to "#EEEEEE", "borderRightColor" to "#EEEEEE", "borderBottomColor" to "#EEEEEE", "borderLeftColor" to "#EEEEEE", "borderTopLeftRadius" to "16rpx", "borderTopRightRadius" to "16rpx", "borderBottomRightRadius" to "16rpx", "borderBottomLeftRadius" to "16rpx", "transitionProperty" to "borderColor", "transitionDuration" to "0.3s", "transitionTimingFunction" to "ease")), "error-message-bottom" to padStyleMapOf(utsMapOf("display" to "flex", "alignItems" to "flex-start", "marginTop" to "8rpx", "paddingTop" to "12rpx", "paddingRight" to "16rpx", "paddingBottom" to "12rpx", "paddingLeft" to "16rpx", "backgroundColor" to "#fff2f0", "borderTopLeftRadius" to "8rpx", "borderTopRightRadius" to "8rpx", "borderBottomRightRadius" to "8rpx", "borderBottomLeftRadius" to "8rpx", "borderLeftWidth" to "4rpx", "borderLeftStyle" to "solid", "borderLeftColor" to "#ff4d4f", "boxShadow" to "0 2rpx 8rpx rgba(255, 77, 79, 0.1)", "animation" to "errorFadeIn 0.3s ease-out")), "has-error" to padStyleMapOf(utsMapOf("borderTopWidth" to 1, "borderRightWidth" to 1, "borderBottomWidth" to 1, "borderLeftWidth" to 1, "borderTopStyle" to "solid", "borderRightStyle" to "solid", "borderBottomStyle" to "solid", "borderLeftStyle" to "solid", "borderTopColor" to "#ff4d4f", "borderRightColor" to "#ff4d4f", "borderBottomColor" to "#ff4d4f", "borderLeftColor" to "#ff4d4f")), "error-text" to padStyleMapOf(utsMapOf("color" to "#ff4d4f", "fontSize" to "24rpx", "lineHeight" to 1.5, "flex" to 1, "wordWrap" to "break-word", "wordBreak" to "break-all")), "@FONT-FACE" to utsMapOf("0" to utsMapOf(), "1" to utsMapOf("form-item-wrapper" to utsMapOf("" to utsMapOf("marginBottom" to "16rpx")), "error-message-bottom" to utsMapOf("" to utsMapOf("paddingTop" to "10rpx", "paddingRight" to "14rpx", "paddingBottom" to "10rpx", "paddingLeft" to "14rpx", "marginTop" to "6rpx")), "error-text" to utsMapOf("" to utsMapOf("fontSize" to "22rpx")), "error-icon" to utsMapOf("" to utsMapOf("width" to "28rpx", "height" to "28rpx", "fontSize" to "18rpx", "marginRight" to "10rpx")))), "@TRANSITION" to utsMapOf("input-container" to utsMapOf("property" to "borderColor", "duration" to "0.3s", "timingFunction" to "ease")))
            }
        var inheritAttrs = true
        var inject: Map<String, Map<String, Any?>> = utsMapOf()
        var emits: Map<String, Any?> = utsMapOf()
        var props = normalizePropsOptions(utsMapOf("customStyle" to utsMapOf("type" to "Object", "default" to UTSJSONObject() as UTSJSONObject), "field" to utsMapOf("type" to "String", "default" to ""), "rule" to utsMapOf("type" to "Object", "default" to fun(): FormItemRule {
            return FormItemRule()
        }
        ), "showError" to utsMapOf("type" to "Boolean", "default" to false), "marginTop" to utsMapOf("type" to "Number", "default" to 0)))
        var propsNeedCastKeys = utsArrayOf(
            "customStyle",
            "field",
            "rule",
            "showError",
            "marginTop"
        )
        var components: Map<String, CreateVueComponent> = utsMapOf()
    }
}
