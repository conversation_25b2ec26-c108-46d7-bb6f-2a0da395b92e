@file:Suppress("UNCHECKED_CAST", "USELESS_CAST", "INAPPLICABLE_JVM_NAME", "UNUSED_ANONYMOUS_PARAMETER", "NAME_SHADOWING", "UNNECESSARY_NOT_NULL_ASSERTION")
package uni.UNI4AABA03
import io.dcloud.uniapp.*
import io.dcloud.uniapp.extapi.*
import io.dcloud.uniapp.framework.*
import io.dcloud.uniapp.runtime.*
import io.dcloud.uniapp.vue.*
import io.dcloud.uniapp.vue.shared.*
import io.dcloud.unicloud.*
import io.dcloud.uts.*
import io.dcloud.uts.Map
import io.dcloud.uts.Set
import io.dcloud.uts.UTSAndroid
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
open class GenComponentsUcUToastUToast : VueComponent {
    constructor(__ins: ComponentInternalInstance) : super(__ins) {}
    open var visible: Boolean by `$props`
    open var message: String by `$props`
    open var type: String by `$props`
    open var backgroundColor: String by `$props`
    open var textColor: String by `$props`
    open var icon: String by `$props`
    open var iconColor: String by `$props`
    open var showIcon: Boolean by `$props`
    open var duration: Number by `$props`
    open var position: String by `$props`
    open var top: Number by `$props`
    open var customStyle: UTSJSONObject by `$props`
    open var show: () -> Unit
        get() {
            return unref(this.`$exposed`["show"]) as () -> Unit
        }
        set(value) {
            setRefValue(this.`$exposed`, "show", value)
        }
    open var hide: () -> Unit
        get() {
            return unref(this.`$exposed`["hide"]) as () -> Unit
        }
        set(value) {
            setRefValue(this.`$exposed`, "hide", value)
        }
    companion object {
        @Suppress("UNUSED_PARAMETER", "UNUSED_VARIABLE")
        var setup: (__props: GenComponentsUcUToastUToast, _arg1: SetupContext) -> Any? = fun(__props, ref1): Any? {
            var __expose = ref1.expose
            val __ins = getCurrentInstance()!!
            val _ctx = __ins.proxy as GenComponentsUcUToastUToast
            val _cache = __ins.renderCache
            val props = __props
            fun emit(event: String, vararg do_not_transform_spread: Any?) {
                __ins.emit(event, *do_not_transform_spread)
            }
            val visible = ref(props.visible)
            val timer = ref<Number?>(null)
            val iconType = computed(fun(): String {
                val iconValue = getIconValue()
                if (iconValue === "") {
                    return "none"
                }
                if (iconValue.length <= 2 || UTSRegExp("^[\\u{1F600}-\\u{1F64F}]|[\\u{1F300}-\\u{1F5FF}]|[\\u{1F680}-\\u{1F6FF}]|[\\u{1F1E0}-\\u{1F1FF}]|[\\u{2600}-\\u{26FF}]|[\\u{2700}-\\u{27BF}]", "u").test(iconValue)) {
                    return "text"
                }
                return "custom"
            }
            )
            val getIconValue = fun(): String {
                if (props.icon !== "") {
                    return props.icon
                }
                when (props.type) {
                    "success" -> 
                        return "✓"
                    "error" -> 
                        return "✕"
                    "warning" -> 
                        return "⚠"
                    "info" -> 
                        return "ℹ"
                    else -> 
                        return "●"
                }
            }
            val toastClass = computed(fun(): String {
                val classes = utsArrayOf(
                    "u-toast-" + props.type
                )
                if (props.position === "top") {
                    classes.push("u-toast-top")
                }
                if (props.position === "bottom") {
                    classes.push("u-toast-bottom")
                }
                return classes.join(" ")
            }
            )
            val containerStyle = computed(fun(): UTSJSONObject {
                val style: UTSJSONObject = object : UTSJSONObject(UTSSourceMapPosition("style", "components/uc/u-toast/u-toast.uvue", 133, 11)) {
                }
                val topValue = if (props.top != null) {
                    props.top
                } else {
                    0
                }
                if (props?.position === "top") {
                    style["top"] = topValue + "rpx"
                } else if (props?.position === "bottom") {
                    style["bottom"] = topValue + "rpx"
                }
                return style
            }
            )
            val toastStyle = computed(fun(): UTSJSONObject {
                val style: UTSJSONObject = object : UTSJSONObject(UTSSourceMapPosition("style", "components/uc/u-toast/u-toast.uvue", 144, 11)) {
                }
                if (props.backgroundColor != null && props.backgroundColor !== "") {
                    style["background-color"] = props.backgroundColor
                }
                for(key in resolveUTSKeyIterator(props.customStyle)){
                    style[key] = props.customStyle[key]
                }
                return style
            }
            )
            val messageStyle = computed(fun(): UTSJSONObject {
                val style: UTSJSONObject = object : UTSJSONObject(UTSSourceMapPosition("style", "components/uc/u-toast/u-toast.uvue", 156, 11)) {
                }
                if (props.textColor != null && props.textColor !== "") {
                    style["color"] = props.textColor
                }
                return style
            }
            )
            fun gen_clearTimer_fn() {
                val timerId = timer.value
                if (timerId != null) {
                    clearTimeout(timerId)
                    timer.value = null
                }
            }
            val clearTimer = ::gen_clearTimer_fn
            fun gen_hide_fn() {
                visible.value = false
                clearTimer()
                emit("close")
            }
            val hide = ::gen_hide_fn
            fun gen_show_fn() {
                visible.value = true
                if (props.duration > 0) {
                    clearTimer()
                    timer.value = setTimeout(fun(){
                        hide()
                    }
                    , props.duration)
                }
            }
            val show = ::gen_show_fn
            watchEffect(fun(){
                if (props.visible) {
                    show()
                } else {
                    hide()
                }
            }
            )
            onUnmounted(fun(){
                clearTimer()
            }
            )
            __expose(utsMapOf("show" to show, "hide" to hide))
            return fun(): Any? {
                return if (isTrue(unref(visible))) {
                    createElementVNode("view", utsMapOf("key" to 0, "class" to "u-toast-container", "style" to normalizeStyle(unref(containerStyle))), utsArrayOf(
                        createElementVNode("view", utsMapOf("class" to normalizeClass(utsArrayOf(
                            "u-toast",
                            unref(toastClass)
                        )), "style" to normalizeStyle(unref(toastStyle))), utsArrayOf(
                            if (isTrue(_ctx.showIcon)) {
                                createElementVNode("view", utsMapOf("key" to 0, "class" to "u-toast-icon"), utsArrayOf(
                                    if (unref(iconType) === "text") {
                                        createElementVNode("text", utsMapOf("key" to 0, "class" to "u-toast-icon-text"), toDisplayString(getIconValue()), 1)
                                    } else {
                                        if (unref(iconType) === "custom") {
                                            createElementVNode("view", utsMapOf("key" to 1, "class" to "u-toast-icon-custom"), utsArrayOf(
                                                renderSlot(_ctx.`$slots`, "icon")
                                            ))
                                        } else {
                                            createCommentVNode("v-if", true)
                                        }
                                    }
                                ))
                            } else {
                                createCommentVNode("v-if", true)
                            },
                            createElementVNode("view", utsMapOf("class" to "u-toast-content"), utsArrayOf(
                                createElementVNode("text", utsMapOf("class" to "u-toast-message", "style" to normalizeStyle(unref(messageStyle))), toDisplayString(_ctx.message), 5)
                            ))
                        ), 6)
                    ), 4)
                } else {
                    createCommentVNode("v-if", true)
                }
            }
        }
        val styles: Map<String, Map<String, Map<String, Any>>> by lazy {
            normalizeCssStyles(utsArrayOf(
                styles0
            ))
        }
        val styles0: Map<String, Map<String, Map<String, Any>>>
            get() {
                return utsMapOf("u-toast-container" to padStyleMapOf(utsMapOf("position" to "fixed", "left" to 0, "right" to 0, "zIndex" to 9999, "pointerEvents" to "none", "display" to "flex", "justifyContent" to "center", "paddingTop" to 0, "paddingRight" to "32rpx", "paddingBottom" to 0, "paddingLeft" to "32rpx")), "u-toast" to padStyleMapOf(utsMapOf("display" to "flex", "alignItems" to "center", "minHeight" to "96rpx", "paddingTop" to "20rpx", "paddingRight" to "28rpx", "paddingBottom" to "20rpx", "paddingLeft" to "28rpx", "borderTopLeftRadius" to "16rpx", "borderTopRightRadius" to "16rpx", "borderBottomRightRadius" to "16rpx", "borderBottomLeftRadius" to "16rpx", "boxShadow" to "0 8rpx 24rpx rgba(0, 0, 0, 0.12)", "backdropFilter" to "blur(20rpx)", "pointerEvents" to "auto", "wordWrap" to "break-word", "borderTopWidth" to "1rpx", "borderRightWidth" to "1rpx", "borderBottomWidth" to "1rpx", "borderLeftWidth" to "1rpx", "borderTopStyle" to "solid", "borderRightStyle" to "solid", "borderBottomStyle" to "solid", "borderLeftStyle" to "solid", "borderTopColor" to "rgba(255,255,255,0.2)", "borderRightColor" to "rgba(255,255,255,0.2)", "borderBottomColor" to "rgba(255,255,255,0.2)", "borderLeftColor" to "rgba(255,255,255,0.2)", "animation" to "toastSlideIn 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94)", "position" to "relative", "overflow" to "hidden", "content::before" to "\"\"", "position::before" to "absolute", "top::before" to 0, "left::before" to "-100%", "width::before" to "100%", "height::before" to "100%", "backgroundImage::before" to "linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent)", "backgroundColor::before" to "rgba(0,0,0,0)", "animation::before" to "shine 2s ease-in-out infinite")), "u-toast-top" to padStyleMapOf(utsMapOf("transformOrigin" to "top center")), "u-toast-bottom" to padStyleMapOf(utsMapOf("transformOrigin" to "bottom center")), "u-toast-success" to padStyleMapOf(utsMapOf("backgroundImage" to "linear-gradient(135deg, rgba(82, 196, 26, 0.95) 0%, rgba(115, 209, 61, 0.95) 100%)", "backgroundColor" to "rgba(0,0,0,0)", "color" to "#FFFFFF", "borderTopColor" to "rgba(82,196,26,0.3)", "borderRightColor" to "rgba(82,196,26,0.3)", "borderBottomColor" to "rgba(82,196,26,0.3)", "borderLeftColor" to "rgba(82,196,26,0.3)")), "u-toast-error" to padStyleMapOf(utsMapOf("backgroundImage" to "linear-gradient(135deg, rgba(255, 77, 79, 0.95) 0%, rgba(255, 120, 117, 0.95) 100%)", "backgroundColor" to "rgba(0,0,0,0)", "color" to "#FFFFFF", "borderTopColor" to "rgba(255,77,79,0.3)", "borderRightColor" to "rgba(255,77,79,0.3)", "borderBottomColor" to "rgba(255,77,79,0.3)", "borderLeftColor" to "rgba(255,77,79,0.3)")), "u-toast-warning" to padStyleMapOf(utsMapOf("backgroundImage" to "linear-gradient(135deg, rgba(250, 173, 20, 0.95) 0%, rgba(255, 197, 61, 0.95) 100%)", "backgroundColor" to "rgba(0,0,0,0)", "color" to "#FFFFFF", "borderTopColor" to "rgba(250,173,20,0.3)", "borderRightColor" to "rgba(250,173,20,0.3)", "borderBottomColor" to "rgba(250,173,20,0.3)", "borderLeftColor" to "rgba(250,173,20,0.3)")), "u-toast-info" to padStyleMapOf(utsMapOf("backgroundImage" to "linear-gradient(135deg, rgba(24, 144, 255, 0.95) 0%, rgba(64, 169, 255, 0.95) 100%)", "backgroundColor" to "rgba(0,0,0,0)", "color" to "#FFFFFF", "borderTopColor" to "rgba(24,144,255,0.3)", "borderRightColor" to "rgba(24,144,255,0.3)", "borderBottomColor" to "rgba(24,144,255,0.3)", "borderLeftColor" to "rgba(24,144,255,0.3)")), "u-toast-default" to padStyleMapOf(utsMapOf("backgroundImage" to "linear-gradient(135deg, rgba(0, 0, 0, 0.85) 0%, rgba(64, 64, 64, 0.85) 100%)", "backgroundColor" to "rgba(0,0,0,0)", "color" to "#FFFFFF", "borderTopColor" to "rgba(255,255,255,0.1)", "borderRightColor" to "rgba(255,255,255,0.1)", "borderBottomColor" to "rgba(255,255,255,0.1)", "borderLeftColor" to "rgba(255,255,255,0.1)")), "u-toast-icon" to padStyleMapOf(utsMapOf("marginRight" to "16rpx", "flexShrink" to 0, "display" to "flex", "alignItems" to "center", "justifyContent" to "center", "width" to "48rpx", "height" to "48rpx", "backgroundColor" to "rgba(255,255,255,0.2)", "backdropFilter" to "blur(10rpx)")), "u-toast-icon-text" to padStyleMapOf(utsMapOf("fontSize" to "28rpx", "lineHeight" to 1, "fontWeight" to "bold", "textShadow" to "0 1rpx 2rpx rgba(0, 0, 0, 0.1)")), "u-toast-content" to padStyleMapOf(utsMapOf("flex" to 1, "minWidth" to 0)), "u-toast-message" to padStyleMapOf(utsMapOf("fontSize" to "30rpx", "lineHeight" to 1.5, "wordWrap" to "break-word", "textShadow" to "0 1rpx 2rpx rgba(0, 0, 0, 0.1)", "letterSpacing" to "0.5rpx")), "@FONT-FACE" to utsMapOf("0" to utsMapOf(), "1" to utsMapOf(), "2" to utsMapOf("u-toast" to utsMapOf("" to utsMapOf("minHeight" to "80rpx", "paddingTop" to "14rpx", "paddingRight" to "20rpx", "paddingBottom" to "14rpx", "paddingLeft" to "20rpx")), "u-toast-message" to utsMapOf("" to utsMapOf("fontSize" to "26rpx")), "u-toast-icon-text" to utsMapOf("" to utsMapOf("fontSize" to "28rpx")))))
            }
        var inheritAttrs = true
        var inject: Map<String, Map<String, Any?>> = utsMapOf()
        var emits: Map<String, Any?> = utsMapOf("close" to null, "click" to null)
        var props = normalizePropsOptions(utsMapOf("visible" to utsMapOf("type" to "Boolean", "default" to false), "message" to utsMapOf("type" to "String", "default" to ""), "type" to utsMapOf("type" to "String", "default" to "info" as ToastType), "backgroundColor" to utsMapOf("type" to "String", "default" to ""), "textColor" to utsMapOf("type" to "String", "default" to ""), "icon" to utsMapOf("type" to "String", "default" to ""), "iconColor" to utsMapOf("type" to "String", "default" to ""), "showIcon" to utsMapOf("type" to "Boolean", "default" to true), "duration" to utsMapOf("type" to "Number", "default" to 3000), "position" to utsMapOf("type" to "String", "default" to "top" as ToastPosition), "top" to utsMapOf("type" to "Number", "default" to 0), "customStyle" to utsMapOf("type" to "Object", "default" to UTSJSONObject())))
        var propsNeedCastKeys = utsArrayOf(
            "visible",
            "message",
            "type",
            "backgroundColor",
            "textColor",
            "icon",
            "iconColor",
            "showIcon",
            "duration",
            "position",
            "top",
            "customStyle"
        )
        var components: Map<String, CreateVueComponent> = utsMapOf()
    }
}
