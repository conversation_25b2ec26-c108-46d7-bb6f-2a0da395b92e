{"version": 3, "file": "interface.uts", "sourceRoot": "", "sources": ["uni_modules/lime-file-utils/utssdk/interface.uts"], "names": [], "mappings": "AAAA,cAAc;AACd,MAAM,MAAM,cAAc,GAAG,MAAM,GAAG,IAAI,CAAA;AAC1C,MAAM,MAAM,cAAc,GAAG,UAAU,GAAG,WAAW,GAAG,QAAQ,CAAA;AAChE,MAAM,MAAM,kBAAkB,GAAG;IAC/B,IAAI,EAAG,cAAc,CAAA;IACrB,IAAI,EAAE,MAAM,CAAA;IACZ,QAAQ,CAAC,EAAE,MAAM,CAAA;IACjB,OAAQ,CAAC,EAAE,CAAC,GAAG,EAAG,MAAM,KAAK,IAAI,CAAA;IACjC,IAAK,CAAC,EAAE,CAAC,GAAG,EAAG,GAAG,KAAK,IAAI,CAAA;IAC3B,QAAS,CAAC,EAAE,CAAC,GAAG,EAAG,GAAG,KAAK,IAAI,CAAA;CAChC,CAAA;AAID;;;;;GAKG;AACH,MAAM,MAAM,oBAAoB,GAAG,OAAO,GAAG,OAAO,CAAC;AACrD;;GAEG;AACH,MAAM,WAAW,eAAgB,SAAQ,SAAS;IAChD,OAAO,EAAG,oBAAoB,CAAA;CAC/B;AAAA,CAAC", "sourcesContent": ["// @ts-nocheck\r\nexport type NullableString = string | null\r\nexport type ConversionType = 'toBase64' | 'toDataURL' | 'toFile'\r\nexport type ProcessFileOptions = {\r\n  type : ConversionType\r\n  path: string\r\n  filename?: string\r\n  success ?: (res : string) => void\r\n  fail ?: (res : any) => void\r\n  complete ?: (res : any) => void\r\n}\r\n\r\n\r\n\r\n/**\r\n * 错误码\r\n * 根据uni错误码规范要求，建议错误码以90开头，以下是错误码示例：\r\n * - 9010001 错误信息1\r\n * - 9010002 错误信息2\r\n */\r\nexport type ProcessFileErrorCode = 9010001 | 9010002;\r\n/**\r\n * myApi 的错误回调参数\r\n */\r\nexport interface ProcessFileFail extends IUniError {\r\n  errCode : ProcessFileErrorCode\r\n};\r\n"]}