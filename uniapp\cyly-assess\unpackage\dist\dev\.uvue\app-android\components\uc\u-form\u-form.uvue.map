{"version": 3, "sources": ["components/uc/u-form/u-form.uvue"], "names": [], "mappings": "AAOC,OAAO,EAAE,YAAY,EAAE,oBAAoB,EAAE,eAAe,EAAE,mBAAmB,EAAE,MAAM,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;QAIpG,MAAM,KAAK,GAAG,OASZ,CAAC;QACH,MAAM,UAAU,GAAG,GAAG,CAAC,YAAY,EAAE,EAAE,EAAE,CAAC,CAAA;QAE1C,SAAA,IAAA,CAAA,KAAA,EAAA,MAAA,EAAA,GAAA,uBAAA,EAAA,KAAA,CAAA,GAAA,GAAA,IAAA,CAAA;YAAA,KAAA,CAAA,IAAA,CAAA,KAAA,EAAA,GAAA,uBAAA,CAAA,CAAA;QAAA,CAAyD;QAEzD,MAAM,KAAK,GAAG,QAAA,CAAA,aAAA,EAAA,KAAA,CAAA,KAAA,EAAA,YAAA,CAGZ,CAAA;QAEF,eAAe;QACf,SAAS,KAAK,CAAC,QAAQ,EAAG,eAAe;YACxC,IAAI,KAAK,GAAG,IAAI,CAAA;YAChB,IAAI,SAAS,GAAG,EAAE,IAAI,oBAAoB,EAAE,CAAA;YAC5C,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAG,YAAY,EAAE,EAAE;gBAChD,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAA;gBACtB,IAAI,KAAK,GAAG,CAAC,KAAK,CAAC,KAAK,IAAI,aAAa,CAAC,CAAC,KAAK,CAAC,CAAA;gBACjD,IAAI,KAAK,IAAI,IAAI,EAAE;oBAClB,IAAI,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,EAAG,YAAY,GAAI,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,IAAI,KAAK,CAAC,CAAA;oBACrF,IAAI,KAAK,IAAI,IAAI,EAAE;wBAClB,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,QAAQ,EAAE,KAAK,EAAE,CAAC,GAAG,EAAG,oBAAoB,EAAE,EAAE;4BACzE,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE;gCACf,KAAK,GAAG,KAAK,CAAA;gCACb,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;6BACnB;wBACF,CAAC,CAAC,CAAA;qBACF;iBACD;qBAAM;oBACN,KAAK,GAAG,KAAK,CAAA;iBACb;YACF,CAAC,CAAC,CAAA;YACF,IAAI,KAAK,EAAE;gBACV,IAAI,QAAQ,CAAC,OAAO,IAAI,IAAI,EAAE;oBAC7B,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAA;iBACnB;aACD;iBAAM;gBACN,IAAI,QAAQ,CAAC,IAAI,IAAI,IAAI,EAAE;oBAC1B,QAAQ,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAA;iBACzB;aACD;QACF,CAAC;QAED,SAAS,QAAQ,CAAC,QAAQ,EAAG,eAAe;YAC3C,KAAK,CAAC;gBACL,OAAO,EAAE,GAAG,EAAE;oBACb,IAAI,QAAQ,CAAC,OAAO,IAAI,IAAI,EAAE;wBAC7B,IAAI,CAAC,UAAU,CAAC,CAAA;wBAChB,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAA;qBACnB;gBACF,CAAC;gBACD,IAAI,EAAE,CAAC,WAAW,EAAG,oBAAoB,EAAE,EAAE,EAAE;oBAC9C,IAAI,QAAQ,CAAC,IAAI,IAAI,IAAI,EAAE;wBAC1B,QAAQ,CAAC,IAAI,CAAC,CAAC,WAAW,CAAC,CAAA;qBAC3B;gBACF,CAAC;aACD,IAAI,eAAe,CAAC,CAAA;QACtB,CAAC;QAED,SAAS,aAAa,CAAC,SAAS,EAAG,YAAY;YAC9C,IAAI,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,EAAG,YAAY,GAAI,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,IAAI,SAAS,CAAC,KAAK,CAAC,IAAI,IAAI,EAAE;gBACpG,OAAO;aACP;YACD,OAAO,CAAC,GAAG,CAAC,SAAQ,EAAA,yCAAC,CAAC,CAAC;YACvB,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACjC,OAAO,CAAC,GAAG,CAAC,2BAA2B,EAAE,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA,CAAC,UAAA,EAAE,CAAA,CAAC,CAAC,KAAK,CAAA,EAAA,yCAAC,CAAC,CAAC;QAC5E,CAAC;QACD,SAAS,SAAS,CAAC,KAAK,EAAG,MAAM,EAAE,QAAQ,EAAG,mBAAmB;YAChE,MAAM,SAAS,GAAG,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA,IAAI,WAAC,EAAE,CAAC,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,CAAC;YACrE,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,UAAS,EAAA,yCAAC,CAAC,CAAC;YAC7C,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAI,EAAA,yCAAC,CAAC,CAAC;YAC7B,MAAM,KAAK,GAAG,CAAC,KAAK,CAAC,KAAK,IAAI,SAAS,CAAC,CAAC,KAAK,CAAC,CAAA;YAC/C,IAAI,SAAS,IAAI,IAAI,EAAE;gBACtB,SAAS,CAAC,QAAQ,CAAC,WAAW,CAAC,QAAQ,EAAE,KAAK,EAAE,CAAC,GAAG,EAAG,oBAAoB,EAAE,EAAE;oBAC9E,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE;wBACf,IAAI,QAAQ,CAAC,IAAI,IAAI,IAAI,EAAE;4BAC1B,QAAQ,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAA;yBACnB;wBACD,OAAO;qBACP;oBACD,IAAI,QAAQ,CAAC,OAAO,IAAI,IAAI,EAAE;wBAC7B,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAA;qBACnB;gBACF,CAAC,CAAC,CAAA;aACF;iBAAM;gBACN,kBAAkB;gBAClB,IAAI,QAAQ,CAAC,IAAI,IAAI,IAAI,EAAE;oBAC1B,QAAQ,CAAC,IAAI,CAAC,CAAC;wBACd,KAAK,EAAE,KAAK;wBACZ,OAAO,EAAE,MAAM,KAAK,MAAM;wBAC1B,KAAK,EAAE,KAAK;qBACZ,IAAI,oBAAoB,CAAC,CAAA;iBAC1B;aACD;QACF,CAAC;QACD,KAAK,CAAC,KAAK,EAAE,CAAC,KAAK,EAAG,aAAa,EAAE,EAAE;YACtC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAA;YACrB,SAAS;YAET,IAAI,KAAK,CAAC,aAAa,EAAE;gBACxB,KAAK,CAAC;oBACL,OAAO;wBACN,OAAO,CAAC,GAAG,CAAC,GAAE,EAAA,0CAAC,CAAC,CAAC;oBAClB,CAAC;oBACD,IAAI,CAAC,GAAG;wBACP,OAAO,CAAC,GAAG,CAAC,KAAK,EAAE,GAAE,EAAA,0CAAC,CAAC,CAAC;oBACzB,CAAC;iBACD,oBAAC,CAAA;aACF;QACF,CAAC,EAAE;YACF,IAAI,EAAE,IAAI;SACV,CAAC,CAAA;QACF,QAAY,CAAC;YACZ,QAAQ;YACR,aAAa;YACb,SAAS;YACT,KAAK;SACL,CAAC,CAAC;;mBAvIQ,kBAAS,CAAA,MAAA,EAAA,QAAA,CAAA;gBAAE,KAAK,EAAA,QAAA;;cAC1B,EAAA", "sourcesContent": ["<template>\n\t<view class=\"u-form\" :style=\"[customStyle]\">\n\t\t<slot></slot>\n\t</view>\n</template>\n\n<script lang=\"uts\" setup>\n\timport { FormItemData, FormItemVerifyResult, FormValidResult, FormValidResultItem } from \"../types\";\r\n\tdefineOptions({\r\n\t\tname: 'u-form'\r\n\t})\n\tconst props = defineProps({\n\t\tcustomStyle: {\n\t\t\ttype: Object as PropType<UTSJSONObject>,\n\t\t\tdefault: {} as UTSJSONObject as UTSJSONObject\n\t\t},\n\t\ttriggerChange: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: false\n\t\t}\n\t});\n\tconst fieldItems = ref<FormItemData[]>([])\n\n\tconst emit = defineEmits(['submited', 'change', 'focus'])\n\n\tconst model = defineModel({\n\t\ttype: Object as PropType<UTSJSONObject>,\n\t\tdefault: {} as UTSJSONObject\n\t})\n\n\t// 先定义 valid 函数\n\tfunction valid(callback : FormValidResult) {\n\t\tlet allow = true\n\t\tlet verifyRes = [] as FormItemVerifyResult[]\n\t\tfieldItems.value.forEach((item : FormItemData) => {\n\t\t\tlet field = item.field\n\t\t\tlet value = (model.value as UTSJSONObject)[field]\n\t\t\tif (value != null) {\n\t\t\t\tlet _item = fieldItems.value.find((it : FormItemData) : boolean => it.field == field)\n\t\t\t\tif (_item != null) {\n\t\t\t\t\titem.instance.$callMethod('verify', value, (res : FormItemVerifyResult) => {\n\t\t\t\t\t\tif (!res.valid) {\n\t\t\t\t\t\t\tallow = false\n\t\t\t\t\t\t\tverifyRes.push(res)\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tallow = false\n\t\t\t}\n\t\t})\n\t\tif (allow) {\n\t\t\tif (callback.success != null) {\n\t\t\t\tcallback.success!()\n\t\t\t}\n\t\t} else {\n\t\t\tif (callback.fail != null) {\n\t\t\t\tcallback.fail!(verifyRes)\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction onSubmit(callback : FormValidResult) {\n\t\tvalid({\n\t\t\tsuccess: () => {\n\t\t\t\tif (callback.success != null) {\n\t\t\t\t\temit('submited')\n\t\t\t\t\tcallback.success!()\n\t\t\t\t}\n\t\t\t},\n\t\t\tfail: (failResults : FormItemVerifyResult[]) => {\n\t\t\t\tif (callback.fail != null) {\n\t\t\t\t\tcallback.fail!(failResults)\n\t\t\t\t}\n\t\t\t}\n\t\t} as FormValidResult)\n\t}\n\n\tfunction pushFielditem(fieldItem : FormItemData) {\n\t\tif (fieldItems.value.find((item : FormItemData) : boolean => item.field == fieldItem.field) != null) {\n\t\t\treturn;\n\t\t}\n\t\tconsole.log(fieldItem);\n\t\tfieldItems.value.push(fieldItem);\n\t\tconsole.log(\"pushFielditem-fieldItems:\", fieldItems.value.map(v=>v.field));\n\t}\n\tfunction validItem(field : string, callback : FormValidResultItem) {\n\t\tconst fieldItem = fieldItems.value.find(item => item.field == field);\r\n\t\tconsole.log(\"===fieldItems===:\", fieldItems);\r\n\t\tconsole.log(\"model:\", model);\n\t\tconst value = (model.value as UTSObject)[field]\n\t\tif (fieldItem != null) {\n\t\t\tfieldItem.instance.$callMethod('verify', value, (res : FormItemVerifyResult) => {\n\t\t\t\tif (!res.valid) {\n\t\t\t\t\tif (callback.fail != null) {\n\t\t\t\t\t\tcallback.fail!(res)\n\t\t\t\t\t}\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tif (callback.success != null) {\n\t\t\t\t\tcallback.success!()\n\t\t\t\t}\n\t\t\t})\n\t\t} else {\n\t\t\t// 如果找不到字段项，调用失败回调\n\t\t\tif (callback.fail != null) {\n\t\t\t\tcallback.fail!({\n\t\t\t\t\tvalid: false,\n\t\t\t\t\tmessage: `字段 ${field} 不存在`,\n\t\t\t\t\tfield: field\n\t\t\t\t} as FormItemVerifyResult)\n\t\t\t}\n\t\t}\n\t}\n\twatch(model, (value : UTSJSONObject) => {\n\t\temit('change', value)\n\t\t//TODO 测试\n\n\t\tif (props.triggerChange) {\n\t\t\tvalid({\n\t\t\t\tsuccess() {\n\t\t\t\t\tconsole.log(\"?\");\n\t\t\t\t},\n\t\t\t\tfail(res) {\n\t\t\t\t\tconsole.log(\"???\", res);\n\t\t\t\t}\n\t\t\t})\n\t\t}\n\t}, {\n\t\tdeep: true\n\t})\n\tdefineExpose({\n\t\tonSubmit,\n\t\tpushFielditem,\n\t\tvalidItem,\n\t\tvalid\n\t});\n</script>"]}