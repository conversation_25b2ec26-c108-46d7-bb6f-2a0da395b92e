@file:Suppress("UNCHECKED_CAST", "USELESS_CAST", "INAPPLICABLE_JVM_NAME", "UNUSED_ANONYMOUS_PARAMETER", "NAME_SHADOWING", "UNNECESSARY_NOT_NULL_ASSERTION")
package uni.UNI4AABA03
import io.dcloud.uniapp.*
import io.dcloud.uniapp.extapi.*
import io.dcloud.uniapp.framework.*
import io.dcloud.uniapp.runtime.*
import io.dcloud.uniapp.vue.*
import io.dcloud.uniapp.vue.shared.*
import io.dcloud.unicloud.*
import io.dcloud.uts.*
import io.dcloud.uts.Map
import io.dcloud.uts.Set
import io.dcloud.uts.UTSAndroid
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import io.dcloud.uniapp.extapi.connectSocket as uni_connectSocket
import io.dcloud.uniapp.extapi.exit as uni_exit
import io.dcloud.uniapp.extapi.getSystemInfoSync as uni_getSystemInfoSync
import io.dcloud.uniapp.extapi.showToast as uni_showToast
val runBlock1 = run {
    __uniConfig.getAppStyles = fun(): Map<String, Map<String, Map<String, Any>>> {
        return GenApp.styles
    }
}
fun initRuntimeSocket(hosts: String, port: String, id: String): UTSPromise<SocketTask?> {
    if (hosts == "" || port == "" || id == "") {
        return UTSPromise.resolve(null)
    }
    return hosts.split(",").reduce<UTSPromise<SocketTask?>>(fun(promise: UTSPromise<SocketTask?>, host: String): UTSPromise<SocketTask?> {
        return promise.then(fun(socket): UTSPromise<SocketTask?> {
            if (socket != null) {
                return UTSPromise.resolve(socket)
            }
            return tryConnectSocket(host, port, id)
        }
        )
    }
    , UTSPromise.resolve(null))
}
val SOCKET_TIMEOUT: Number = 500
fun tryConnectSocket(host: String, port: String, id: String): UTSPromise<SocketTask?> {
    return UTSPromise(fun(resolve, reject){
        val socket = uni_connectSocket(ConnectSocketOptions(url = "ws://" + host + ":" + port + "/" + id, fail = fun(_) {
            resolve(null)
        }
        ))
        val timer = setTimeout(fun(){
            socket.close(CloseSocketOptions(code = 1006, reason = "connect timeout"))
            resolve(null)
        }
        , SOCKET_TIMEOUT)
        socket.onOpen(fun(e){
            clearTimeout(timer)
            resolve(socket)
        }
        )
        socket.onClose(fun(e){
            clearTimeout(timer)
            resolve(null)
        }
        )
        socket.onError(fun(e){
            clearTimeout(timer)
            resolve(null)
        }
        )
    }
    )
}
fun initRuntimeSocketService(): UTSPromise<Boolean> {
    val hosts: String = "************,127.0.0.1"
    val port: String = "8090"
    val id: String = "app-android_19tXMI"
    if (hosts == "" || port == "" || id == "") {
        return UTSPromise.resolve(false)
    }
    var socketTask: SocketTask? = null
    __registerWebViewUniConsole(fun(): String {
        return "!function(){\"use strict\";\"function\"==typeof SuppressedError&&SuppressedError;var e=[\"log\",\"warn\",\"error\",\"info\",\"debug\"],n=e.reduce((function(e,n){return e[n]=console[n].bind(console),e}),{}),t=null,r=new Set,o={};function i(e){if(null!=t){var n=e.map((function(e){if(\"string\"==typeof e)return e;var n=e&&\"promise\"in e&&\"reason\"in e,t=n?\"UnhandledPromiseRejection: \":\"\";if(n&&(e=e.reason),e instanceof Error&&e.stack)return e.message&&!e.stack.includes(e.message)?\"\".concat(t).concat(e.message,\"\\n\").concat(e.stack):\"\".concat(t).concat(e.stack);if(\"object\"==typeof e&&null!==e)try{return t+JSON.stringify(e)}catch(e){return t+String(e)}return t+String(e)})).filter(Boolean);n.length>0&&t(JSON.stringify(Object.assign({type:\"error\",data:n},o)))}else e.forEach((function(e){r.add(e)}))}function a(e,n){try{return{type:e,args:u(n)}}catch(e){}return{type:e,args:[]}}function u(e){return e.map((function(e){return c(e)}))}function c(e,n){if(void 0===n&&(n=0),n>=7)return{type:\"object\",value:\"[Maximum depth reached]\"};switch(typeof e){case\"string\":return{type:\"string\",value:e};case\"number\":return function(e){return{type:\"number\",value:String(e)}}(e);case\"boolean\":return function(e){return{type:\"boolean\",value:String(e)}}(e);case\"object\":try{return function(e,n){if(null===e)return{type:\"null\"};if(function(e){return e.\$&&s(e.\$)}(e))return function(e,n){return{type:\"object\",className:\"ComponentPublicInstance\",value:{properties:Object.entries(e.\$.type).map((function(e){return f(e[0],e[1],n+1)}))}}}(e,n);if(s(e))return function(e,n){return{type:\"object\",className:\"ComponentInternalInstance\",value:{properties:Object.entries(e.type).map((function(e){return f(e[0],e[1],n+1)}))}}}(e,n);if(function(e){return e.style&&null!=e.tagName&&null!=e.nodeName}(e))return function(e,n){return{type:\"object\",value:{properties:Object.entries(e).filter((function(e){var n=e[0];return[\"id\",\"tagName\",\"nodeName\",\"dataset\",\"offsetTop\",\"offsetLeft\",\"style\"].includes(n)})).map((function(e){return f(e[0],e[1],n+1)}))}}}(e,n);if(function(e){return\"function\"==typeof e.getPropertyValue&&\"function\"==typeof e.setProperty&&e.\$styles}(e))return function(e,n){return{type:\"object\",value:{properties:Object.entries(e.\$styles).map((function(e){return f(e[0],e[1],n+1)}))}}}(e,n);if(Array.isArray(e))return{type:\"object\",subType:\"array\",value:{properties:e.map((function(e,t){return function(e,n,t){var r=c(e,t);return r.name=\"\".concat(n),r}(e,t,n+1)}))}};if(e instanceof Set)return{type:\"object\",subType:\"set\",className:\"Set\",description:\"Set(\".concat(e.size,\")\"),value:{entries:Array.from(e).map((function(e){return function(e,n){return{value:c(e,n)}}(e,n+1)}))}};if(e instanceof Map)return{type:\"object\",subType:\"map\",className:\"Map\",description:\"Map(\".concat(e.size,\")\"),value:{entries:Array.from(e.entries()).map((function(e){return function(e,n){return{key:c(e[0],n),value:c(e[1],n)}}(e,n+1)}))}};if(e instanceof Promise)return{type:\"object\",subType:\"promise\",value:{properties:[]}};if(e instanceof RegExp)return{type:\"object\",subType:\"regexp\",value:String(e),className:\"Regexp\"};if(e instanceof Date)return{type:\"object\",subType:\"date\",value:String(e),className:\"Date\"};if(e instanceof Error)return{type:\"object\",subType:\"error\",value:e.message||String(e),className:e.name||\"Error\"};var t=void 0,r=e.constructor;r&&r.get\$UTSMetadata\$&&(t=r.get\$UTSMetadata\$().name);var o=Object.entries(e);(function(e){return e.modifier&&e.modifier._attribute&&e.nodeContent})(e)&&(o=o.filter((function(e){var n=e[0];return\"modifier\"!==n&&\"nodeContent\"!==n})));return{type:\"object\",className:t,value:{properties:o.map((function(e){return f(e[0],e[1],n+1)}))}}}(e,n)}catch(e){return{type:\"object\",value:{properties:[]}}}case\"undefined\":return{type:\"undefined\"};case\"function\":return function(e){return{type:\"function\",value:\"function \".concat(e.name,\"() {}\")}}(e);case\"symbol\":return function(e){return{type:\"symbol\",value:e.description}}(e);case\"bigint\":return function(e){return{type:\"bigint\",value:String(e)}}(e)}}function s(e){return e.type&&null!=e.uid&&e.appContext}function f(e,n,t){var r=c(n,t);return r.name=e,r}var l=null,p=[],y={},g=\"---BEGIN:EXCEPTION---\",d=\"---END:EXCEPTION---\";function v(e){null!=l?l(JSON.stringify(Object.assign({type:\"console\",data:e},y))):p.push.apply(p,e)}var m=/^\\s*at\\s+[\\w/./-]+:\\d+\$/;function b(){function t(e){return function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];var o=function(e,n,t){if(t||2===arguments.length)for(var r,o=0,i=n.length;o<i;o++)!r&&o in n||(r||(r=Array.prototype.slice.call(n,0,o)),r[o]=n[o]);return e.concat(r||Array.prototype.slice.call(n))}([],t,!0);if(o.length){var u=o[o.length-1];\"string\"==typeof u&&m.test(u)&&o.pop()}if(n[e].apply(n,o),\"error\"===e&&1===t.length){var c=t[0];if(\"string\"==typeof c&&c.startsWith(g)){var s=g.length,f=c.length-d.length;return void i([c.slice(s,f)])}if(c instanceof Error)return void i([c])}v([a(e,t)])}}return function(){var e=console.log,n=Symbol();try{console.log=n}catch(e){return!1}var t=console.log===n;return console.log=e,t}()?(e.forEach((function(e){console[e]=t(e)})),function(){e.forEach((function(e){console[e]=n[e]}))}):function(){}}function _(e){var n={type:\"WEB_INVOKE_APPSERVICE\",args:{data:{name:\"console\",arg:e}}};return window.__uniapp_x_postMessageToService?window.__uniapp_x_postMessageToService(n):window.__uniapp_x_.postMessageToService(JSON.stringify(n))}!function(){if(!window.__UNI_CONSOLE_WEBVIEW__){window.__UNI_CONSOLE_WEBVIEW__=!0;var e=\"[web-view]\".concat(window.__UNI_PAGE_ROUTE__?\"[\".concat(window.__UNI_PAGE_ROUTE__,\"]\"):\"\");b(),function(e,n){if(void 0===n&&(n={}),l=e,Object.assign(y,n),null!=e&&p.length>0){var t=p.slice();p.length=0,v(t)}}((function(e){_(e)}),{channel:e}),function(e,n){if(void 0===n&&(n={}),t=e,Object.assign(o,n),null!=e&&r.size>0){var a=Array.from(r);r.clear(),i(a)}}((function(e){_(e)}),{channel:e}),window.addEventListener(\"error\",(function(e){i([e.error])})),window.addEventListener(\"unhandledrejection\",(function(e){i([e])}))}}()}();"
    }
    , fun(data: String){
        socketTask?.send(SendSocketMessageOptions(data = data))
    }
    )
    return UTSPromise.resolve().then(fun(): UTSPromise<Boolean> {
        return initRuntimeSocket(hosts, port, id).then(fun(socket): Boolean {
            if (socket == null) {
                return false
            }
            socketTask = socket
            return true
        }
        )
    }
    ).`catch`(fun(): Boolean {
        return false
    }
    )
}
val runBlock2 = run {
    initRuntimeSocketService()
}
var firstBackTime: Number = 0
open class GenApp : BaseApp {
    constructor(__ins: ComponentInternalInstance) : super(__ins) {
        onLaunch(fun(options: OnLaunchOptions) {
            console.log("App Launch", " at App.uvue:15")
        }
        , __ins)
        onAppShow(fun(options: OnShowOptions) {
            console.log("App Show", " at App.uvue:18")
        }
        , __ins)
        onAppHide(fun() {
            console.log("App Hide", " at App.uvue:21")
        }
        , __ins)
        onLastPageBackPress(fun() {
            console.log("App LastPageBackPress", " at App.uvue:25")
            if (firstBackTime == 0) {
                uni_showToast(ShowToastOptions(title = "再按一次退出应用", position = "bottom"))
                firstBackTime = Date.now()
                setTimeout(fun(){
                    firstBackTime = 0
                }, 2000)
            } else if (Date.now() - firstBackTime < 2000) {
                firstBackTime = Date.now()
                uni_exit(null)
            }
        }
        , __ins)
        onExit(fun() {
            console.log("App Exit", " at App.uvue:41")
        }
        , __ins)
        onError(fun(err: Any) {
            console.log("App Error", err, " at App.uvue:45")
        }
        , __ins)
    }
    companion object {
        val styles: Map<String, Map<String, Map<String, Any>>> by lazy {
            normalizeCssStyles(utsArrayOf())
        }
    }
}
val GenAppClass = CreateVueAppComponent(GenApp::class.java, fun(): VueComponentOptions {
    return VueComponentOptions(type = "app", name = "", inheritAttrs = true, inject = Map(), props = Map(), propsNeedCastKeys = utsArrayOf(), emits = Map(), components = Map(), styles = GenApp.styles)
}
, fun(instance): GenApp {
    return GenApp(instance)
}
)
var statusBarHeight = "" + uni_getSystemInfoSync().statusBarHeight + "px"
val GenComponentsFirstuiFuiStatusBarFuiStatusBarClass = CreateVueComponent(GenComponentsFirstuiFuiStatusBarFuiStatusBar::class.java, fun(): VueComponentOptions {
    return VueComponentOptions(type = "component", name = GenComponentsFirstuiFuiStatusBarFuiStatusBar.name, inheritAttrs = GenComponentsFirstuiFuiStatusBarFuiStatusBar.inheritAttrs, inject = GenComponentsFirstuiFuiStatusBarFuiStatusBar.inject, props = GenComponentsFirstuiFuiStatusBarFuiStatusBar.props, propsNeedCastKeys = GenComponentsFirstuiFuiStatusBarFuiStatusBar.propsNeedCastKeys, emits = GenComponentsFirstuiFuiStatusBarFuiStatusBar.emits, components = GenComponentsFirstuiFuiStatusBarFuiStatusBar.components, styles = GenComponentsFirstuiFuiStatusBarFuiStatusBar.styles)
}
, fun(instance, renderer): GenComponentsFirstuiFuiStatusBarFuiStatusBar {
    return GenComponentsFirstuiFuiStatusBarFuiStatusBar(instance)
}
)
open class FuiFooterNavigateParam (
    @JsonNotNull
    open var text: String,
    open var url: String? = null,
    open var openType: String? = null,
    open var delta: Number? = null,
    open var color: String? = null,
    open var size: Number? = null,
) : UTSReactiveObject(), IUTSSourceMap {
    override fun `__$getOriginalPosition`(): UTSSourceMapPosition? {
        return UTSSourceMapPosition("FuiFooterNavigateParam", "components/firstui/fui-types/index.uts", 42, 13)
    }
    override fun __v_create(__v_isReadonly: Boolean, __v_isShallow: Boolean, __v_skip: Boolean): UTSReactiveObject {
        return FuiFooterNavigateParamReactiveObject(this, __v_isReadonly, __v_isShallow, __v_skip)
    }
}
open class FuiFooterNavigateParamReactiveObject : FuiFooterNavigateParam, IUTSReactive<FuiFooterNavigateParam> {
    override var __v_raw: FuiFooterNavigateParam
    override var __v_isReadonly: Boolean
    override var __v_isShallow: Boolean
    override var __v_skip: Boolean
    constructor(__v_raw: FuiFooterNavigateParam, __v_isReadonly: Boolean, __v_isShallow: Boolean, __v_skip: Boolean) : super(text = __v_raw.text, url = __v_raw.url, openType = __v_raw.openType, delta = __v_raw.delta, color = __v_raw.color, size = __v_raw.size) {
        this.__v_raw = __v_raw
        this.__v_isReadonly = __v_isReadonly
        this.__v_isShallow = __v_isShallow
        this.__v_skip = __v_skip
    }
    override fun __v_clone(__v_isReadonly: Boolean, __v_isShallow: Boolean, __v_skip: Boolean): FuiFooterNavigateParamReactiveObject {
        return FuiFooterNavigateParamReactiveObject(this.__v_raw, __v_isReadonly, __v_isShallow, __v_skip)
    }
    override var text: String
        get() {
            return trackReactiveGet(__v_raw, "text", __v_raw.text, this.__v_isReadonly, this.__v_isShallow)
        }
        set(value) {
            if (!this.__v_canSet("text")) {
                return
            }
            val oldValue = __v_raw.text
            __v_raw.text = value
            triggerReactiveSet(__v_raw, "text", oldValue, value)
        }
    override var url: String?
        get() {
            return trackReactiveGet(__v_raw, "url", __v_raw.url, this.__v_isReadonly, this.__v_isShallow)
        }
        set(value) {
            if (!this.__v_canSet("url")) {
                return
            }
            val oldValue = __v_raw.url
            __v_raw.url = value
            triggerReactiveSet(__v_raw, "url", oldValue, value)
        }
    override var openType: String?
        get() {
            return trackReactiveGet(__v_raw, "openType", __v_raw.openType, this.__v_isReadonly, this.__v_isShallow)
        }
        set(value) {
            if (!this.__v_canSet("openType")) {
                return
            }
            val oldValue = __v_raw.openType
            __v_raw.openType = value
            triggerReactiveSet(__v_raw, "openType", oldValue, value)
        }
    override var delta: Number?
        get() {
            return trackReactiveGet(__v_raw, "delta", __v_raw.delta, this.__v_isReadonly, this.__v_isShallow)
        }
        set(value) {
            if (!this.__v_canSet("delta")) {
                return
            }
            val oldValue = __v_raw.delta
            __v_raw.delta = value
            triggerReactiveSet(__v_raw, "delta", oldValue, value)
        }
    override var color: String?
        get() {
            return trackReactiveGet(__v_raw, "color", __v_raw.color, this.__v_isReadonly, this.__v_isShallow)
        }
        set(value) {
            if (!this.__v_canSet("color")) {
                return
            }
            val oldValue = __v_raw.color
            __v_raw.color = value
            triggerReactiveSet(__v_raw, "color", oldValue, value)
        }
    override var size: Number?
        get() {
            return trackReactiveGet(__v_raw, "size", __v_raw.size, this.__v_isReadonly, this.__v_isShallow)
        }
        set(value) {
            if (!this.__v_canSet("size")) {
                return
            }
            val oldValue = __v_raw.size
            __v_raw.size = value
            triggerReactiveSet(__v_raw, "size", oldValue, value)
        }
}
open class FuiCheckboxChangeParam (
    @JsonNotNull
    open var checked: Boolean = false,
    @JsonNotNull
    open var value: String,
) : UTSObject(), IUTSSourceMap {
    override fun `__$getOriginalPosition`(): UTSSourceMapPosition? {
        return UTSSourceMapPosition("FuiCheckboxChangeParam", "components/firstui/fui-types/index.uts", 68, 13)
    }
}
open class FuiTabsItemParam (
    @JsonNotNull
    open var name: String,
    open var icon: String? = null,
    open var selectedIcon: String? = null,
    open var badge: Number? = null,
    open var isDot: Boolean? = null,
    open var disabled: Boolean? = null,
    open var index: Number? = null,
) : UTSReactiveObject(), IUTSSourceMap {
    override fun `__$getOriginalPosition`(): UTSSourceMapPosition? {
        return UTSSourceMapPosition("FuiTabsItemParam", "components/firstui/fui-types/index.uts", 191, 13)
    }
    override fun __v_create(__v_isReadonly: Boolean, __v_isShallow: Boolean, __v_skip: Boolean): UTSReactiveObject {
        return FuiTabsItemParamReactiveObject(this, __v_isReadonly, __v_isShallow, __v_skip)
    }
}
open class FuiTabsItemParamReactiveObject : FuiTabsItemParam, IUTSReactive<FuiTabsItemParam> {
    override var __v_raw: FuiTabsItemParam
    override var __v_isReadonly: Boolean
    override var __v_isShallow: Boolean
    override var __v_skip: Boolean
    constructor(__v_raw: FuiTabsItemParam, __v_isReadonly: Boolean, __v_isShallow: Boolean, __v_skip: Boolean) : super(name = __v_raw.name, icon = __v_raw.icon, selectedIcon = __v_raw.selectedIcon, badge = __v_raw.badge, isDot = __v_raw.isDot, disabled = __v_raw.disabled, index = __v_raw.index) {
        this.__v_raw = __v_raw
        this.__v_isReadonly = __v_isReadonly
        this.__v_isShallow = __v_isShallow
        this.__v_skip = __v_skip
    }
    override fun __v_clone(__v_isReadonly: Boolean, __v_isShallow: Boolean, __v_skip: Boolean): FuiTabsItemParamReactiveObject {
        return FuiTabsItemParamReactiveObject(this.__v_raw, __v_isReadonly, __v_isShallow, __v_skip)
    }
    override var name: String
        get() {
            return trackReactiveGet(__v_raw, "name", __v_raw.name, this.__v_isReadonly, this.__v_isShallow)
        }
        set(value) {
            if (!this.__v_canSet("name")) {
                return
            }
            val oldValue = __v_raw.name
            __v_raw.name = value
            triggerReactiveSet(__v_raw, "name", oldValue, value)
        }
    override var icon: String?
        get() {
            return trackReactiveGet(__v_raw, "icon", __v_raw.icon, this.__v_isReadonly, this.__v_isShallow)
        }
        set(value) {
            if (!this.__v_canSet("icon")) {
                return
            }
            val oldValue = __v_raw.icon
            __v_raw.icon = value
            triggerReactiveSet(__v_raw, "icon", oldValue, value)
        }
    override var selectedIcon: String?
        get() {
            return trackReactiveGet(__v_raw, "selectedIcon", __v_raw.selectedIcon, this.__v_isReadonly, this.__v_isShallow)
        }
        set(value) {
            if (!this.__v_canSet("selectedIcon")) {
                return
            }
            val oldValue = __v_raw.selectedIcon
            __v_raw.selectedIcon = value
            triggerReactiveSet(__v_raw, "selectedIcon", oldValue, value)
        }
    override var badge: Number?
        get() {
            return trackReactiveGet(__v_raw, "badge", __v_raw.badge, this.__v_isReadonly, this.__v_isShallow)
        }
        set(value) {
            if (!this.__v_canSet("badge")) {
                return
            }
            val oldValue = __v_raw.badge
            __v_raw.badge = value
            triggerReactiveSet(__v_raw, "badge", oldValue, value)
        }
    override var isDot: Boolean?
        get() {
            return trackReactiveGet(__v_raw, "isDot", __v_raw.isDot, this.__v_isReadonly, this.__v_isShallow)
        }
        set(value) {
            if (!this.__v_canSet("isDot")) {
                return
            }
            val oldValue = __v_raw.isDot
            __v_raw.isDot = value
            triggerReactiveSet(__v_raw, "isDot", oldValue, value)
        }
    override var disabled: Boolean?
        get() {
            return trackReactiveGet(__v_raw, "disabled", __v_raw.disabled, this.__v_isReadonly, this.__v_isShallow)
        }
        set(value) {
            if (!this.__v_canSet("disabled")) {
                return
            }
            val oldValue = __v_raw.disabled
            __v_raw.disabled = value
            triggerReactiveSet(__v_raw, "disabled", oldValue, value)
        }
    override var index: Number?
        get() {
            return trackReactiveGet(__v_raw, "index", __v_raw.index, this.__v_isReadonly, this.__v_isShallow)
        }
        set(value) {
            if (!this.__v_canSet("index")) {
                return
            }
            val oldValue = __v_raw.index
            __v_raw.index = value
            triggerReactiveSet(__v_raw, "index", oldValue, value)
        }
}
val GenComponentsFirstuiFuiFooterFuiFooterClass = CreateVueComponent(GenComponentsFirstuiFuiFooterFuiFooter::class.java, fun(): VueComponentOptions {
    return VueComponentOptions(type = "component", name = GenComponentsFirstuiFuiFooterFuiFooter.name, inheritAttrs = GenComponentsFirstuiFuiFooterFuiFooter.inheritAttrs, inject = GenComponentsFirstuiFuiFooterFuiFooter.inject, props = GenComponentsFirstuiFuiFooterFuiFooter.props, propsNeedCastKeys = GenComponentsFirstuiFuiFooterFuiFooter.propsNeedCastKeys, emits = GenComponentsFirstuiFuiFooterFuiFooter.emits, components = GenComponentsFirstuiFuiFooterFuiFooter.components, styles = GenComponentsFirstuiFuiFooterFuiFooter.styles)
}
, fun(instance, renderer): GenComponentsFirstuiFuiFooterFuiFooter {
    return GenComponentsFirstuiFuiFooterFuiFooter(instance)
}
)
val GenComponentsFirstuiFuiRowFuiRowClass = CreateVueComponent(GenComponentsFirstuiFuiRowFuiRow::class.java, fun(): VueComponentOptions {
    return VueComponentOptions(type = "component", name = GenComponentsFirstuiFuiRowFuiRow.name, inheritAttrs = GenComponentsFirstuiFuiRowFuiRow.inheritAttrs, inject = GenComponentsFirstuiFuiRowFuiRow.inject, props = GenComponentsFirstuiFuiRowFuiRow.props, propsNeedCastKeys = GenComponentsFirstuiFuiRowFuiRow.propsNeedCastKeys, emits = GenComponentsFirstuiFuiRowFuiRow.emits, components = GenComponentsFirstuiFuiRowFuiRow.components, styles = GenComponentsFirstuiFuiRowFuiRow.styles)
}
, fun(instance, renderer): GenComponentsFirstuiFuiRowFuiRow {
    return GenComponentsFirstuiFuiRowFuiRow(instance)
}
)
val GenComponentsFirstuiFuiTabsFuiTabsClass = CreateVueComponent(GenComponentsFirstuiFuiTabsFuiTabs::class.java, fun(): VueComponentOptions {
    return VueComponentOptions(type = "component", name = GenComponentsFirstuiFuiTabsFuiTabs.name, inheritAttrs = GenComponentsFirstuiFuiTabsFuiTabs.inheritAttrs, inject = GenComponentsFirstuiFuiTabsFuiTabs.inject, props = GenComponentsFirstuiFuiTabsFuiTabs.props, propsNeedCastKeys = GenComponentsFirstuiFuiTabsFuiTabs.propsNeedCastKeys, emits = GenComponentsFirstuiFuiTabsFuiTabs.emits, components = GenComponentsFirstuiFuiTabsFuiTabs.components, styles = GenComponentsFirstuiFuiTabsFuiTabs.styles)
}
, fun(instance, renderer): GenComponentsFirstuiFuiTabsFuiTabs {
    return GenComponentsFirstuiFuiTabsFuiTabs(instance)
}
)
val GenComponentsFirstuiFuiInputFuiInputClass = CreateVueComponent(GenComponentsFirstuiFuiInputFuiInput::class.java, fun(): VueComponentOptions {
    return VueComponentOptions(type = "component", name = GenComponentsFirstuiFuiInputFuiInput.name, inheritAttrs = GenComponentsFirstuiFuiInputFuiInput.inheritAttrs, inject = GenComponentsFirstuiFuiInputFuiInput.inject, props = GenComponentsFirstuiFuiInputFuiInput.props, propsNeedCastKeys = GenComponentsFirstuiFuiInputFuiInput.propsNeedCastKeys, emits = GenComponentsFirstuiFuiInputFuiInput.emits, components = GenComponentsFirstuiFuiInputFuiInput.components, styles = GenComponentsFirstuiFuiInputFuiInput.styles)
}
, fun(instance, renderer): GenComponentsFirstuiFuiInputFuiInput {
    return GenComponentsFirstuiFuiInputFuiInput(instance)
}
)
val GenComponentsFirstuiFuiButtonFuiButtonClass = CreateVueComponent(GenComponentsFirstuiFuiButtonFuiButton::class.java, fun(): VueComponentOptions {
    return VueComponentOptions(type = "component", name = GenComponentsFirstuiFuiButtonFuiButton.name, inheritAttrs = GenComponentsFirstuiFuiButtonFuiButton.inheritAttrs, inject = GenComponentsFirstuiFuiButtonFuiButton.inject, props = GenComponentsFirstuiFuiButtonFuiButton.props, propsNeedCastKeys = GenComponentsFirstuiFuiButtonFuiButton.propsNeedCastKeys, emits = GenComponentsFirstuiFuiButtonFuiButton.emits, components = GenComponentsFirstuiFuiButtonFuiButton.components, styles = GenComponentsFirstuiFuiButtonFuiButton.styles)
}
, fun(instance, renderer): GenComponentsFirstuiFuiButtonFuiButton {
    return GenComponentsFirstuiFuiButtonFuiButton(instance)
}
)
val GenComponentsFirstuiFuiCheckboxFuiCheckboxClass = CreateVueComponent(GenComponentsFirstuiFuiCheckboxFuiCheckbox::class.java, fun(): VueComponentOptions {
    return VueComponentOptions(type = "component", name = GenComponentsFirstuiFuiCheckboxFuiCheckbox.name, inheritAttrs = GenComponentsFirstuiFuiCheckboxFuiCheckbox.inheritAttrs, inject = GenComponentsFirstuiFuiCheckboxFuiCheckbox.inject, props = GenComponentsFirstuiFuiCheckboxFuiCheckbox.props, propsNeedCastKeys = GenComponentsFirstuiFuiCheckboxFuiCheckbox.propsNeedCastKeys, emits = GenComponentsFirstuiFuiCheckboxFuiCheckbox.emits, components = GenComponentsFirstuiFuiCheckboxFuiCheckbox.components, styles = GenComponentsFirstuiFuiCheckboxFuiCheckbox.styles)
}
, fun(instance, renderer): GenComponentsFirstuiFuiCheckboxFuiCheckbox {
    return GenComponentsFirstuiFuiCheckboxFuiCheckbox(instance)
}
)
open class FormItemData (
    @JsonNotNull
    open var field: String,
    @JsonNotNull
    open var instance: ComponentPublicInstance,
) : UTSReactiveObject(), IUTSSourceMap {
    override fun `__$getOriginalPosition`(): UTSSourceMapPosition? {
        return UTSSourceMapPosition("FormItemData", "components/uc/types/index.uts", 28, 13)
    }
    override fun __v_create(__v_isReadonly: Boolean, __v_isShallow: Boolean, __v_skip: Boolean): UTSReactiveObject {
        return FormItemDataReactiveObject(this, __v_isReadonly, __v_isShallow, __v_skip)
    }
}
open class FormItemDataReactiveObject : FormItemData, IUTSReactive<FormItemData> {
    override var __v_raw: FormItemData
    override var __v_isReadonly: Boolean
    override var __v_isShallow: Boolean
    override var __v_skip: Boolean
    constructor(__v_raw: FormItemData, __v_isReadonly: Boolean, __v_isShallow: Boolean, __v_skip: Boolean) : super(field = __v_raw.field, instance = __v_raw.instance) {
        this.__v_raw = __v_raw
        this.__v_isReadonly = __v_isReadonly
        this.__v_isShallow = __v_isShallow
        this.__v_skip = __v_skip
    }
    override fun __v_clone(__v_isReadonly: Boolean, __v_isShallow: Boolean, __v_skip: Boolean): FormItemDataReactiveObject {
        return FormItemDataReactiveObject(this.__v_raw, __v_isReadonly, __v_isShallow, __v_skip)
    }
    override var field: String
        get() {
            return trackReactiveGet(__v_raw, "field", __v_raw.field, this.__v_isReadonly, this.__v_isShallow)
        }
        set(value) {
            if (!this.__v_canSet("field")) {
                return
            }
            val oldValue = __v_raw.field
            __v_raw.field = value
            triggerReactiveSet(__v_raw, "field", oldValue, value)
        }
    override var instance: ComponentPublicInstance
        get() {
            return trackReactiveGet(__v_raw, "instance", __v_raw.instance, this.__v_isReadonly, this.__v_isShallow)
        }
        set(value) {
            if (!this.__v_canSet("instance")) {
                return
            }
            val oldValue = __v_raw.instance
            __v_raw.instance = value
            triggerReactiveSet(__v_raw, "instance", oldValue, value)
        }
}
open class FormItemRule (
    open var type: String? = null,
    open var required: Boolean? = null,
    open var message: String? = null,
    open var pattern: UTSRegExp? = null,
    open var min: Number? = null,
    open var max: Number? = null,
    open var len: Number? = null,
    open var `enum`: UTSArray<Any>? = null,
    open var transform: ((value: Any) -> Any)? = null,
    open var valid: ((value: Any) -> String)? = null,
) : UTSReactiveObject(), IUTSSourceMap {
    override fun `__$getOriginalPosition`(): UTSSourceMapPosition? {
        return UTSSourceMapPosition("FormItemRule", "components/uc/types/index.uts", 32, 13)
    }
    override fun __v_create(__v_isReadonly: Boolean, __v_isShallow: Boolean, __v_skip: Boolean): UTSReactiveObject {
        return FormItemRuleReactiveObject(this, __v_isReadonly, __v_isShallow, __v_skip)
    }
}
open class FormItemRuleReactiveObject : FormItemRule, IUTSReactive<FormItemRule> {
    override var __v_raw: FormItemRule
    override var __v_isReadonly: Boolean
    override var __v_isShallow: Boolean
    override var __v_skip: Boolean
    constructor(__v_raw: FormItemRule, __v_isReadonly: Boolean, __v_isShallow: Boolean, __v_skip: Boolean) : super(type = __v_raw.type, required = __v_raw.required, message = __v_raw.message, pattern = __v_raw.pattern, min = __v_raw.min, max = __v_raw.max, len = __v_raw.len, `enum` = __v_raw.`enum`, transform = __v_raw.transform, valid = __v_raw.valid) {
        this.__v_raw = __v_raw
        this.__v_isReadonly = __v_isReadonly
        this.__v_isShallow = __v_isShallow
        this.__v_skip = __v_skip
    }
    override fun __v_clone(__v_isReadonly: Boolean, __v_isShallow: Boolean, __v_skip: Boolean): FormItemRuleReactiveObject {
        return FormItemRuleReactiveObject(this.__v_raw, __v_isReadonly, __v_isShallow, __v_skip)
    }
    override var type: String?
        get() {
            return trackReactiveGet(__v_raw, "type", __v_raw.type, this.__v_isReadonly, this.__v_isShallow)
        }
        set(value) {
            if (!this.__v_canSet("type")) {
                return
            }
            val oldValue = __v_raw.type
            __v_raw.type = value
            triggerReactiveSet(__v_raw, "type", oldValue, value)
        }
    override var required: Boolean?
        get() {
            return trackReactiveGet(__v_raw, "required", __v_raw.required, this.__v_isReadonly, this.__v_isShallow)
        }
        set(value) {
            if (!this.__v_canSet("required")) {
                return
            }
            val oldValue = __v_raw.required
            __v_raw.required = value
            triggerReactiveSet(__v_raw, "required", oldValue, value)
        }
    override var message: String?
        get() {
            return trackReactiveGet(__v_raw, "message", __v_raw.message, this.__v_isReadonly, this.__v_isShallow)
        }
        set(value) {
            if (!this.__v_canSet("message")) {
                return
            }
            val oldValue = __v_raw.message
            __v_raw.message = value
            triggerReactiveSet(__v_raw, "message", oldValue, value)
        }
    override var pattern: UTSRegExp?
        get() {
            return trackReactiveGet(__v_raw, "pattern", __v_raw.pattern, this.__v_isReadonly, this.__v_isShallow)
        }
        set(value) {
            if (!this.__v_canSet("pattern")) {
                return
            }
            val oldValue = __v_raw.pattern
            __v_raw.pattern = value
            triggerReactiveSet(__v_raw, "pattern", oldValue, value)
        }
    override var min: Number?
        get() {
            return trackReactiveGet(__v_raw, "min", __v_raw.min, this.__v_isReadonly, this.__v_isShallow)
        }
        set(value) {
            if (!this.__v_canSet("min")) {
                return
            }
            val oldValue = __v_raw.min
            __v_raw.min = value
            triggerReactiveSet(__v_raw, "min", oldValue, value)
        }
    override var max: Number?
        get() {
            return trackReactiveGet(__v_raw, "max", __v_raw.max, this.__v_isReadonly, this.__v_isShallow)
        }
        set(value) {
            if (!this.__v_canSet("max")) {
                return
            }
            val oldValue = __v_raw.max
            __v_raw.max = value
            triggerReactiveSet(__v_raw, "max", oldValue, value)
        }
    override var len: Number?
        get() {
            return trackReactiveGet(__v_raw, "len", __v_raw.len, this.__v_isReadonly, this.__v_isShallow)
        }
        set(value) {
            if (!this.__v_canSet("len")) {
                return
            }
            val oldValue = __v_raw.len
            __v_raw.len = value
            triggerReactiveSet(__v_raw, "len", oldValue, value)
        }
    override var `enum`: UTSArray<Any>?
        get() {
            return trackReactiveGet(__v_raw, "enum", __v_raw.`enum`, this.__v_isReadonly, this.__v_isShallow)
        }
        set(value) {
            if (!this.__v_canSet("enum")) {
                return
            }
            val oldValue = __v_raw.`enum`
            __v_raw.`enum` = value
            triggerReactiveSet(__v_raw, "enum", oldValue, value)
        }
}
open class FormItemVerifyResult (
    @JsonNotNull
    open var valid: Boolean = false,
    open var message: String? = null,
    @JsonNotNull
    open var field: String,
) : UTSObject(), IUTSSourceMap {
    override fun `__$getOriginalPosition`(): UTSSourceMapPosition? {
        return UTSSourceMapPosition("FormItemVerifyResult", "components/uc/types/index.uts", 76, 13)
    }
}
open class FormValidResult (
    open var success: (() -> Unit)? = null,
    open var fail: ((failResults: UTSArray<FormItemVerifyResult>) -> Unit)? = null,
) : UTSObject(), IUTSSourceMap {
    override fun `__$getOriginalPosition`(): UTSSourceMapPosition? {
        return UTSSourceMapPosition("FormValidResult", "components/uc/types/index.uts", 90, 13)
    }
}
open class FormValidResultItem (
    open var success: (() -> Unit)? = null,
    open var fail: ((failResults: FormItemVerifyResult?) -> Unit)? = null,
) : UTSObject(), IUTSSourceMap {
    override fun `__$getOriginalPosition`(): UTSSourceMapPosition? {
        return UTSSourceMapPosition("FormValidResultItem", "components/uc/types/index.uts", 100, 13)
    }
}
typealias ToastType = String
typealias ToastPosition = String
val GenComponentsUcUFormUFormClass = CreateVueComponent(GenComponentsUcUFormUForm::class.java, fun(): VueComponentOptions {
    return VueComponentOptions(type = "component", name = GenComponentsUcUFormUForm.name, inheritAttrs = GenComponentsUcUFormUForm.inheritAttrs, inject = GenComponentsUcUFormUForm.inject, props = GenComponentsUcUFormUForm.props, propsNeedCastKeys = GenComponentsUcUFormUForm.propsNeedCastKeys, emits = GenComponentsUcUFormUForm.emits, components = GenComponentsUcUFormUForm.components, styles = GenComponentsUcUFormUForm.styles, setup = fun(props: ComponentPublicInstance, ctx: SetupContext): Any? {
        return GenComponentsUcUFormUForm.setup(props as GenComponentsUcUFormUForm, ctx)
    }
    )
}
, fun(instance, renderer): GenComponentsUcUFormUForm {
    return GenComponentsUcUFormUForm(instance)
}
)
fun isNumber(value: Any?): Boolean {
    return utsArrayOf(
        "Byte",
        "UByte",
        "Short",
        "UShort",
        "Int",
        "UInt",
        "Long",
        "ULong",
        "Float",
        "Double",
        "number"
    ).includes(UTSAndroid.`typeof`(value))
}
fun isURL(url: String): Boolean {
    var reg = UTSRegExp("^((https|http|ftp|rtsp|mms|ws):\\/\\/)(([0-9a-zA-Z_!~*'().&=+\$%-]+: )?[0-9a-zA-Z_!~*'().&=+\$%-]+@)?(([0-9]{1,3}.){3}[0-9]{1,3}|([0-9a-zA-Z_!~*'()-]+.)*([0-9a-zA-Z][0-9a-zA-Z-]{0,61})?[0-9a-zA-Z].[a-zA-Z]{2,6})(:[0-9]{1,4})?((\\/?)|(\\/[0-9a-zA-Z_!~*'().;?:@&=+\$,%#-]+)+\\/?)\$", "")
    return reg.test(url)
}
fun isEmail(email: String): Boolean {
    val reg = UTSRegExp("^\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*\$", "g")
    return reg.test(email)
}
fun isPhone(phone: String): Boolean {
    val reg = UTSRegExp("^[1][3,4,5,6,7,8,9][0-9]{9}\$", "g")
    return reg.test(phone)
}
fun isIDCard(code: String): Boolean {
    var cities = utsArrayOf(
        "11",
        "12",
        "13",
        "14",
        "15",
        "21",
        "22",
        "23",
        "31",
        "32",
        "33",
        "34",
        "35",
        "36",
        "37",
        "41",
        "42",
        "43",
        "44",
        "45",
        "46",
        "50",
        "51",
        "52",
        "53",
        "54",
        "61",
        "62",
        "63",
        "64",
        "65",
        "71",
        "81",
        "82",
        "91"
    )
    var case1 = code.length != 18
    var case2 = code.trim() == "" || !UTSRegExp("^\\d{6}(18|19|20)?\\d{2}(0[1-9]|1[012])(0[1-9]|[12]\\d|3[01])\\d{3}(\\d|X)\$", "i").test(code)
    var case3 = !cities.includes(code.slice(0, 2))
    if (case1 || case2 || case3) {
        return false
    }
    if (code.length == 18) {
        var _code = code.split("")
        var factor: UTSArray<Number> = utsArrayOf(
            7,
            9,
            10,
            5,
            8,
            4,
            2,
            1,
            6,
            3,
            7,
            9,
            10,
            5,
            8,
            4,
            2
        )
        var parity = utsArrayOf(
            "1",
            "0",
            "X",
            "9",
            "8,",
            "7",
            "6",
            "5",
            "4",
            "3",
            "2"
        )
        var sum: Number = 0
        var ai: Number = 0
        var wi: Number = 0
        run {
            var i: Number = 0
            while(i < 17){
                ai = parseInt(_code[i])
                wi = factor[i]
                sum += ai * wi
                i++
            }
        }
        if (parity[sum % 11] != _code[17]) {
            return false
        }
    }
    return true
}
fun isFloat(num: Number): Boolean {
    return !isNaN(num) && isFinite(num) && Math.trunc(num) != num
}
fun isInteger(num: Number): Boolean {
    return !isNaN(num) && isFinite(num) && Math.trunc(num) == num
}
fun isEmpty(str: String): Boolean {
    return str.trim() == ""
}
fun useVerify(isValid: Ref<Boolean>, hintMessage: Ref<String>, field: String, itemRule: FormItemRule, reassignedValue: Any, callback: (res: FormItemVerifyResult) -> Unit) {
    var value = reassignedValue
    var rule = itemRule
    var type = rule.type
    var valid = true
    var message = rule.message
    var empty = false
    if (UTSAndroid.`typeof`(value) == "string") {
        if ((value as String) == "") {
            empty = true
        }
    } else if (UTSArray.isArray(value)) {
        if ((value as UTSArray<Any>).length == 0) {
            empty = true
        }
    }
    if (rule.transform != null) {
        value = rule.transform!!(value)
    }
    if (rule.valid != null) {
        var msg = rule.valid!!(value)
        valid = msg.trim() == ""
        message = if (valid) {
            message
        } else {
            msg
        }
    }
    if (rule.pattern != null && UTSAndroid.`typeof`(value) == "string") {
        if (!(rule.pattern as UTSRegExp).test(value as String)) {
            valid = false
        }
    }
    if (rule.required == true) {
        if (UTSAndroid.`typeof`(value) == "string") {
            if (isEmpty(value as String)) {
                valid = false
            }
        }
    }
    if (type != null) {
        fun check(type: String) {
            if (UTSAndroid.`typeof`(value) != type) {
                valid = false
            }
        }
        when (type) {
            "string" -> 
                check("string")
            "number" -> 
                if (!isNumber(value)) {
                    valid = false
                }
            "boolean" -> 
                check("boolean")
            "object" -> 
                check("object")
            "array" -> 
                check("array")
            "integer" -> 
                if (isNumber(value)) {
                    if (!isInteger(value as Number)) {
                        valid = false
                    }
                } else {
                    valid = false
                }
            "float" -> 
                if (isNumber(value)) {
                    if (!isFloat(value as Number)) {
                        valid = false
                    }
                } else {
                    valid = false
                }
            "enum" -> 
                if (rule.`enum` != null && !(rule.`enum` as UTSArray<Any>).includes(value)) {
                    valid = false
                }
            "url" -> 
                if (!empty && !isURL(value as String)) {
                    valid = false
                }
            "email" -> 
                if (!empty && !isEmail(value as String)) {
                    valid = false
                }
            "idcard" -> 
                if (!empty && !isIDCard(value as String)) {
                    valid = false
                }
            "phone" -> 
                if (!empty && !isPhone(value as String)) {
                    valid = false
                }
        }
    }
    if (UTSAndroid.`typeof`(value) == "string") {
        if (rule.min != null && (value as String).length < (rule.min as Number)) {
            valid = false
        }
        if (rule.max != null && (value as String).length > (rule.max as Number)) {
            valid = false
        }
        if (rule.len != null) {
            if ((value as String).length != (rule.len as Number)) {
                valid = false
            } else {
                valid = true
            }
        }
    }
    if (isNumber(value)) {
        if (rule.min != null && (value as Number) < (rule.min as Number)) {
            valid = false
        }
        if (rule.max != null && (value as Number) > (rule.max as Number)) {
            valid = false
        }
    }
    isValid.value = valid
    hintMessage.value = message ?: ""
    callback(FormItemVerifyResult(message = message, valid = valid, field = field))
}
fun findParent(instance: ComponentPublicInstance, parentNames: UTSArray<String>): ComponentPublicInstance? {
    run {
        var i: Number = 0
        while(i < parentNames.length){
            var parent = instance.`$parent`
            var name = parent?.`$options`?.name
            while(parent != null && (name == null || parentNames[i] != name)){
                parent = parent.`$parent`
                if (parent != null) {
                    name = parent.`$options`.name
                }
            }
            if (parent != null) {
                return parent as ComponentPublicInstance
            }
            i++
        }
    }
    return null
}
val GenComponentsUcUFromItemUFromItemClass = CreateVueComponent(GenComponentsUcUFromItemUFromItem::class.java, fun(): VueComponentOptions {
    return VueComponentOptions(type = "component", name = "", inheritAttrs = GenComponentsUcUFromItemUFromItem.inheritAttrs, inject = GenComponentsUcUFromItemUFromItem.inject, props = GenComponentsUcUFromItemUFromItem.props, propsNeedCastKeys = GenComponentsUcUFromItemUFromItem.propsNeedCastKeys, emits = GenComponentsUcUFromItemUFromItem.emits, components = GenComponentsUcUFromItemUFromItem.components, styles = GenComponentsUcUFromItemUFromItem.styles, setup = fun(props: ComponentPublicInstance, ctx: SetupContext): Any? {
        return GenComponentsUcUFromItemUFromItem.setup(props as GenComponentsUcUFromItemUFromItem, ctx)
    }
    )
}
, fun(instance, renderer): GenComponentsUcUFromItemUFromItem {
    return GenComponentsUcUFromItemUFromItem(instance)
}
)
val GenComponentsUcUToastUToastClass = CreateVueComponent(GenComponentsUcUToastUToast::class.java, fun(): VueComponentOptions {
    return VueComponentOptions(type = "component", name = "", inheritAttrs = GenComponentsUcUToastUToast.inheritAttrs, inject = GenComponentsUcUToastUToast.inject, props = GenComponentsUcUToastUToast.props, propsNeedCastKeys = GenComponentsUcUToastUToast.propsNeedCastKeys, emits = GenComponentsUcUToastUToast.emits, components = GenComponentsUcUToastUToast.components, styles = GenComponentsUcUToastUToast.styles, setup = fun(props: ComponentPublicInstance, ctx: SetupContext): Any? {
        return GenComponentsUcUToastUToast.setup(props as GenComponentsUcUToastUToast, ctx)
    }
    )
}
, fun(instance, renderer): GenComponentsUcUToastUToast {
    return GenComponentsUcUToastUToast(instance)
}
)
open class phoneModelType (
    @JsonNotNull
    open var phone: String,
    @JsonNotNull
    open var code: String,
) : UTSReactiveObject(), IUTSSourceMap {
    override fun `__$getOriginalPosition`(): UTSSourceMapPosition? {
        return UTSSourceMapPosition("phoneModelType", "pages/login/types.uts", 1, 13)
    }
    override fun __v_create(__v_isReadonly: Boolean, __v_isShallow: Boolean, __v_skip: Boolean): UTSReactiveObject {
        return phoneModelTypeReactiveObject(this, __v_isReadonly, __v_isShallow, __v_skip)
    }
}
open class phoneModelTypeReactiveObject : phoneModelType, IUTSReactive<phoneModelType> {
    override var __v_raw: phoneModelType
    override var __v_isReadonly: Boolean
    override var __v_isShallow: Boolean
    override var __v_skip: Boolean
    constructor(__v_raw: phoneModelType, __v_isReadonly: Boolean, __v_isShallow: Boolean, __v_skip: Boolean) : super(phone = __v_raw.phone, code = __v_raw.code) {
        this.__v_raw = __v_raw
        this.__v_isReadonly = __v_isReadonly
        this.__v_isShallow = __v_isShallow
        this.__v_skip = __v_skip
    }
    override fun __v_clone(__v_isReadonly: Boolean, __v_isShallow: Boolean, __v_skip: Boolean): phoneModelTypeReactiveObject {
        return phoneModelTypeReactiveObject(this.__v_raw, __v_isReadonly, __v_isShallow, __v_skip)
    }
    override var phone: String
        get() {
            return trackReactiveGet(__v_raw, "phone", __v_raw.phone, this.__v_isReadonly, this.__v_isShallow)
        }
        set(value) {
            if (!this.__v_canSet("phone")) {
                return
            }
            val oldValue = __v_raw.phone
            __v_raw.phone = value
            triggerReactiveSet(__v_raw, "phone", oldValue, value)
        }
    override var code: String
        get() {
            return trackReactiveGet(__v_raw, "code", __v_raw.code, this.__v_isReadonly, this.__v_isShallow)
        }
        set(value) {
            if (!this.__v_canSet("code")) {
                return
            }
            val oldValue = __v_raw.code
            __v_raw.code = value
            triggerReactiveSet(__v_raw, "code", oldValue, value)
        }
}
val GenPagesLoginLoginClass = CreateVueComponent(GenPagesLoginLogin::class.java, fun(): VueComponentOptions {
    return VueComponentOptions(type = "page", name = "", inheritAttrs = GenPagesLoginLogin.inheritAttrs, inject = GenPagesLoginLogin.inject, props = GenPagesLoginLogin.props, propsNeedCastKeys = GenPagesLoginLogin.propsNeedCastKeys, emits = GenPagesLoginLogin.emits, components = GenPagesLoginLogin.components, styles = GenPagesLoginLogin.styles, setup = fun(props: ComponentPublicInstance): Any? {
        return GenPagesLoginLogin.setup(props as GenPagesLoginLogin)
    }
    )
}
, fun(instance, renderer): GenPagesLoginLogin {
    return GenPagesLoginLogin(instance, renderer)
}
)
val GenPagesIndexIndexClass = CreateVueComponent(GenPagesIndexIndex::class.java, fun(): VueComponentOptions {
    return VueComponentOptions(type = "page", name = "", inheritAttrs = GenPagesIndexIndex.inheritAttrs, inject = GenPagesIndexIndex.inject, props = GenPagesIndexIndex.props, propsNeedCastKeys = GenPagesIndexIndex.propsNeedCastKeys, emits = GenPagesIndexIndex.emits, components = GenPagesIndexIndex.components, styles = GenPagesIndexIndex.styles, setup = fun(props: ComponentPublicInstance): Any? {
        return GenPagesIndexIndex.setup(props as GenPagesIndexIndex)
    }
    )
}
, fun(instance, renderer): GenPagesIndexIndex {
    return GenPagesIndexIndex(instance, renderer)
}
)
fun createApp(): UTSJSONObject {
    val app = createSSRApp(GenAppClass)
    return UTSJSONObject(Map<String, Any?>(utsArrayOf(
        utsArrayOf(
            "app",
            app
        )
    )))
}
fun main(app: IApp) {
    definePageRoutes()
    defineAppConfig()
    (createApp()["app"] as VueApp).mount(app, GenUniApp())
}
open class UniAppConfig : io.dcloud.uniapp.appframe.AppConfig {
    override var name: String = "demo"
    override var appid: String = "__UNI__4AABA03"
    override var versionName: String = "1.0.0"
    override var versionCode: String = "100"
    override var uniCompilerVersion: String = "4.66"
    constructor() : super() {}
}
fun definePageRoutes() {
    __uniRoutes.push(UniPageRoute(path = "pages/login/login", component = GenPagesLoginLoginClass, meta = UniPageMeta(isQuit = true), style = utsMapOf("navigationBarTitleText" to "登录", "navigationStyle" to "custom")))
    __uniRoutes.push(UniPageRoute(path = "pages/index/index", component = GenPagesIndexIndexClass, meta = UniPageMeta(isQuit = false), style = utsMapOf("navigationBarTitleText" to "index")))
}
val __uniLaunchPage: Map<String, Any?> = utsMapOf("url" to "pages/login/login", "style" to utsMapOf("navigationBarTitleText" to "登录", "navigationStyle" to "custom"))
fun defineAppConfig() {
    __uniConfig.entryPagePath = "/pages/login/login"
    __uniConfig.globalStyle = utsMapOf("pageOrientation" to "portrait", "backgroundColor" to "#F8F8F8", "backgroundColorTop" to "#F4F5F6", "backgroundColorBottom" to "#F4F5F6", "navigationStyle" to "custom")
    __uniConfig.getTabBarConfig = fun(): Map<String, Any>? {
        return null
    }
    __uniConfig.tabBar = __uniConfig.getTabBarConfig()
    __uniConfig.conditionUrl = ""
    __uniConfig.uniIdRouter = utsMapOf()
    __uniConfig.ready = true
}
open class GenUniApp : UniAppImpl() {
    open val vm: GenApp?
        get() {
            return getAppVm() as GenApp?
        }
    open val `$vm`: GenApp?
        get() {
            return getAppVm() as GenApp?
        }
}
fun getApp(): GenUniApp {
    return getUniApp() as GenUniApp
}
