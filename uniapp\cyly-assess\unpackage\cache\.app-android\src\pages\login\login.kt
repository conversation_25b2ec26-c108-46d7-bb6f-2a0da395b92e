@file:Suppress("UNCHECKED_CAST", "USELESS_CAST", "INAPPLICABLE_JVM_NAME", "UNUSED_ANONYMOUS_PARAMETER", "NAME_SHADOWING", "UNNECESSARY_NOT_NULL_ASSERTION")
package uni.UNI4AABA03
import io.dcloud.uniapp.*
import io.dcloud.uniapp.extapi.*
import io.dcloud.uniapp.framework.*
import io.dcloud.uniapp.runtime.*
import io.dcloud.uniapp.vue.*
import io.dcloud.uniapp.vue.shared.*
import io.dcloud.unicloud.*
import io.dcloud.uts.*
import io.dcloud.uts.Map
import io.dcloud.uts.Set
import io.dcloud.uts.UTSAndroid
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
open class GenPagesLoginLogin : BasePage {
    constructor(__ins: ComponentInternalInstance, __renderer: String?) : super(__ins, __renderer) {}
    companion object {
        @Suppress("UNUSED_PARAMETER", "UNUSED_VARIABLE")
        var setup: (__props: GenPagesLoginLogin) -> Any? = fun(__props): Any? {
            val __ins = getCurrentInstance()!!
            val _ctx = __ins.proxy as GenPagesLoginLogin
            val _cache = __ins.renderCache
            val inputStyle: UTSJSONObject = object : UTSJSONObject(UTSSourceMapPosition("inputStyle", "pages/login/login.uvue", 71, 8)) {
                var inputBorder = true
                var size: Number = 28
                var radius: Number = 12
                var marginTop: Number = 16
                var type = "number"
            }
            val instance = getCurrentInstance()
            val loginForm = ref<ComponentPublicInstance?>(null)
            fun gen_onBlur_fn(field: String) {
                console.log("field:", field, " at pages/login/login.uvue:97")
                val f = loginForm.value
                if (f != null) {
                    f.`$callMethod`("validItem", field, FormValidResultItem(success = fun() {
                        console.log("success", " at pages/login/login.uvue:103")
                    }
                    , fail = fun(res) {
                        console.log("fail:", res, " at pages/login/login.uvue:106")
                    }
                    ))
                }
            }
            val onBlur = ::gen_onBlur_fn
            val tabsList = ref(utsArrayOf(
                object : UTSJSONObject() {
                    var name = "手机号登录"
                    var id: Number = 0
                },
                object : UTSJSONObject() {
                    var name = "账号登录"
                    var id: Number = 1
                }
            ))
            val current = ref(0)
            fun gen_handleChangeTab_fn(e: FuiTabsItemParam) {
                console.log("handleChangeTab:", e, " at pages/login/login.uvue:120")
                if (e.index != null) {
                    current.value = e.index as Number
                }
            }
            val handleChangeTab = ::gen_handleChangeTab_fn
            val phoneModel = reactive<phoneModelType>(phoneModelType(phone = "", code = ""))
            val phoneRules = ref<FormItemRule>(FormItemRule(type = "phone", required = true, message = "请输入正确的手机号"))
            val codeRules = ref<FormItemRule>(FormItemRule(type = "number", required = true, message = "请输入验证码"))
            val countryCode = ref("86")
            val codeText = ref("获取验证码")
            val canGetCode = ref(true)
            val accountForm = reactive(object : UTSJSONObject() {
                var username = ""
                var password = ""
            })
            val isAgreeProtocol = ref(false)
            val isDisabledloginButton = computed(fun(): Boolean {
                return !isAgreeProtocol.value
            }
            )
            fun gen_ChangeIsAgreeProtocol_fn() {
                isAgreeProtocol.value = !isAgreeProtocol.value
            }
            val ChangeIsAgreeProtocol = ::gen_ChangeIsAgreeProtocol_fn
            fun gen_onSubmit_fn(e: Any) {
                console.log("onSubmit:", e, " at pages/login/login.uvue:165")
            }
            val onSubmit = ::gen_onSubmit_fn
            return fun(): Any? {
                val _component_block = resolveComponent("block")
                return createElementVNode("view", utsMapOf("class" to "login-container"), utsArrayOf(
                    createVNode(unref(GenComponentsFirstuiFuiStatusBarFuiStatusBarClass)),
                    createElementVNode("view", utsMapOf("class" to "gradient-circle top-right")),
                    createElementVNode("view", utsMapOf("class" to "gradient-circle bottom-left")),
                    createElementVNode("view", utsMapOf("class" to "form-container"), utsArrayOf(
                        createElementVNode("view", utsMapOf("class" to "logo-title"), utsArrayOf(
                            createElementVNode("text", utsMapOf("class" to "title-text"), "登录")
                        )),
                        createVNode(unref(GenComponentsFirstuiFuiTabsFuiTabsClass), utsMapOf("class" to "form-login-tabs", "tabs" to unref(tabsList), "short" to false, "center" to "", "current" to unref(current), "onChange" to handleChangeTab), null, 8, utsArrayOf(
                            "tabs",
                            "current"
                        )),
                        createElementVNode("view", utsMapOf("class" to "login-form-container"), utsArrayOf(
                            if (unref(current) === 0) {
                                createVNode(_component_block, utsMapOf("key" to 0), utsMapOf("default" to withSlotCtx(fun(): UTSArray<Any> {
                                    return utsArrayOf(
                                        createVNode(unref(GenComponentsUcUFormUFormClass), utsMapOf("ref_key" to "loginForm", "ref" to loginForm, "onSubmited" to onSubmit, "modelValue" to unref(phoneModel), "onUpdate:modelValue" to fun(`$event`: phoneModelType){
                                            trySetRefValue(phoneModel, `$event`)
                                        }, "showToast" to true, "toastTop" to 120, "toastDuration" to 4000), utsMapOf("default" to withSlotCtx(fun(): UTSArray<Any> {
                                            return utsArrayOf(
                                                createVNode(unref(GenComponentsUcUFromItemUFromItemClass), utsMapOf("field" to "phone", "rule" to unref(phoneRules), "marginTop" to 16), utsMapOf("default" to withSlotCtx(fun(): UTSArray<Any> {
                                                    return utsArrayOf(
                                                        createVNode(unref(GenComponentsFirstuiFuiInputFuiInputClass), utsMapOf("ref" to "phone", "onBlur" to fun(){
                                                            onBlur("phone")
                                                        }, "borderBottom" to false, "size" to 28, "radius" to 12, "type" to "number", "placeholder" to "请输入手机号", "modelValue" to unref(phoneModel).phone, "onUpdate:modelValue" to fun(`$event`: String){
                                                            unref(phoneModel).phone = `$event`
                                                        }), null, 8, utsArrayOf(
                                                            "onBlur",
                                                            "modelValue",
                                                            "onUpdate:modelValue"
                                                        ))
                                                    )
                                                }), "_" to 1), 8, utsArrayOf(
                                                    "rule"
                                                )),
                                                createVNode(unref(GenComponentsUcUFromItemUFromItemClass), utsMapOf("field" to "code", "rule" to unref(codeRules), "marginTop" to 16), utsMapOf("default" to withSlotCtx(fun(): UTSArray<Any> {
                                                    return utsArrayOf(
                                                        createVNode(unref(GenComponentsFirstuiFuiInputFuiInputClass), utsMapOf("ref" to "code", "onBlur" to fun(){
                                                            onBlur("code")
                                                        }, "borderBottom" to false, "size" to 28, "radius" to 12, "type" to "number", "placeholder" to "请输入验证码", "modelValue" to unref(phoneModel).code, "onUpdate:modelValue" to fun(`$event`: String){
                                                            unref(phoneModel).code = `$event`
                                                        }), null, 8, utsArrayOf(
                                                            "onBlur",
                                                            "modelValue",
                                                            "onUpdate:modelValue"
                                                        ))
                                                    )
                                                }), "_" to 1), 8, utsArrayOf(
                                                    "rule"
                                                ))
                                            )
                                        }), "_" to 1), 8, utsArrayOf(
                                            "modelValue"
                                        ))
                                    )
                                }), "_" to 1))
                            } else {
                                createVNode(_component_block, utsMapOf("key" to 1), utsMapOf("default" to withSlotCtx(fun(): UTSArray<Any> {
                                    return utsArrayOf(
                                        " 2 "
                                    )
                                }
                                ), "_" to 1))
                            }
                        )),
                        createElementVNode("view", utsMapOf("class" to "agreement"), utsArrayOf(
                            createVNode(unref(GenComponentsFirstuiFuiRowFuiRowClass), utsMapOf("justify" to "center", "class" to "agreement-row"), utsMapOf("default" to withSlotCtx(fun(): UTSArray<Any> {
                                return utsArrayOf(
                                    createVNode(unref(GenComponentsFirstuiFuiCheckboxFuiCheckboxClass), utsMapOf("class" to "agreement-checkbox", "checked" to unref(isAgreeProtocol), "borderRadius" to "8rpx", "scaleRatio" to 0.78, "onChange" to ChangeIsAgreeProtocol), null, 8, utsArrayOf(
                                        "checked"
                                    )),
                                    createElementVNode("text", utsMapOf("class" to "agreement-text"), "我已阅读并同意"),
                                    createElementVNode("text", utsMapOf("class" to "agreement-text agreement-link"), "《用户服务条款》"),
                                    createElementVNode("text", utsMapOf("class" to "agreement-text"), "和"),
                                    createElementVNode("text", utsMapOf("class" to "agreement-text agreement-link"), "《隐私协议》")
                                )
                            }
                            ), "_" to 1))
                        ))
                    )),
                    createElementVNode("view", utsMapOf("class" to "footer"), utsArrayOf(
                        createVNode(unref(GenComponentsFirstuiFuiFooterFuiFooterClass), utsMapOf("text" to "by@海南长养乔智能科技有限责任公司"))
                    ))
                ))
            }
        }
        val styles: Map<String, Map<String, Map<String, Any>>> by lazy {
            normalizeCssStyles(utsArrayOf(
                styles0
            ), utsArrayOf(
                GenApp.styles
            ))
        }
        val styles0: Map<String, Map<String, Map<String, Any>>>
            get() {
                return utsMapOf("login-container" to padStyleMapOf(utsMapOf("height" to "100%", "display" to "flex", "flexDirection" to "column", "alignItems" to "center", "position" to "relative")), "gradient-circle" to padStyleMapOf(utsMapOf("position" to "absolute", "borderTopLeftRadius" to "200rpx", "borderTopRightRadius" to "200rpx", "borderBottomRightRadius" to "200rpx", "borderBottomLeftRadius" to "200rpx", "backgroundColor" to "#33a1fd", "zIndex" to 0, "fontSize::after" to 12, "position::after" to "absolute", "top::after" to 0, "left::after" to 0)), "top-right" to padStyleMapOf(utsMapOf("top" to "-80rpx", "right" to "-30rpx", "width" to "300rpx", "height" to "300rpx", "opacity" to 0.08)), "bottom-left" to padStyleMapOf(utsMapOf("bottom" to "-60rpx", "left" to "-60rpx", "width" to "280rpx", "height" to "280rpx", "opacity" to 0.1)), "logo-title" to padStyleMapOf(utsMapOf("textAlign" to "center", "marginTop" to "10rpx", "marginRight" to 0, "marginBottom" to "30rpx", "marginLeft" to 0)), "title-text" to padStyleMapOf(utsMapOf("fontSize" to "58rpx", "fontWeight" to "bold", "color" to "#465CFF")), "form-container" to padStyleMapOf(utsMapOf("display" to "flex", "justifyContent" to "center", "alignItems" to "center", "width" to "100%", "height" to "70%")), "login-form-container" to utsMapOf(".form-container " to utsMapOf("paddingTop" to 0, "paddingRight" to "16rpx", "paddingBottom" to 0, "paddingLeft" to "16rpx", "display" to "flex", "minWidth" to "580rpx")), "agreement" to padStyleMapOf(utsMapOf("display" to "flex", "alignItems" to "flex-start", "marginTop" to "24rpx", "color" to "#CCCCCC")), "agreement-row" to utsMapOf(".agreement " to utsMapOf("alignItems" to "center")), "agreement-text" to padStyleMapOf(utsMapOf("fontSize" to "24rpx")), "agreement-link" to padStyleMapOf(utsMapOf("color" to "#465CFF", "textDecoration" to "none")), "footer" to padStyleMapOf(utsMapOf("textAlign" to "center", "paddingTop" to "16rpx", "paddingRight" to "16rpx", "paddingBottom" to "16rpx", "paddingLeft" to "16rpx", "color" to "#CCCCCC", "fontSize" to "32rpx", "marginTop" to "20rpx")))
            }
        var inheritAttrs = true
        var inject: Map<String, Map<String, Any?>> = utsMapOf()
        var emits: Map<String, Any?> = utsMapOf()
        var props = normalizePropsOptions(utsMapOf())
        var propsNeedCastKeys: UTSArray<String> = utsArrayOf()
        var components: Map<String, CreateVueComponent> = utsMapOf()
    }
}
