@file:Suppress("UNCHECKED_CAST", "USELESS_CAST", "INAPPLICABLE_JVM_NAME", "UNUSED_ANONYMOUS_PARAMETER", "NAME_SHADOWING", "UNNECESSARY_NOT_NULL_ASSERTION")
package uni.UNI4AABA03
import io.dcloud.uniapp.*
import io.dcloud.uniapp.extapi.*
import io.dcloud.uniapp.framework.*
import io.dcloud.uniapp.runtime.*
import io.dcloud.uniapp.vue.*
import io.dcloud.uniapp.vue.shared.*
import io.dcloud.unicloud.*
import io.dcloud.uts.*
import io.dcloud.uts.Map
import io.dcloud.uts.Set
import io.dcloud.uts.UTSAndroid
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
open class GenPagesLoginLogin : BasePage {
    constructor(__ins: ComponentInternalInstance, __renderer: String?) : super(__ins, __renderer) {}
    companion object {
        @Suppress("UNUSED_PARAMETER", "UNUSED_VARIABLE")
        var setup: (__props: GenPagesLoginLogin) -> Any? = fun(__props): Any? {
            val __ins = getCurrentInstance()!!
            val _ctx = __ins.proxy as GenPagesLoginLogin
            val _cache = __ins.renderCache
            val inputStyle: UTSJSONObject = object : UTSJSONObject(UTSSourceMapPosition("inputStyle", "pages/login/login.uvue", 79, 8)) {
                var inputBorder = true
                var size: Number = 28
                var radius: Number = 12
                var marginTop: Number = 16
                var type = "number"
            }
            val instance = getCurrentInstance()
            val loginForm = ref<ComponentPublicInstance?>(null)
            fun gen_onBlur_fn(field: String) {
                console.log("field:", field, " at pages/login/login.uvue:106")
                val f = loginForm.value
                if (f != null) {
                    f.`$callMethod`("validItem", field, FormValidResultItem(success = fun() {
                        console.log("success", " at pages/login/login.uvue:112")
                    }
                    , fail = fun(res) {
                        console.log("fail:", res, " at pages/login/login.uvue:115")
                    }
                    ))
                }
            }
            val onBlur = ::gen_onBlur_fn
            val tabsList = ref(utsArrayOf(
                object : UTSJSONObject() {
                    var name = "手机号登录"
                    var id: Number = 0
                },
                object : UTSJSONObject() {
                    var name = "账号登录"
                    var id: Number = 1
                }
            ))
            val current = ref(0)
            fun gen_handleChangeTab_fn(e: FuiTabsItemParam) {
                console.log("handleChangeTab:", e, " at pages/login/login.uvue:129")
                if (e.index != null) {
                    current.value = e.index as Number
                }
            }
            val handleChangeTab = ::gen_handleChangeTab_fn
            val phoneModel = reactive<phoneModelType>(phoneModelType(phone = "", code = ""))
            val phoneRules = ref<FormItemRule>(FormItemRule(type = "phone", required = true, message = "请输入正确的手机号"))
            val codeRules = ref<FormItemRule>(FormItemRule(type = "number", required = true, message = "请输入验证码"))
            val countryCode = ref("86")
            val codeText = ref("获取验证码")
            val canGetCode = ref(true)
            val accountForm = reactive(object : UTSJSONObject() {
                var username = ""
                var password = ""
            })
            val isAgreeProtocol = ref(false)
            val isDisabledloginButton = computed(fun(): Boolean {
                return !isAgreeProtocol.value
            }
            )
            fun gen_ChangeIsAgreeProtocol_fn() {
                isAgreeProtocol.value = !isAgreeProtocol.value
            }
            val ChangeIsAgreeProtocol = ::gen_ChangeIsAgreeProtocol_fn
            fun gen_onSubmit_fn(e: Any) {
                console.log("onSubmit:", e, " at pages/login/login.uvue:174")
            }
            val onSubmit = ::gen_onSubmit_fn
            val toastRef = ref<ComponentPublicInstance?>(null)
            val toastVisible = ref(false)
            val toastMessage = ref("")
            val toastType = ref<ToastType>("info")
            val toastBgColor = ref("")
            val toastTextColor = ref("")
            val toastIcon = ref("")
            val toastShowIcon = ref(true)
            val toastDuration = ref(3000)
            val toastPosition = ref<ToastPosition>("top")
            val toastTop = ref(100)
            fun gen_showToast_fn(options: {
                var message: String
                var type: ToastType?
                var backgroundColor: String?
                var textColor: String?
                var icon: String?
                var showIcon: Boolean?
                var duration: Number?
                var position: ToastPosition?
                var top: Number?
            }) {
                toastMessage.value = options.message
                toastType.value = options.type || "info"
                toastBgColor.value = options.backgroundColor || ""
                toastTextColor.value = options.textColor || ""
                toastIcon.value = options.icon || ""
                toastShowIcon.value = options.showIcon !== false
                toastDuration.value = options.duration || 3000
                toastPosition.value = options.position || "top"
                toastTop.value = options.top || 100
                toastVisible.value = true
            }
            val showToast = ::gen_showToast_fn
            fun gen_onToastClose_fn() {
                toastVisible.value = false
            }
            val onToastClose = ::gen_onToastClose_fn
            fun gen_showSuccessToast_fn() {
                showToast(object : UTSJSONObject() {
                    var message = "这是一条成功提示消息！"
                    var type = "success"
                    var icon = "✓"
                })
            }
            val showSuccessToast = ::gen_showSuccessToast_fn
            fun gen_showCustomToast_fn() {
                showToast(object : UTSJSONObject() {
                    var message = "自定义背景色"
                    var backgroundColor = "#6366f1"
                    var textColor = "#ffffff"
                    var icon = "🎨"
                })
            }
            val showCustomToast = ::gen_showCustomToast_fn
            fun gen_showIconToast_fn() {
                showToast(object : UTSJSONObject() {
                    var message = "带图标提示"
                    var type = "warning"
                    var icon = "⚠️"
                })
            }
            val showIconToast = ::gen_showIconToast_fn
            fun gen_showTimedToast_fn() {
                showToast(object : UTSJSONObject() {
                    var message = "设置显示时间"
                    var type = "info"
                    var icon = "⏰"
                    var duration: Number = 5000
                })
            }
            val showTimedToast = ::gen_showTimedToast_fn
            return fun(): Any? {
                val _component_block = resolveComponent("block")
                return createElementVNode("view", utsMapOf("class" to "login-container"), utsArrayOf(
                    createVNode(unref(GenComponentsUcUToastUToastClass), utsMapOf("ref_key" to "toastRef", "ref" to toastRef, "visible" to unref(toastVisible), "message" to unref(toastMessage), "type" to unref(toastType), "backgroundColor" to unref(toastBgColor), "textColor" to unref(toastTextColor), "icon" to unref(toastIcon), "showIcon" to unref(toastShowIcon), "duration" to unref(toastDuration), "position" to unref(toastPosition), "top" to unref(toastTop), "onClose" to onToastClose), null, 8, utsArrayOf(
                        "visible",
                        "message",
                        "type",
                        "backgroundColor",
                        "textColor",
                        "icon",
                        "showIcon",
                        "duration",
                        "position",
                        "top"
                    )),
                    createVNode(unref(GenComponentsFirstuiFuiStatusBarFuiStatusBarClass)),
                    createElementVNode("view", utsMapOf("class" to "gradient-circle top-right")),
                    createElementVNode("view", utsMapOf("class" to "gradient-circle bottom-left")),
                    createElementVNode("view", utsMapOf("class" to "form-container"), utsArrayOf(
                        createElementVNode("view", utsMapOf("class" to "logo-title"), utsArrayOf(
                            createElementVNode("text", utsMapOf("class" to "title-text"), "登录")
                        )),
                        createVNode(unref(GenComponentsFirstuiFuiTabsFuiTabsClass), utsMapOf("class" to "form-login-tabs", "tabs" to unref(tabsList), "short" to false, "center" to "", "current" to unref(current), "onChange" to handleChangeTab), null, 8, utsArrayOf(
                            "tabs",
                            "current"
                        )),
                        createElementVNode("view", utsMapOf("class" to "login-form-container"), utsArrayOf(
                            if (unref(current) === 0) {
                                createVNode(_component_block, utsMapOf("key" to 0), utsMapOf("default" to withSlotCtx(fun(): UTSArray<Any> {
                                    return utsArrayOf(
                                        createVNode(unref(GenComponentsUcUFormUFormClass), utsMapOf("ref_key" to "loginForm", "ref" to loginForm, "onSubmited" to onSubmit, "modelValue" to unref(phoneModel), "onUpdate:modelValue" to fun(`$event`: phoneModelType){
                                            trySetRefValue(phoneModel, `$event`)
                                        }), utsMapOf("default" to withSlotCtx(fun(): UTSArray<Any> {
                                            return utsArrayOf(
                                                createVNode(unref(GenComponentsUcUFromItemUFromItemClass), utsMapOf("field" to "phone", "rule" to unref(phoneRules), "marginTop" to 16), utsMapOf("default" to withSlotCtx(fun(): UTSArray<Any> {
                                                    return utsArrayOf(
                                                        createVNode(unref(GenComponentsFirstuiFuiInputFuiInputClass), utsMapOf("ref" to "phone", "onBlur" to fun(){
                                                            onBlur("phone")
                                                        }, "borderBottom" to false, "size" to 28, "radius" to 12, "type" to "number", "placeholder" to "请输入手机号", "modelValue" to unref(phoneModel).phone, "onUpdate:modelValue" to fun(`$event`: String){
                                                            unref(phoneModel).phone = `$event`
                                                        }), null, 8, utsArrayOf(
                                                            "onBlur",
                                                            "modelValue",
                                                            "onUpdate:modelValue"
                                                        ))
                                                    )
                                                }), "_" to 1), 8, utsArrayOf(
                                                    "rule"
                                                )),
                                                createVNode(unref(GenComponentsUcUFromItemUFromItemClass), utsMapOf("field" to "code", "rule" to unref(codeRules), "marginTop" to 16), utsMapOf("default" to withSlotCtx(fun(): UTSArray<Any> {
                                                    return utsArrayOf(
                                                        createVNode(unref(GenComponentsFirstuiFuiInputFuiInputClass), utsMapOf("ref" to "code", "onBlur" to fun(){
                                                            onBlur("code")
                                                        }, "borderBottom" to false, "size" to 28, "radius" to 12, "type" to "number", "placeholder" to "请输入验证码", "modelValue" to unref(phoneModel).code, "onUpdate:modelValue" to fun(`$event`: String){
                                                            unref(phoneModel).code = `$event`
                                                        }), null, 8, utsArrayOf(
                                                            "onBlur",
                                                            "modelValue",
                                                            "onUpdate:modelValue"
                                                        ))
                                                    )
                                                }), "_" to 1), 8, utsArrayOf(
                                                    "rule"
                                                ))
                                            )
                                        }), "_" to 1), 8, utsArrayOf(
                                            "modelValue"
                                        ))
                                    )
                                }), "_" to 1))
                            } else {
                                createVNode(_component_block, utsMapOf("key" to 1), utsMapOf("default" to withSlotCtx(fun(): UTSArray<Any> {
                                    return utsArrayOf(
                                        " 2 "
                                    )
                                }
                                ), "_" to 1))
                            }
                        )),
                        createElementVNode("view", utsMapOf("class" to "agreement"), utsArrayOf(
                            createVNode(unref(GenComponentsFirstuiFuiRowFuiRowClass), utsMapOf("justify" to "center", "class" to "agreement-row"), utsMapOf("default" to withSlotCtx(fun(): UTSArray<Any> {
                                return utsArrayOf(
                                    createVNode(unref(GenComponentsFirstuiFuiCheckboxFuiCheckboxClass), utsMapOf("class" to "agreement-checkbox", "checked" to unref(isAgreeProtocol), "borderRadius" to "8rpx", "scaleRatio" to 0.78, "onChange" to ChangeIsAgreeProtocol), null, 8, utsArrayOf(
                                        "checked"
                                    )),
                                    createElementVNode("text", utsMapOf("class" to "agreement-text"), "我已阅读并同意"),
                                    createElementVNode("text", utsMapOf("class" to "agreement-text agreement-link"), "《用户服务条款》"),
                                    createElementVNode("text", utsMapOf("class" to "agreement-text"), "和"),
                                    createElementVNode("text", utsMapOf("class" to "agreement-text agreement-link"), "《隐私协议》")
                                )
                            }
                            ), "_" to 1))
                        )),
                        createElementVNode("view", utsMapOf("class" to "test-buttons"), utsArrayOf(
                            createElementVNode("button", utsMapOf("class" to "test-btn success", "onClick" to showSuccessToast), "基础使用"),
                            createElementVNode("button", utsMapOf("class" to "test-btn custom", "onClick" to showCustomToast), "自定义背景色"),
                            createElementVNode("button", utsMapOf("class" to "test-btn icon", "onClick" to showIconToast), "带图标提示"),
                            createElementVNode("button", utsMapOf("class" to "test-btn time", "onClick" to showTimedToast), "设置显示时间")
                        ))
                    )),
                    createElementVNode("view", utsMapOf("class" to "footer"), utsArrayOf(
                        createVNode(unref(GenComponentsFirstuiFuiFooterFuiFooterClass), utsMapOf("text" to "by@海南长养乔智能科技有限责任公司"))
                    ))
                ))
            }
        }
        val styles: Map<String, Map<String, Map<String, Any>>> by lazy {
            normalizeCssStyles(utsArrayOf(
                styles0
            ), utsArrayOf(
                GenApp.styles
            ))
        }
        val styles0: Map<String, Map<String, Map<String, Any>>>
            get() {
                return utsMapOf("login-container" to padStyleMapOf(utsMapOf("height" to "100%", "display" to "flex", "flexDirection" to "column", "alignItems" to "center", "position" to "relative")), "gradient-circle" to padStyleMapOf(utsMapOf("position" to "absolute", "borderTopLeftRadius" to "200rpx", "borderTopRightRadius" to "200rpx", "borderBottomRightRadius" to "200rpx", "borderBottomLeftRadius" to "200rpx", "backgroundColor" to "#33a1fd", "zIndex" to 0, "fontSize::after" to 12, "position::after" to "absolute", "top::after" to 0, "left::after" to 0)), "top-right" to padStyleMapOf(utsMapOf("top" to "-80rpx", "right" to "-30rpx", "width" to "300rpx", "height" to "300rpx", "opacity" to 0.08)), "bottom-left" to padStyleMapOf(utsMapOf("bottom" to "-60rpx", "left" to "-60rpx", "width" to "280rpx", "height" to "280rpx", "opacity" to 0.1)), "logo-title" to padStyleMapOf(utsMapOf("textAlign" to "center", "marginTop" to "10rpx", "marginRight" to 0, "marginBottom" to "30rpx", "marginLeft" to 0)), "title-text" to padStyleMapOf(utsMapOf("fontSize" to "58rpx", "fontWeight" to "bold", "color" to "#465CFF")), "form-container" to padStyleMapOf(utsMapOf("display" to "flex", "justifyContent" to "center", "alignItems" to "center", "width" to "100%", "height" to "70%")), "login-form-container" to utsMapOf(".form-container " to utsMapOf("paddingTop" to 0, "paddingRight" to "16rpx", "paddingBottom" to 0, "paddingLeft" to "16rpx", "display" to "flex", "minWidth" to "580rpx")), "agreement" to padStyleMapOf(utsMapOf("display" to "flex", "alignItems" to "flex-start", "marginTop" to "24rpx", "color" to "#CCCCCC")), "agreement-row" to utsMapOf(".agreement " to utsMapOf("alignItems" to "center")), "agreement-text" to padStyleMapOf(utsMapOf("fontSize" to "24rpx")), "agreement-link" to padStyleMapOf(utsMapOf("color" to "#465CFF", "textDecoration" to "none")), "footer" to padStyleMapOf(utsMapOf("textAlign" to "center", "paddingTop" to "16rpx", "paddingRight" to "16rpx", "paddingBottom" to "16rpx", "paddingLeft" to "16rpx", "color" to "#CCCCCC", "fontSize" to "32rpx", "marginTop" to "20rpx")), "test-buttons" to padStyleMapOf(utsMapOf("display" to "flex", "flexWrap" to "wrap", "gap" to "16rpx", "marginTop" to "32rpx", "justifyContent" to "center")), "test-btn" to utsMapOf("" to utsMapOf("paddingTop" to "16rpx", "paddingRight" to "24rpx", "paddingBottom" to "16rpx", "paddingLeft" to "24rpx", "borderTopLeftRadius" to "8rpx", "borderTopRightRadius" to "8rpx", "borderBottomRightRadius" to "8rpx", "borderBottomLeftRadius" to "8rpx", "borderTopWidth" to "medium", "borderRightWidth" to "medium", "borderBottomWidth" to "medium", "borderLeftWidth" to "medium", "borderTopStyle" to "none", "borderRightStyle" to "none", "borderBottomStyle" to "none", "borderLeftStyle" to "none", "borderTopColor" to "#000000", "borderRightColor" to "#000000", "borderBottomColor" to "#000000", "borderLeftColor" to "#000000", "fontSize" to "24rpx", "color" to "#FFFFFF", "cursor" to "pointer", "transitionDuration" to "0.3s", "transitionTimingFunction" to "ease", "minWidth" to "120rpx"), ".success" to utsMapOf("backgroundColor" to "#52c41a"), ".success:hover" to utsMapOf("backgroundColor" to "#73d13d"), ".custom" to utsMapOf("backgroundColor" to "#6366f1"), ".custom:hover" to utsMapOf("backgroundColor" to "#818cf8"), ".icon" to utsMapOf("backgroundColor" to "#faad14"), ".icon:hover" to utsMapOf("backgroundColor" to "#ffc53d"), ".time" to utsMapOf("backgroundColor" to "#1890ff"), ".time:hover" to utsMapOf("backgroundColor" to "#40a9ff")), "@TRANSITION" to utsMapOf("test-btn" to utsMapOf("duration" to "0.3s", "timingFunction" to "ease")))
            }
        var inheritAttrs = true
        var inject: Map<String, Map<String, Any?>> = utsMapOf()
        var emits: Map<String, Any?> = utsMapOf()
        var props = normalizePropsOptions(utsMapOf())
        var propsNeedCastKeys: UTSArray<String> = utsArrayOf()
        var components: Map<String, CreateVueComponent> = utsMapOf()
    }
}
