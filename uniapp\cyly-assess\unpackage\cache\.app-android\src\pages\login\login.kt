@file:Suppress("UNCHECKED_CAST", "USELESS_CAST", "INAPPLICABLE_JVM_NAME", "UNUSED_ANONYMOUS_PARAMETER", "NAME_SHADOWING", "UNNECESSARY_NOT_NULL_ASSERTION")
package uni.UNI4AABA03
import io.dcloud.uniapp.*
import io.dcloud.uniapp.extapi.*
import io.dcloud.uniapp.framework.*
import io.dcloud.uniapp.runtime.*
import io.dcloud.uniapp.vue.*
import io.dcloud.uniapp.vue.shared.*
import io.dcloud.unicloud.*
import io.dcloud.uts.*
import io.dcloud.uts.Map
import io.dcloud.uts.Set
import io.dcloud.uts.UTSAndroid
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
open class GenPagesLoginLogin : BasePage {
    constructor(__ins: ComponentInternalInstance, __renderer: String?) : super(__ins, __renderer) {}
    companion object {
        @Suppress("UNUSED_PARAMETER", "UNUSED_VARIABLE")
        var setup: (__props: GenPagesLoginLogin) -> Any? = fun(__props): Any? {
            val __ins = getCurrentInstance()!!
            val _ctx = __ins.proxy as GenPagesLoginLogin
            val _cache = __ins.renderCache
            val inputStyle: UTSJSONObject = object : UTSJSONObject(UTSSourceMapPosition("inputStyle", "pages/login/login.uvue", 78, 8)) {
                var inputBorder = true
                var size: Number = 28
                var radius: Number = 12
                var marginTop: Number = 16
                var type = "number"
            }
            val instance = getCurrentInstance()
            val loginForm = ref<ComponentPublicInstance?>(null)
            fun gen_onBlur_fn(field: String) {
                console.log("field:", field, " at pages/login/login.uvue:105")
                val f = loginForm.value
                if (f != null) {
                    f.`$callMethod`("validItem", field, FormValidResultItem(success = fun() {
                        console.log("success", " at pages/login/login.uvue:111")
                    }
                    , fail = fun(res) {
                        console.log("fail:", res, " at pages/login/login.uvue:114")
                    }
                    ))
                }
            }
            val onBlur = ::gen_onBlur_fn
            val tabsList = ref(utsArrayOf(
                object : UTSJSONObject() {
                    var name = "手机号登录"
                    var id: Number = 0
                },
                object : UTSJSONObject() {
                    var name = "账号登录"
                    var id: Number = 1
                }
            ))
            val current = ref(0)
            fun gen_handleChangeTab_fn(e: FuiTabsItemParam) {
                console.log("handleChangeTab:", e, " at pages/login/login.uvue:128")
                if (e.index != null) {
                    current.value = e.index as Number
                }
            }
            val handleChangeTab = ::gen_handleChangeTab_fn
            val phoneModel = reactive<phoneModelType>(phoneModelType(phone = "", code = ""))
            val phoneRules = ref<FormItemRule>(FormItemRule(type = "phone", required = true, message = "请输入正确的手机号"))
            val codeRules = ref<FormItemRule>(FormItemRule(type = "number", required = true, message = "请输入验证码"))
            val countryCode = ref("86")
            val codeText = ref("获取验证码")
            val canGetCode = ref(true)
            val accountForm = reactive(object : UTSJSONObject() {
                var username = ""
                var password = ""
            })
            val isAgreeProtocol = ref(false)
            val isDisabledloginButton = computed(fun(): Boolean {
                return !isAgreeProtocol.value
            }
            )
            fun gen_ChangeIsAgreeProtocol_fn() {
                isAgreeProtocol.value = !isAgreeProtocol.value
            }
            val ChangeIsAgreeProtocol = ::gen_ChangeIsAgreeProtocol_fn
            fun gen_onSubmit_fn(e: Any) {
                console.log("onSubmit:", e, " at pages/login/login.uvue:173")
            }
            val onSubmit = ::gen_onSubmit_fn
            val toastRef = ref<ComponentPublicInstance?>(null)
            val toastVisible = ref(false)
            val toastMessage = ref("")
            val toastType = ref<ToastType>("info")
            val toastIcon = ref("")
            val toastIconColor = ref("")
            fun gen_showToast_fn(options: ShowToastOptions1) {
                toastMessage.value = options.message
                val optionType = options.type
                toastType.value = if (optionType != null) {
                    optionType
                } else {
                    "info"
                }
                val optionIcon = options.icon
                toastIcon.value = if (optionIcon != null) {
                    optionIcon
                } else {
                    ""
                }
                val optionIconColor = options.iconColor
                toastIconColor.value = if (optionIconColor != null) {
                    optionIconColor
                } else {
                    ""
                }
                toastVisible.value = true
            }
            val showToast = ::gen_showToast_fn
            fun gen_onToastClose_fn() {
                toastVisible.value = false
            }
            val onToastClose = ::gen_onToastClose_fn
            fun gen_testValidation_fn() {
                val formInstance = loginForm.value
                if (formInstance != null) {
                    formInstance.`$callMethod`("valid", object : UTSJSONObject() {
                        var success = fun(){
                            console.log("表单验证成功", " at pages/login/login.uvue:216")
                        }
                        var fail = fun(failResults: UTSArray<FormItemVerifyResult>){
                            console.log("表单验证失败:", failResults, " at pages/login/login.uvue:219")
                        }
                    })
                }
            }
            val testValidation = ::gen_testValidation_fn
            fun gen_testSvgToast_fn() {
                val options = ShowToastOptions1(message = "这是成功提示！", type = "success", icon = "✓")
                showToast(options)
            }
            val testSvgToast = ::gen_testSvgToast_fn
            fun gen_testEmojiToast_fn() {
                val options = ShowToastOptions1(message = "这是使用 Emoji 图标的提示！", type = "warning", icon = "🎉")
                showToast(options)
            }
            val testEmojiToast = ::gen_testEmojiToast_fn
            return fun(): Any? {
                val _component_block = resolveComponent("block")
                return createElementVNode("view", utsMapOf("class" to "login-container"), utsArrayOf(
                    createVNode(unref(GenComponentsUcUToastUToastClass), utsMapOf("ref_key" to "toastRef", "ref" to toastRef, "visible" to unref(toastVisible), "message" to unref(toastMessage), "type" to unref(toastType), "icon" to unref(toastIcon), "iconColor" to unref(toastIconColor), "duration" to 4000, "position" to "top", "top" to 120, "onClose" to onToastClose), null, 8, utsArrayOf(
                        "visible",
                        "message",
                        "type",
                        "icon",
                        "iconColor"
                    )),
                    createVNode(unref(GenComponentsFirstuiFuiStatusBarFuiStatusBarClass)),
                    createElementVNode("view", utsMapOf("class" to "gradient-circle top-right")),
                    createElementVNode("view", utsMapOf("class" to "gradient-circle bottom-left")),
                    createElementVNode("view", utsMapOf("class" to "form-container"), utsArrayOf(
                        createElementVNode("view", utsMapOf("class" to "logo-title"), utsArrayOf(
                            createElementVNode("text", utsMapOf("class" to "title-text"), "登录")
                        )),
                        createVNode(unref(GenComponentsFirstuiFuiTabsFuiTabsClass), utsMapOf("class" to "form-login-tabs", "tabs" to unref(tabsList), "short" to false, "center" to "", "current" to unref(current), "onChange" to handleChangeTab), null, 8, utsArrayOf(
                            "tabs",
                            "current"
                        )),
                        createElementVNode("view", utsMapOf("class" to "login-form-container"), utsArrayOf(
                            if (unref(current) === 0) {
                                createVNode(_component_block, utsMapOf("key" to 0), utsMapOf("default" to withSlotCtx(fun(): UTSArray<Any> {
                                    return utsArrayOf(
                                        createVNode(unref(GenComponentsUcUFormUFormClass), utsMapOf("ref_key" to "loginForm", "ref" to loginForm, "onSubmited" to onSubmit, "modelValue" to unref(phoneModel), "onUpdate:modelValue" to fun(`$event`: phoneModelType){
                                            trySetRefValue(phoneModel, `$event`)
                                        }, "showToast" to true, "toastTop" to 120, "toastDuration" to 4000), utsMapOf("default" to withSlotCtx(fun(): UTSArray<Any> {
                                            return utsArrayOf(
                                                createVNode(unref(GenComponentsUcUFromItemUFromItemClass), utsMapOf("field" to "phone", "rule" to unref(phoneRules), "marginTop" to 16), utsMapOf("default" to withSlotCtx(fun(): UTSArray<Any> {
                                                    return utsArrayOf(
                                                        createVNode(unref(GenComponentsFirstuiFuiInputFuiInputClass), utsMapOf("ref" to "phone", "onBlur" to fun(){
                                                            onBlur("phone")
                                                        }, "borderBottom" to false, "size" to 28, "radius" to 12, "type" to "number", "placeholder" to "请输入手机号", "modelValue" to unref(phoneModel).phone, "onUpdate:modelValue" to fun(`$event`: String){
                                                            unref(phoneModel).phone = `$event`
                                                        }), null, 8, utsArrayOf(
                                                            "onBlur",
                                                            "modelValue",
                                                            "onUpdate:modelValue"
                                                        ))
                                                    )
                                                }), "_" to 1), 8, utsArrayOf(
                                                    "rule"
                                                )),
                                                createVNode(unref(GenComponentsUcUFromItemUFromItemClass), utsMapOf("field" to "code", "rule" to unref(codeRules), "marginTop" to 16), utsMapOf("default" to withSlotCtx(fun(): UTSArray<Any> {
                                                    return utsArrayOf(
                                                        createVNode(unref(GenComponentsFirstuiFuiInputFuiInputClass), utsMapOf("ref" to "code", "onBlur" to fun(){
                                                            onBlur("code")
                                                        }, "borderBottom" to false, "size" to 28, "radius" to 12, "type" to "number", "placeholder" to "请输入验证码", "modelValue" to unref(phoneModel).code, "onUpdate:modelValue" to fun(`$event`: String){
                                                            unref(phoneModel).code = `$event`
                                                        }), null, 8, utsArrayOf(
                                                            "onBlur",
                                                            "modelValue",
                                                            "onUpdate:modelValue"
                                                        ))
                                                    )
                                                }), "_" to 1), 8, utsArrayOf(
                                                    "rule"
                                                ))
                                            )
                                        }), "_" to 1), 8, utsArrayOf(
                                            "modelValue"
                                        ))
                                    )
                                }), "_" to 1))
                            } else {
                                createVNode(_component_block, utsMapOf("key" to 1), utsMapOf("default" to withSlotCtx(fun(): UTSArray<Any> {
                                    return utsArrayOf(
                                        " 2 "
                                    )
                                }
                                ), "_" to 1))
                            }
                        )),
                        createElementVNode("view", utsMapOf("class" to "agreement"), utsArrayOf(
                            createVNode(unref(GenComponentsFirstuiFuiRowFuiRowClass), utsMapOf("justify" to "center", "class" to "agreement-row"), utsMapOf("default" to withSlotCtx(fun(): UTSArray<Any> {
                                return utsArrayOf(
                                    createVNode(unref(GenComponentsFirstuiFuiCheckboxFuiCheckboxClass), utsMapOf("class" to "agreement-checkbox", "checked" to unref(isAgreeProtocol), "borderRadius" to "8rpx", "scaleRatio" to 0.78, "onChange" to ChangeIsAgreeProtocol), null, 8, utsArrayOf(
                                        "checked"
                                    )),
                                    createElementVNode("text", utsMapOf("class" to "agreement-text"), "我已阅读并同意"),
                                    createElementVNode("text", utsMapOf("class" to "agreement-text agreement-link"), "《用户服务条款》"),
                                    createElementVNode("text", utsMapOf("class" to "agreement-text"), "和"),
                                    createElementVNode("text", utsMapOf("class" to "agreement-text agreement-link"), "《隐私协议》")
                                )
                            }
                            ), "_" to 1))
                        )),
                        createElementVNode("view", utsMapOf("class" to "test-buttons"), utsArrayOf(
                            createElementVNode("button", utsMapOf("class" to "test-btn validate", "onClick" to testValidation), "测试验证"),
                            createElementVNode("button", utsMapOf("class" to "test-btn svg", "onClick" to testSvgToast), "成功提示"),
                            createElementVNode("button", utsMapOf("class" to "test-btn emoji", "onClick" to testEmojiToast), "Emoji图标")
                        ))
                    )),
                    createElementVNode("view", utsMapOf("class" to "footer"), utsArrayOf(
                        createVNode(unref(GenComponentsFirstuiFuiFooterFuiFooterClass), utsMapOf("text" to "by@海南长养乔智能科技有限责任公司"))
                    ))
                ))
            }
        }
        val styles: Map<String, Map<String, Map<String, Any>>> by lazy {
            normalizeCssStyles(utsArrayOf(
                styles0
            ), utsArrayOf(
                GenApp.styles
            ))
        }
        val styles0: Map<String, Map<String, Map<String, Any>>>
            get() {
                return utsMapOf("login-container" to padStyleMapOf(utsMapOf("height" to "100%", "display" to "flex", "flexDirection" to "column", "alignItems" to "center", "position" to "relative")), "gradient-circle" to padStyleMapOf(utsMapOf("position" to "absolute", "borderTopLeftRadius" to "200rpx", "borderTopRightRadius" to "200rpx", "borderBottomRightRadius" to "200rpx", "borderBottomLeftRadius" to "200rpx", "backgroundColor" to "#33a1fd", "zIndex" to 0, "fontSize::after" to 12, "position::after" to "absolute", "top::after" to 0, "left::after" to 0)), "top-right" to padStyleMapOf(utsMapOf("top" to "-80rpx", "right" to "-30rpx", "width" to "300rpx", "height" to "300rpx", "opacity" to 0.08)), "bottom-left" to padStyleMapOf(utsMapOf("bottom" to "-60rpx", "left" to "-60rpx", "width" to "280rpx", "height" to "280rpx", "opacity" to 0.1)), "logo-title" to padStyleMapOf(utsMapOf("textAlign" to "center", "marginTop" to "10rpx", "marginRight" to 0, "marginBottom" to "30rpx", "marginLeft" to 0)), "title-text" to padStyleMapOf(utsMapOf("fontSize" to "58rpx", "fontWeight" to "bold", "color" to "#465CFF")), "form-container" to padStyleMapOf(utsMapOf("display" to "flex", "justifyContent" to "center", "alignItems" to "center", "width" to "100%", "height" to "70%")), "login-form-container" to utsMapOf(".form-container " to utsMapOf("paddingTop" to 0, "paddingRight" to "16rpx", "paddingBottom" to 0, "paddingLeft" to "16rpx", "display" to "flex", "minWidth" to "580rpx")), "agreement" to padStyleMapOf(utsMapOf("display" to "flex", "alignItems" to "flex-start", "marginTop" to "24rpx", "color" to "#CCCCCC")), "agreement-row" to utsMapOf(".agreement " to utsMapOf("alignItems" to "center")), "agreement-text" to padStyleMapOf(utsMapOf("fontSize" to "24rpx")), "agreement-link" to padStyleMapOf(utsMapOf("color" to "#465CFF", "textDecoration" to "none")), "footer" to padStyleMapOf(utsMapOf("textAlign" to "center", "paddingTop" to "16rpx", "paddingRight" to "16rpx", "paddingBottom" to "16rpx", "paddingLeft" to "16rpx", "color" to "#CCCCCC", "fontSize" to "32rpx", "marginTop" to "20rpx")), "test-buttons" to padStyleMapOf(utsMapOf("display" to "flex", "flexWrap" to "wrap", "gap" to "16rpx", "marginTop" to "32rpx", "justifyContent" to "center")), "test-btn" to utsMapOf("" to utsMapOf("paddingTop" to "16rpx", "paddingRight" to "24rpx", "paddingBottom" to "16rpx", "paddingLeft" to "24rpx", "borderTopLeftRadius" to "8rpx", "borderTopRightRadius" to "8rpx", "borderBottomRightRadius" to "8rpx", "borderBottomLeftRadius" to "8rpx", "borderTopWidth" to "medium", "borderRightWidth" to "medium", "borderBottomWidth" to "medium", "borderLeftWidth" to "medium", "borderTopStyle" to "none", "borderRightStyle" to "none", "borderBottomStyle" to "none", "borderLeftStyle" to "none", "borderTopColor" to "#000000", "borderRightColor" to "#000000", "borderBottomColor" to "#000000", "borderLeftColor" to "#000000", "fontSize" to "24rpx", "color" to "#FFFFFF", "cursor" to "pointer", "transitionDuration" to "0.3s", "transitionTimingFunction" to "ease", "minWidth" to "120rpx"), ".validate" to utsMapOf("backgroundColor" to "#ff4d4f"), ".validate:hover" to utsMapOf("backgroundColor" to "#ff7875"), ".svg" to utsMapOf("backgroundColor" to "#52c41a"), ".svg:hover" to utsMapOf("backgroundColor" to "#73d13d"), ".emoji" to utsMapOf("backgroundColor" to "#faad14"), ".emoji:hover" to utsMapOf("backgroundColor" to "#ffc53d")), "@TRANSITION" to utsMapOf("test-btn" to utsMapOf("duration" to "0.3s", "timingFunction" to "ease")))
            }
        var inheritAttrs = true
        var inject: Map<String, Map<String, Any?>> = utsMapOf()
        var emits: Map<String, Any?> = utsMapOf()
        var props = normalizePropsOptions(utsMapOf())
        var propsNeedCastKeys: UTSArray<String> = utsArrayOf()
        var components: Map<String, CreateVueComponent> = utsMapOf()
    }
}
