@file:Suppress("UNCHECKED_CAST", "USELESS_CAST", "INAPPLICABLE_JVM_NAME", "UNUSED_ANONYMOUS_PARAMETER", "NAME_SHADOWING", "UNNECESSARY_NOT_NULL_ASSERTION")
package uni.UNI4AABA03
import io.dcloud.uniapp.*
import io.dcloud.uniapp.extapi.*
import io.dcloud.uniapp.framework.*
import io.dcloud.uniapp.runtime.*
import io.dcloud.uniapp.vue.*
import io.dcloud.uniapp.vue.shared.*
import io.dcloud.unicloud.*
import io.dcloud.uts.*
import io.dcloud.uts.Map
import io.dcloud.uts.Set
import io.dcloud.uts.UTSAndroid
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
open class GenComponentsUcUFormUForm : VueComponent {
    constructor(__ins: ComponentInternalInstance) : super(__ins) {}
    open var customStyle: UTSJSONObject by `$props`
    open var triggerChange: Boolean by `$props`
    open var showToast: Boolean by `$props`
    open var toastPosition: String by `$props`
    open var toastTop: Number by `$props`
    open var toastDuration: Number by `$props`
    open var modelValue: UTSJSONObject by `$props`
    open var onSubmit: (callback: FormValidResult) -> Unit
        get() {
            return unref(this.`$exposed`["onSubmit"]) as (callback: FormValidResult) -> Unit
        }
        set(value) {
            setRefValue(this.`$exposed`, "onSubmit", value)
        }
    open var pushFielditem: (fieldItem: FormItemData) -> Unit
        get() {
            return unref(this.`$exposed`["pushFielditem"]) as (fieldItem: FormItemData) -> Unit
        }
        set(value) {
            setRefValue(this.`$exposed`, "pushFielditem", value)
        }
    open var validItem: (field: String, callback: FormValidResultItem) -> Unit
        get() {
            return unref(this.`$exposed`["validItem"]) as (field: String, callback: FormValidResultItem) -> Unit
        }
        set(value) {
            setRefValue(this.`$exposed`, "validItem", value)
        }
    open var valid: (callback: FormValidResult) -> Unit
        get() {
            return unref(this.`$exposed`["valid"]) as (callback: FormValidResult) -> Unit
        }
        set(value) {
            setRefValue(this.`$exposed`, "valid", value)
        }
    companion object {
        @Suppress("UNUSED_PARAMETER", "UNUSED_VARIABLE")
        var setup: (__props: GenComponentsUcUFormUForm, _arg1: SetupContext) -> Any? = fun(__props, ref1): Any? {
            var __expose = ref1.expose
            val __ins = getCurrentInstance()!!
            val _ctx = __ins.proxy as GenComponentsUcUFormUForm
            val _cache = __ins.renderCache
            val props = __props
            val fieldItems = ref(utsArrayOf<FormItemData>())
            fun emit(event: String, vararg do_not_transform_spread: Any?) {
                __ins.emit(event, *do_not_transform_spread)
            }
            val model = useModel<UTSJSONObject>(__ins.props, "modelValue")
            val toastRef = ref<ComponentPublicInstance?>(null)
            val toastVisible = ref(false)
            val toastMessage = ref("")
            val toastType = ref<ToastType>("error")
            val toastIcon = ref("✕")
            fun gen_showErrorToast_fn(message: String) {
                if (!props.showToast) {
                    return
                }
                toastMessage.value = message
                toastType.value = "error"
                toastIcon.value = "✕"
                toastVisible.value = true
            }
            val showErrorToast = ::gen_showErrorToast_fn
            fun gen_onToastClose_fn() {
                toastVisible.value = false
            }
            val onToastClose = ::gen_onToastClose_fn
            fun gen_valid_fn(callback: FormValidResult) {
                var allow = true
                var verifyRes = utsArrayOf<FormItemVerifyResult>()
                fieldItems.value.forEach(fun(item: FormItemData){
                    var field = item.field
                    var value = (model.value as UTSJSONObject)[field]
                    if (value != null) {
                        var _item = fieldItems.value.find(fun(it: FormItemData): Boolean {
                            return it.field == field
                        })
                        if (_item != null) {
                            item.instance.`$callMethod`("verify", value, fun(res: FormItemVerifyResult){
                                if (!res.valid) {
                                    allow = false
                                    verifyRes.push(res)
                                }
                            }
                            )
                        }
                    } else {
                        allow = false
                    }
                }
                )
                if (allow) {
                    if (callback.success != null) {
                        callback.success!!()
                    }
                } else {
                    if (verifyRes.length > 0) {
                        val errorMessage = verifyRes[0].message
                        showErrorToast(if (errorMessage != null) {
                            errorMessage
                        } else {
                            "验证失败"
                        }
                        )
                    }
                    if (callback.fail != null) {
                        callback.fail!!(verifyRes)
                    }
                }
            }
            val valid = ::gen_valid_fn
            fun gen_onSubmit_fn(callback: FormValidResult) {
                valid(FormValidResult(success = fun(){
                    if (callback.success != null) {
                        emit("submited")
                        callback.success!!()
                    }
                }
                , fail = fun(failResults: UTSArray<FormItemVerifyResult>){
                    if (callback.fail != null) {
                        callback.fail!!(failResults)
                    }
                }
                ))
            }
            val onSubmit = ::gen_onSubmit_fn
            fun gen_pushFielditem_fn(fieldItem: FormItemData) {
                if (fieldItems.value.find(fun(item: FormItemData): Boolean {
                    return item.field == fieldItem.field
                }
                ) != null) {
                    return
                }
                console.log(fieldItem, " at components/uc/u-form/u-form.uvue:132")
                fieldItems.value.push(fieldItem)
                console.log("pushFielditem-fieldItems:", fieldItems.value.map(fun(v): String {
                    return v.field
                }
                ), " at components/uc/u-form/u-form.uvue:134")
            }
            val pushFielditem = ::gen_pushFielditem_fn
            fun gen_validItem_fn(field: String, callback: FormValidResultItem) {
                val fieldItem = fieldItems.value.find(fun(item): Boolean {
                    return item.field == field
                }
                )
                console.log("===fieldItems===:", fieldItems, " at components/uc/u-form/u-form.uvue:138")
                console.log("model:", model, " at components/uc/u-form/u-form.uvue:139")
                val value = (model.value as UTSObject)[field]
                if (fieldItem != null) {
                    fieldItem.instance.`$callMethod`("verify", value, fun(res: FormItemVerifyResult){
                        if (!res.valid) {
                            val errorMessage = res.message
                            showErrorToast(if (errorMessage != null) {
                                errorMessage
                            } else {
                                "验证失败"
                            }
                            )
                            if (callback.fail != null) {
                                callback.fail!!(res)
                            }
                            return
                        }
                        if (callback.success != null) {
                            callback.success!!()
                        }
                    })
                } else {
                    val errorMsg = "\u5B57\u6BB5 " + field + " \u4E0D\u5B58\u5728"
                    showErrorToast(errorMsg)
                    if (callback.fail != null) {
                        callback.fail!!(FormItemVerifyResult(valid = false, message = errorMsg, field = field))
                    }
                }
            }
            val validItem = ::gen_validItem_fn
            watch(model, fun(value: UTSJSONObject){
                emit("change", value)
                if (props.triggerChange) {
                    valid(FormValidResult(success = fun() {
                        console.log("?", " at components/uc/u-form/u-form.uvue:176")
                    }
                    , fail = fun(res) {
                        console.log("???", res, " at components/uc/u-form/u-form.uvue:179")
                    }
                    ))
                }
            }
            , WatchOptions(deep = true))
            __expose(utsMapOf("onSubmit" to onSubmit, "pushFielditem" to pushFielditem, "validItem" to validItem, "valid" to valid))
            return fun(): Any? {
                return createElementVNode("view", utsMapOf("class" to "u-form", "style" to normalizeStyle(utsArrayOf(
                    _ctx.customStyle
                ))), utsArrayOf(
                    createVNode(unref(GenComponentsUcUToastUToastClass), utsMapOf("ref_key" to "toastRef", "ref" to toastRef, "visible" to unref(toastVisible), "message" to unref(toastMessage), "type" to unref(toastType), "icon" to unref(toastIcon), "duration" to _ctx.toastDuration, "position" to _ctx.toastPosition, "top" to _ctx.toastTop, "onClose" to onToastClose), null, 8, utsArrayOf(
                        "visible",
                        "message",
                        "type",
                        "icon",
                        "duration",
                        "position",
                        "top"
                    )),
                    renderSlot(_ctx.`$slots`, "default")
                ), 4)
            }
        }
        var name = "u-form"
        val styles: Map<String, Map<String, Map<String, Any>>> by lazy {
            normalizeCssStyles(utsArrayOf())
        }
        var inheritAttrs = true
        var inject: Map<String, Map<String, Any?>> = utsMapOf()
        var emits: Map<String, Any?> = utsMapOf("submited" to null, "change" to null, "focus" to null, "update:modelValue" to null)
        var props = normalizePropsOptions(utsMapOf("customStyle" to utsMapOf("type" to "Object", "default" to UTSJSONObject() as UTSJSONObject), "triggerChange" to utsMapOf("type" to "Boolean", "default" to false), "showToast" to utsMapOf("type" to "Boolean", "default" to true), "toastPosition" to utsMapOf("type" to "String", "default" to "top" as ToastPosition), "toastTop" to utsMapOf("type" to "Number", "default" to 100), "toastDuration" to utsMapOf("type" to "Number", "default" to 3000), "modelValue" to utsMapOf("type" to "Object", "default" to UTSJSONObject())))
        var propsNeedCastKeys = utsArrayOf(
            "customStyle",
            "triggerChange",
            "showToast",
            "toastPosition",
            "toastTop",
            "toastDuration",
            "modelValue"
        )
        var components: Map<String, CreateVueComponent> = utsMapOf()
    }
}
