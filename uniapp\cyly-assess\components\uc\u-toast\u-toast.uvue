<template>
  <view class="u-toast-container" v-if="visible" :style="containerStyle">
    <view class="u-toast" :class="toastClass" :style="toastStyle">
      <!-- 图标 -->
      <view class="u-toast-icon" v-if="showIcon">
        <!-- 文字图标 -->
        <text class="u-toast-icon-text" v-if="iconType === 'text'">{{ iconValue }}</text>
        <!-- 自定义图标插槽 -->
        <view class="u-toast-icon-custom" v-else-if="iconType === 'custom'">
          <slot name="icon"></slot>
        </view>
      </view>

      <!-- 消息内容 -->
      <view class="u-toast-content">
        <text class="u-toast-message" :style="messageStyle">{{ message }}</text>
      </view>
    </view>
  </view>
</template>

<script lang="uts" setup>
  import { ToastType, ToastPosition } from "../types"
  // import lSvg from "@/uni_modules/lime-svg/components/l-svg/l-svg"

  // Props 定义
  const props = defineProps({
    // 是否显示
    visible: {
      type: Boolean,
      default: false
    },
    // 消息内容
    message: {
      type: String,
      default: ''
    },
    // 提示类型
    type: {
      type: String as PropType<ToastType>,
      default: 'info' as ToastType
    },
    // 自定义背景色
    backgroundColor: {
      type: String,
      default: ''
    },
    // 自定义文字颜色
    textColor: {
      type: String,
      default: ''
    },
    // 图标
    icon: {
      type: String,
      default: ''
    },
    // 图标颜色（用于 SVG 图标）
    iconColor: {
      type: String,
      default: ''
    },
    // 是否显示图标
    showIcon: {
      type: Boolean,
      default: true
    },
    // 自动关闭时间（毫秒）
    duration: {
      type: Number,
      default: 3000
    },
    // 位置
    position: {
      type: String as PropType<ToastPosition>,
      default: 'top' as ToastPosition
    },
    // 距离顶部的距离
    top: {
      type: Number,
      default: 0
    },
    // 自定义样式
    customStyle: {
      type: Object as PropType<UTSJSONObject>,
      default: {} as UTSJSONObject
    }
  })

  // Emits 定义
  const emit = defineEmits(['close', 'click'])

  // 响应式数据
  const visible = ref(props.visible)
  const timer = ref<number | null>(null)

  // 计算属性 - 获取图标值
  const iconValue = computed(() => {
    if (props.icon !== '') return props.icon

    // 根据类型返回默认图标
    switch (props.type) {
      case 'success':
        return '✓'
      case 'error':
        return '✕'
      case 'warning':
        return '⚠'
      case 'info':
        return 'ℹ'
      default:
        return '●'
    }
  })

  // 计算属性 - 图标类型
  const iconType = computed(() => {
    const value = iconValue.value
    if (value === '') return 'none'
    // 检查是否是单个字符（emoji 或文字图标）
    if (value.length <= 2 || /^[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/u.test(value)) return 'text'
    return 'custom'
  })

  const toastClass = computed(() => {
    const classes = ['u-toast-' + props.type]
    if (props.position === 'top') classes.push('u-toast-top')
    if (props.position === 'bottom') classes.push('u-toast-bottom')
    return classes.join(' ')
  })

  const containerStyle = computed(() => {
    const style: UTSJSONObject = {}
    const topValue = props.top != null ? props.top : 0
    if (props?.position === 'top') {
      style['top'] = topValue + 'rpx'
    } else if (props?.position === 'bottom') {
      style['bottom'] = topValue + 'rpx'
    }
    return style
  })

  const toastStyle = computed(() => {
    const style: UTSJSONObject = {}
    if (props.backgroundColor != null && props.backgroundColor !== '') {
      style['background-color'] = props.backgroundColor
    }
    // 合并自定义样式
    for (const key in props.customStyle) {
      style[key] = props.customStyle[key]
    }
    return style
  })

  const messageStyle = computed(() => {
    const style: UTSJSONObject = {}
    if (props.textColor != null && props.textColor !== '') {
      style['color'] = props.textColor
    }
    return style
  })

  // 方法 - 先声明 clearTimer
  function clearTimer() {
    const timerId = timer.value
    if (timerId != null) {
      clearTimeout(timerId)
      timer.value = null
    }
  }

  function hide() {
    visible.value = false
    clearTimer()
    emit('close')
  }

  function show() {
    visible.value = true
    if (props.duration > 0) {
      clearTimer()
      timer.value = setTimeout(() => {
        hide()
      }, props.duration)
    }
  }

  // 监听 props.visible 变化
  watchEffect(() => {
    if (props.visible) {
      show()
    } else {
      hide()
    }
  })

  // 组件卸载时清理定时器
  onUnmounted(() => {
    clearTimer()
  })

  // 暴露方法
  defineExpose({
    show,
    hide
  })
</script>

<style lang="scss" scoped>
.u-toast-container {
  position: fixed;
  left: 0;
  right: 0;
  z-index: 9999;
  pointer-events: none;
  display: flex;
  justify-content: center;
  padding: 0 32rpx;
}

.u-toast {
  display: flex;
  align-items: center;
  min-height: 80rpx;
  padding: 16rpx 24rpx;
  border-radius: 40rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
  backdrop-filter: blur(20rpx);
  pointer-events: auto;
  max-width: 85%;
  word-wrap: break-word;
  border: 1rpx solid rgba(255, 255, 255, 0.2);

  /* 动画效果 */
  animation: toastSlideIn 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);

  /* 添加渐变光泽效果 */
  position: relative;
  overflow: hidden;
}

/* 光泽效果 */
.u-toast::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  animation: shine 2s ease-in-out infinite;
}

@keyframes shine {
  0% {
    left: -100%;
  }

  50% {
    left: 100%;
  }

  100% {
    left: 100%;
  }
}

.u-toast-top {
  transform-origin: top center;
}

.u-toast-bottom {
  transform-origin: bottom center;
}

/* 不同类型的默认样式 */
.u-toast-success {
  background: linear-gradient(135deg, rgba(82, 196, 26, 0.95) 0%, rgba(115, 209, 61, 0.95) 100%);
  color: white;
  border-color: rgba(82, 196, 26, 0.3);
}

.u-toast-error {
  background: linear-gradient(135deg, rgba(255, 77, 79, 0.95) 0%, rgba(255, 120, 117, 0.95) 100%);
  color: white;
  border-color: rgba(255, 77, 79, 0.3);
}

.u-toast-warning {
  background: linear-gradient(135deg, rgba(250, 173, 20, 0.95) 0%, rgba(255, 197, 61, 0.95) 100%);
  color: white;
  border-color: rgba(250, 173, 20, 0.3);
}

.u-toast-info {
  background: linear-gradient(135deg, rgba(24, 144, 255, 0.95) 0%, rgba(64, 169, 255, 0.95) 100%);
  color: white;
  border-color: rgba(24, 144, 255, 0.3);
}

.u-toast-default {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.85) 0%, rgba(64, 64, 64, 0.85) 100%);
  color: white;
  border-color: rgba(255, 255, 255, 0.1);
}

.u-toast-icon {
  margin-right: 12rpx;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(8rpx);
}

.u-toast-icon-text {
  font-size: 24rpx;
  line-height: 1;
  font-weight: bold;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
}

.u-toast-content {
  flex: 1;
  min-width: 0;
}

.u-toast-message {
  font-size: 28rpx;
  line-height: 1.4;
  word-wrap: break-word;
  font-weight: 500;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
  letter-spacing: 0.3rpx;
}

/* 动画 */
@keyframes toastSlideIn {
  0% {
    opacity: 0;
    transform: translateY(-30rpx) scale(0.9);
    filter: blur(4rpx);
  }

  50% {
    opacity: 0.8;
    transform: translateY(-5rpx) scale(1.02);
  }

  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
    filter: blur(0);
  }
}

/* 响应式设计 */
@media (max-width: 575px) {
  .u-toast {
    min-height: 72rpx;
    padding: 12rpx 20rpx;
    border-radius: 36rpx;
  }

  .u-toast-icon {
    width: 32rpx;
    height: 32rpx;
    margin-right: 10rpx;
  }

  .u-toast-icon-text {
    font-size: 20rpx;
  }

  .u-toast-message {
    font-size: 26rpx;
  }
}
</style>
