<template>
  <view class="u-toast-container" v-if="visible" :style="containerStyle">
    <view class="u-toast" :class="toastClass" :style="toastStyle">
      <!-- 图标 -->
      <view class="u-toast-icon" v-if="showIcon">
        <text class="u-toast-icon-text" v-if="iconType === 'text'">{{ icon }}</text>
        <view class="u-toast-icon-custom" v-else-if="iconType === 'custom'">
          <slot name="icon"></slot>
        </view>
      </view>
      
      <!-- 消息内容 -->
      <view class="u-toast-content">
        <text class="u-toast-message" :style="messageStyle">{{ message }}</text>
      </view>
      
      <!-- 关闭按钮 -->
      <view class="u-toast-close" v-if="closable" @click="handleClose">
        <text class="u-toast-close-icon">×</text>
      </view>
    </view>
  </view>
</template>

<script lang="uts" setup>
  import { ToastType, ToastPosition } from "../types"
  
  // Props 定义
  const props = defineProps({
    // 是否显示
    visible: {
      type: Boolean,
      default: false
    },
    // 消息内容
    message: {
      type: String,
      default: ''
    },
    // 提示类型
    type: {
      type: String as PropType<ToastType>,
      default: 'info' as ToastType
    },
    // 自定义背景色
    backgroundColor: {
      type: String,
      default: ''
    },
    // 自定义文字颜色
    textColor: {
      type: String,
      default: ''
    },
    // 图标
    icon: {
      type: String,
      default: ''
    },
    // 是否显示图标
    showIcon: {
      type: Boolean,
      default: true
    },
    // 是否可关闭
    closable: {
      type: Boolean,
      default: false
    },
    // 自动关闭时间（毫秒）
    duration: {
      type: Number,
      default: 3000
    },
    // 位置
    position: {
      type: String as PropType<ToastPosition>,
      default: 'top' as ToastPosition
    },
    // 距离顶部的距离
    top: {
      type: Number,
      default: 0
    },
    // 自定义样式
    customStyle: {
      type: Object as PropType<UTSJSONObject>,
      default: {} as UTSJSONObject
    }
  })
  
  // Emits 定义
  const emit = defineEmits(['close', 'click'])
  
  // 响应式数据
  const visible = ref(props.visible)
  const timer = ref<number | null>(null)
  
  // 计算属性
  const iconType = computed(() => {
    if (props.icon === '') return 'none'
    if (props.icon.length === 1) return 'text'
    return 'custom'
  })
  
  const toastClass = computed(() => {
    const classes = ['u-toast-' + props.type]
    if (props.position === 'top') classes.push('u-toast-top')
    if (props.position === 'bottom') classes.push('u-toast-bottom')
    return classes.join(' ')
  })
  
  const containerStyle = computed(() => {
    const style: UTSJSONObject = {}
    if (props.position === 'top') {
      style['top'] = (props.top || 0) + 'rpx'
    } else if (props.position === 'bottom') {
      style['bottom'] = (props.top || 0) + 'rpx'
    }
    return style
  })
  
  const toastStyle = computed(() => {
    const style: UTSJSONObject = {}
    if (props.backgroundColor) {
      style['background-color'] = props.backgroundColor
    }
    // 合并自定义样式
    Object.keys(props.customStyle).forEach(key => {
      style[key] = props.customStyle[key]
    })
    return style
  })
  
  const messageStyle = computed(() => {
    const style: UTSJSONObject = {}
    if (props.textColor) {
      style['color'] = props.textColor
    }
    return style
  })
  
  // 方法
  function show() {
    visible.value = true
    if (props.duration > 0) {
      clearTimer()
      timer.value = setTimeout(() => {
        hide()
      }, props.duration)
    }
  }
  
  function hide() {
    visible.value = false
    clearTimer()
    emit('close')
  }
  
  function handleClose() {
    hide()
  }
  
  function clearTimer() {
    if (timer.value) {
      clearTimeout(timer.value)
      timer.value = null
    }
  }
  
  // 监听 props.visible 变化
  watch(() => props.visible, (newVal) => {
    if (newVal) {
      show()
    } else {
      hide()
    }
  }, { immediate: true })
  
  // 组件卸载时清理定时器
  onUnmounted(() => {
    clearTimer()
  })
  
  // 暴露方法
  defineExpose({
    show,
    hide
  })
</script>

<style lang="scss" scoped>
.u-toast-container {
  position: fixed;
  left: 0;
  right: 0;
  z-index: 9999;
  pointer-events: none;
  display: flex;
  justify-content: center;
  padding: 0 32rpx;
}

.u-toast {
  display: flex;
  align-items: center;
  min-height: 88rpx;
  padding: 16rpx 24rpx;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10rpx);
  pointer-events: auto;
  max-width: 100%;
  word-wrap: break-word;
  
  /* 动画效果 */
  animation: toastSlideIn 0.3s ease-out;
}

.u-toast-top {
  transform-origin: top center;
}

.u-toast-bottom {
  transform-origin: bottom center;
}

/* 不同类型的默认样式 */
.u-toast-success {
  background-color: rgba(82, 196, 26, 0.9);
  color: white;
}

.u-toast-error {
  background-color: rgba(255, 77, 79, 0.9);
  color: white;
}

.u-toast-warning {
  background-color: rgba(250, 173, 20, 0.9);
  color: white;
}

.u-toast-info {
  background-color: rgba(24, 144, 255, 0.9);
  color: white;
}

.u-toast-default {
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
}

.u-toast-icon {
  margin-right: 12rpx;
  flex-shrink: 0;
}

.u-toast-icon-text {
  font-size: 32rpx;
  line-height: 1;
}

.u-toast-content {
  flex: 1;
  min-width: 0;
}

.u-toast-message {
  font-size: 28rpx;
  line-height: 1.4;
  word-wrap: break-word;
}

.u-toast-close {
  margin-left: 12rpx;
  padding: 8rpx;
  flex-shrink: 0;
  cursor: pointer;
}

.u-toast-close-icon {
  font-size: 36rpx;
  line-height: 1;
  opacity: 0.8;
}

/* 动画 */
@keyframes toastSlideIn {
  0% {
    opacity: 0;
    transform: translateY(-20rpx) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 响应式设计 */
@media (max-width: 575px) {
  .u-toast {
    min-height: 80rpx;
    padding: 14rpx 20rpx;
  }
  
  .u-toast-message {
    font-size: 26rpx;
  }
  
  .u-toast-icon-text {
    font-size: 28rpx;
  }
}
</style>
