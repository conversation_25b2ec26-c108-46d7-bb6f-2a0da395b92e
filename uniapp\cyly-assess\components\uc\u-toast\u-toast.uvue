<template>
  <view class="u-toast-container" v-if="visible" :style="containerStyle">
    <view class="u-toast" :class="toastClass" :style="toastStyle">
      <!-- 图标 -->
      <view class="u-toast-icon" v-if="showIcon">
        <!-- SVG 图标 -->
        <l-svg v-if="iconType === 'svg'" class="u-toast-icon-svg" :src="iconSrc" :color="iconColor" />
        <!-- 文字图标 -->
        <text class="u-toast-icon-text" v-else-if="iconType === 'text'">{{ icon }}</text>
        <!-- 自定义图标插槽 -->
        <view class="u-toast-icon-custom" v-else-if="iconType === 'custom'">
          <slot name="icon"></slot>
        </view>
      </view>

      <!-- 消息内容 -->
      <view class="u-toast-content">
        <text class="u-toast-message" :style="messageStyle">{{ message }}</text>
      </view>
    </view>
  </view>
</template>

<script lang="uts" setup>
  import { ToastType, ToastPosition } from "../types"
  import lSvg from "@/uni_modules/lime-svg/components/l-svg/l-svg"

  // Props 定义
  const props = defineProps({
    // 是否显示
    visible: {
      type: Boolean,
      default: false
    },
    // 消息内容
    message: {
      type: String,
      default: ''
    },
    // 提示类型
    type: {
      type: String as PropType<ToastType>,
      default: 'info' as ToastType
    },
    // 自定义背景色
    backgroundColor: {
      type: String,
      default: ''
    },
    // 自定义文字颜色
    textColor: {
      type: String,
      default: ''
    },
    // 图标
    icon: {
      type: String,
      default: ''
    },
    // 图标颜色（用于 SVG 图标）
    iconColor: {
      type: String,
      default: ''
    },
    // 是否显示图标
    showIcon: {
      type: Boolean,
      default: true
    },
    // 自动关闭时间（毫秒）
    duration: {
      type: Number,
      default: 3000
    },
    // 位置
    position: {
      type: String as PropType<ToastPosition>,
      default: 'top' as ToastPosition
    },
    // 距离顶部的距离
    top: {
      type: Number,
      default: 0
    },
    // 自定义样式
    customStyle: {
      type: Object as PropType<UTSJSONObject>,
      default: {} as UTSJSONObject
    }
  })

  // Emits 定义
  const emit = defineEmits(['close', 'click'])

  // 响应式数据
  const visible = ref(props.visible)
  const timer = ref<number | null>(null)

  // 计算属性
  const iconType = computed(() => {
    if (props.icon === '') return 'none'
    // 检查是否是 SVG 文件路径
    if (props.icon.startsWith('/static/icons/') && props.icon.endsWith('.svg')) return 'svg'
    // 检查是否是单个字符（emoji 或文字图标）
    if (props.icon.length === 1 || /^[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/u.test(props.icon)) return 'text'
    return 'custom'
  })

  const iconSrc = computed(() => {
    if (iconType.value === 'svg') {
      return props.icon
    }
    return ''
  })

  const toastClass = computed(() => {
    const classes = ['u-toast-' + props.type]
    if (props.position === 'top') classes.push('u-toast-top')
    if (props.position === 'bottom') classes.push('u-toast-bottom')
    return classes.join(' ')
  })

  const containerStyle = computed(() => {
    const style: UTSJSONObject = {}
    const topValue = props.top != null ? props.top : 0
    if (props?.position === 'top') {
      style['top'] = topValue + 'rpx'
    } else if (props?.position === 'bottom') {
      style['bottom'] = topValue + 'rpx'
    }
    return style
  })

  const toastStyle = computed(() => {
    const style: UTSJSONObject = {}
    if (props.backgroundColor != null && props.backgroundColor !== '') {
      style['background-color'] = props.backgroundColor
    }
    // 合并自定义样式
    for (const key in props.customStyle) {
      style[key] = props.customStyle[key]
    }
    return style
  })

  const messageStyle = computed(() => {
    const style: UTSJSONObject = {}
    if (props.textColor != null && props.textColor !== '') {
      style['color'] = props.textColor
    }
    return style
  })

  // 方法
  function show() {
    visible.value = true
    if (props.duration > 0) {
      clearTimer()
      timer.value = setTimeout(() => {
        hide()
      }, props.duration)
    }
  }

  function hide() {
    visible.value = false
    clearTimer()
    emit('close')
  }

  function clearTimer() {
    if (timer.value) {
      clearTimeout(timer.value)
      timer.value = null
    }
  }

  // 监听 props.visible 变化
  watch(() => props.visible, (newVal) => {
    if (newVal) {
      show()
    } else {
      hide()
    }
  }, { immediate: true })

  // 组件卸载时清理定时器
  onUnmounted(() => {
    clearTimer()
  })

  // 暴露方法
  defineExpose({
    show,
    hide
  })
</script>

<style lang="scss" scoped>
.u-toast-container {
  position: fixed;
  left: 0;
  right: 0;
  z-index: 9999;
  pointer-events: none;
  display: flex;
  justify-content: center;
  padding: 0 32rpx;
}

.u-toast {
  display: flex;
  align-items: center;
  min-height: 88rpx;
  padding: 16rpx 24rpx;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10rpx);
  pointer-events: auto;
  max-width: 100%;
  word-wrap: break-word;

  /* 动画效果 */
  animation: toastSlideIn 0.3s ease-out;
}

.u-toast-top {
  transform-origin: top center;
}

.u-toast-bottom {
  transform-origin: bottom center;
}

/* 不同类型的默认样式 */
.u-toast-success {
  background-color: rgba(82, 196, 26, 0.9);
  color: white;
}

.u-toast-error {
  background-color: rgba(255, 77, 79, 0.9);
  color: white;
}

.u-toast-warning {
  background-color: rgba(250, 173, 20, 0.9);
  color: white;
}

.u-toast-info {
  background-color: rgba(24, 144, 255, 0.9);
  color: white;
}

.u-toast-default {
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
}

.u-toast-icon {
  margin-right: 12rpx;
  flex-shrink: 0;
}

.u-toast-icon-text {
  font-size: 32rpx;
  line-height: 1;
}

.u-toast-icon-svg {
  width: 32rpx;
  height: 32rpx;
}

.u-toast-content {
  flex: 1;
  min-width: 0;
}

.u-toast-message {
  font-size: 28rpx;
  line-height: 1.4;
  word-wrap: break-word;
}

/* 动画 */
@keyframes toastSlideIn {
  0% {
    opacity: 0;
    transform: translateY(-20rpx) scale(0.95);
  }

  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 响应式设计 */
@media (max-width: 575px) {
  .u-toast {
    min-height: 80rpx;
    padding: 14rpx 20rpx;
  }

  .u-toast-message {
    font-size: 26rpx;
  }

  .u-toast-icon-text {
    font-size: 28rpx;
  }
}
</style>
