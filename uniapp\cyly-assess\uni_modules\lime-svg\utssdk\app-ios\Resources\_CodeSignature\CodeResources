<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/unimoduleLimeSvg-Swift.h</key>
		<data>
		tf81Unzlm2IeIPvhVrOfXjFOfOo=
		</data>
		<key>Headers/unimoduleLimeSvg.h</key>
		<data>
		FH2SRnQAHIO8AbDZPa5/Bz/CcdA=
		</data>
		<key>Info.plist</key>
		<data>
		YEqBpnWq79IKUdZ+vaWBJo4n+C4=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		IhA8PKd525VdS98DEH+6b9Clqhc=
		</data>
		<key>Modules/unimoduleLimeSvg.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		1el+rPWzWynKydU+nZ6TKV1etlY=
		</data>
		<key>Modules/unimoduleLimeSvg.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<data>
		hUoDRQ9kWVS6MKLx4VljeYMxxBQ=
		</data>
		<key>Modules/unimoduleLimeSvg.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<data>
		zg56E0yDXqXbWQfbYkpivuVjR0g=
		</data>
		<key>Modules/unimoduleLimeSvg.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<data>
		zty7FJX09Swj3MeWh2YYhtwnmsI=
		</data>
		<key>Modules/unimoduleLimeSvg.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<data>
		zg56E0yDXqXbWQfbYkpivuVjR0g=
		</data>
		<key>Modules/unimoduleLimeSvg.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<data>
		/f52fJHOnMkK/zDHkBaUmp1obLg=
		</data>
		<key>config.json</key>
		<data>
		hS7IDLM9E6tSFma0f/VfI3h9Kwc=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/unimoduleLimeSvg-Swift.h</key>
		<dict>
			<key>hash2</key>
			<data>
			cNN+ZnUmpK0QyGvS+9HYwSdxND7TRFB2sjAflAE4wAI=
			</data>
		</dict>
		<key>Headers/unimoduleLimeSvg.h</key>
		<dict>
			<key>hash2</key>
			<data>
			XAsze1m7Z4PhyI2gj+RafZVS4xyqWjgvqraTawdVPMY=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash2</key>
			<data>
			yItRRdWTDBHEqWXkBQ66ykO15PDNTn29XmtZY5MeesA=
			</data>
		</dict>
		<key>Modules/unimoduleLimeSvg.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash2</key>
			<data>
			FNa0ZMzQwH2zUPzJFnhGTrjlMtEmadbGiLq7/TGkVTI=
			</data>
		</dict>
		<key>Modules/unimoduleLimeSvg.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Ff3+HpDAtwX5/MSpTfRY+YuOkYMZt7Pd7Ip/mKx5MJs=
			</data>
		</dict>
		<key>Modules/unimoduleLimeSvg.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			CvVMkGx1Fx6W3xqrw9tVQU0d/hQN0TReob8Gv3H3q2o=
			</data>
		</dict>
		<key>Modules/unimoduleLimeSvg.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash2</key>
			<data>
			nEsUzkM3QRd0mLPY7L61rdfLh5mfEKJ//HDfQ3PsCxU=
			</data>
		</dict>
		<key>Modules/unimoduleLimeSvg.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			CvVMkGx1Fx6W3xqrw9tVQU0d/hQN0TReob8Gv3H3q2o=
			</data>
		</dict>
		<key>Modules/unimoduleLimeSvg.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash2</key>
			<data>
			ZQqe8gVQwa4L0K2t737aNBj4vHId9i2HetJxjpRV0P8=
			</data>
		</dict>
		<key>config.json</key>
		<dict>
			<key>hash2</key>
			<data>
			zW/qOeZOGFdkIdPeZADZbLqdNIOUi3VYPY/UcQA0MSI=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
