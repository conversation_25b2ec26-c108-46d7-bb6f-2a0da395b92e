import { FormItemData, FormItemVerifyResult, FormValidResult, FormValidResultItem, ToastType, ToastPosition } from "../types";
import uToast from "../u-toast/u-toast";
import { ComponentPublicInstance } from 'vue';
const __sfc__ = defineComponent({
    __name: 'u-form',
    name: 'u-form',
    props: /*#__PURE__*/ mergeModels({
        customStyle: {
            type: Object as PropType<UTSJSONObject>,
            default: {} as UTSJSONObject as UTSJSONObject
        },
        triggerChange: {
            type: <PERSON>olean,
            default: false
        },
        // Toast 相关配置
        showToast: {
            type: Boolean,
            default: true
        },
        toastPosition: {
            type: String as PropType<ToastPosition>,
            default: 'top' as ToastPosition
        },
        toastTop: {
            type: Number,
            default: 100
        },
        toastDuration: {
            type: Number,
            default: 3000
        }
    }, {
        "modelValue": {
            type: Object as PropType<UTSJSONObject>,
            default: {} as UTSJSONObject
        },
    }),
    emits: /*#__PURE__*/ mergeModels(['submited', 'change', 'focus'], ["update:modelValue"]),
    setup(__props, { expose: __expose }: SetupContext): any | null {
        const __ins = getCurrentInstance()!;
        const _ctx = __ins.proxy as InstanceType<typeof __sfc__>;
        const _cache = __ins.renderCache;
        const props = __props;
        const fieldItems = ref<FormItemData[]>([]);
        function emit(event: string, ...do_not_transform_spread: Array<any | null>) {
            __ins.emit(event, ...do_not_transform_spread);
        }
        const model = useModel<UTSJSONObject>(__ins.props, "modelValue");
        // Toast 相关数据
        const toastRef = ref<ComponentPublicInstance | null>(null);
        const toastVisible = ref(false);
        const toastMessage = ref('');
        const toastType = ref<ToastType>('error');
        const toastIcon = ref('✕');
        // Toast 方法
        function showErrorToast(message: string) {
            if (!props.showToast)
                return;
            toastMessage.value = message;
            toastType.value = 'error';
            toastIcon.value = '/static/icons/user.svg'; // 使用 SVG 图标
            toastVisible.value = true;
        }
        function onToastClose() {
            toastVisible.value = false;
        }
        // 先定义 valid 函数
        function valid(callback: FormValidResult) {
            let allow = true;
            let verifyRes = [] as FormItemVerifyResult[];
            fieldItems.value.forEach((item: FormItemData) => {
                let field = item.field;
                let value = (model.value as UTSJSONObject)[field];
                if (value != null) {
                    let _item = fieldItems.value.find((it: FormItemData): boolean => it.field == field);
                    if (_item != null) {
                        item.instance.$callMethod('verify', value, (res: FormItemVerifyResult) => {
                            if (!res.valid) {
                                allow = false;
                                verifyRes.push(res);
                            }
                        });
                    }
                }
                else {
                    allow = false;
                }
            });
            if (allow) {
                if (callback.success != null) {
                    callback.success!();
                }
            }
            else {
                // 显示第一个验证失败的错误信息
                if (verifyRes.length > 0) {
                    const errorMessage = verifyRes[0].message;
                    showErrorToast(errorMessage != null ? errorMessage : '验证失败');
                }
                if (callback.fail != null) {
                    callback.fail!(verifyRes);
                }
            }
        }
        function onSubmit(callback: FormValidResult) {
            valid({
                success: () => {
                    if (callback.success != null) {
                        emit('submited');
                        callback.success!();
                    }
                },
                fail: (failResults: FormItemVerifyResult[]) => {
                    if (callback.fail != null) {
                        callback.fail!(failResults);
                    }
                }
            } as FormValidResult);
        }
        function pushFielditem(fieldItem: FormItemData) {
            if (fieldItems.value.find((item: FormItemData): boolean => item.field == fieldItem.field) != null) {
                return;
            }
            console.log(fieldItem, " at components/uc/u-form/u-form.uvue:132");
            fieldItems.value.push(fieldItem);
            console.log("pushFielditem-fieldItems:", fieldItems.value.map((v): string => v.field), " at components/uc/u-form/u-form.uvue:134");
        }
        function validItem(field: string, callback: FormValidResultItem) {
            const fieldItem = fieldItems.value.find((item): boolean => item.field == field);
            console.log("===fieldItems===:", fieldItems, " at components/uc/u-form/u-form.uvue:138");
            console.log("model:", model, " at components/uc/u-form/u-form.uvue:139");
            const value = (model.value as UTSObject)[field];
            if (fieldItem != null) {
                fieldItem.instance.$callMethod('verify', value, (res: FormItemVerifyResult) => {
                    if (!res.valid) {
                        // 显示验证失败的错误信息
                        const errorMessage = res.message;
                        showErrorToast(errorMessage != null ? errorMessage : '验证失败');
                        if (callback.fail != null) {
                            callback.fail!(res);
                        }
                        return;
                    }
                    if (callback.success != null) {
                        callback.success!();
                    }
                });
            }
            else {
                // 如果找不到字段项，调用失败回调
                const errorMsg = `字段 ${field} 不存在`;
                showErrorToast(errorMsg);
                if (callback.fail != null) {
                    callback.fail!({
                        valid: false,
                        message: errorMsg,
                        field: field
                    } as FormItemVerifyResult);
                }
            }
        }
        watch(model, (value: UTSJSONObject) => {
            emit('change', value);
            //TODO 测试
            if (props.triggerChange) {
                valid({
                    success() {
                        console.log("?", " at components/uc/u-form/u-form.uvue:176");
                    },
                    fail(res) {
                        console.log("???", res, " at components/uc/u-form/u-form.uvue:179");
                    }
                } as FormValidResult);
            }
        }, {
            deep: true
        });
        __expose({
            onSubmit,
            pushFielditem,
            validItem,
            valid
        });
        return (): any | null => {
            return createElementVNode("view", utsMapOf({
                class: "u-form",
                style: normalizeStyle([_ctx.customStyle])
            }), [
                createVNode(unref(uToast), utsMapOf({
                    ref_key: "toastRef",
                    ref: toastRef,
                    visible: unref(toastVisible),
                    message: unref(toastMessage),
                    type: unref(toastType),
                    icon: unref(toastIcon),
                    duration: _ctx.toastDuration,
                    position: _ctx.toastPosition,
                    top: _ctx.toastTop,
                    onClose: onToastClose
                }), null, 8 /* PROPS */, ["visible", "message", "type", "icon", "duration", "position", "top"]),
                renderSlot(_ctx.$slots, "default")
            ], 4 /* STYLE */);
        };
    }
});
export default __sfc__;
const GenComponentsUcUFormUFormStyles = [];
//# sourceMappingURL=u-form.uvue.map