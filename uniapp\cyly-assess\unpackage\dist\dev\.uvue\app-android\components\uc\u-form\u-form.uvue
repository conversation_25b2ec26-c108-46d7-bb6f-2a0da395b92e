import { FormItemData, FormItemVerifyResult, FormValidResult, FormValidResultItem } from "../types";
const __sfc__ = defineComponent({
    __name: 'u-form',
    name: 'u-form',
    props: /*#__PURE__*/ mergeModels({
        customStyle: {
            type: Object as PropType<UTSJSONObject>,
            default: {} as UTSJSONObject as UTSJSONObject
        },
        triggerChange: {
            type: Boolean,
            default: false
        }
    }, {
        "modelValue": {
            type: Object as PropType<UTSJSONObject>,
            default: {} as UTSJSONObject
        },
    }),
    emits: /*#__PURE__*/ mergeModels(['submited', 'change', 'focus'], ["update:modelValue"]),
    setup(__props, { expose: __expose }: SetupContext): any | null {
        const __ins = getCurrentInstance()!;
        const _ctx = __ins.proxy as InstanceType<typeof __sfc__>;
        const _cache = __ins.renderCache;
        const props = __props;
        const fieldItems = ref<FormItemData[]>([]);
        function emit(event: string, ...do_not_transform_spread: Array<any | null>) {
            __ins.emit(event, ...do_not_transform_spread);
        }
        const model = useModel<UTSJSONObject>(__ins.props, "modelValue");
        // 先定义 valid 函数
        function valid(callback: FormValidResult) {
            let allow = true;
            let verifyRes = [] as FormItemVerifyResult[];
            fieldItems.value.forEach((item: FormItemData) => {
                let field = item.field;
                let value = (model.value as UTSJSONObject)[field];
                if (value != null) {
                    let _item = fieldItems.value.find((it: FormItemData): boolean => it.field == field);
                    if (_item != null) {
                        item.instance.$callMethod('verify', value, (res: FormItemVerifyResult) => {
                            if (!res.valid) {
                                allow = false;
                                verifyRes.push(res);
                            }
                        });
                    }
                }
                else {
                    allow = false;
                }
            });
            if (allow) {
                if (callback.success != null) {
                    callback.success!();
                }
            }
            else {
                if (callback.fail != null) {
                    callback.fail!(verifyRes);
                }
            }
        }
        function onSubmit(callback: FormValidResult) {
            valid({
                success: () => {
                    if (callback.success != null) {
                        emit('submited');
                        callback.success!();
                    }
                },
                fail: (failResults: FormItemVerifyResult[]) => {
                    if (callback.fail != null) {
                        callback.fail!(failResults);
                    }
                }
            } as FormValidResult);
        }
        function pushFielditem(fieldItem: FormItemData) {
            if (fieldItems.value.find((item: FormItemData): boolean => item.field == fieldItem.field) != null) {
                return;
            }
            console.log(fieldItem, " at components/uc/u-form/u-form.uvue:83");
            fieldItems.value.push(fieldItem);
            console.log("pushFielditem-fieldItems:", fieldItems.value.map((v): string => v.field), " at components/uc/u-form/u-form.uvue:85");
        }
        function validItem(field: string, callback: FormValidResultItem) {
            const fieldItem = fieldItems.value.find((item): boolean => item.field == field);
            console.log("===fieldItems===:", fieldItems, " at components/uc/u-form/u-form.uvue:89");
            console.log("model:", model, " at components/uc/u-form/u-form.uvue:90");
            const value = (model.value as UTSObject)[field];
            if (fieldItem != null) {
                fieldItem.instance.$callMethod('verify', value, (res: FormItemVerifyResult) => {
                    if (!res.valid) {
                        if (callback.fail != null) {
                            callback.fail!(res);
                        }
                        return;
                    }
                    if (callback.success != null) {
                        callback.success!();
                    }
                });
            }
            else {
                // 如果找不到字段项，调用失败回调
                if (callback.fail != null) {
                    callback.fail!({
                        valid: false,
                        message: `字段 ${field} 不存在`,
                        field: field
                    } as FormItemVerifyResult);
                }
            }
        }
        watch(model, (value: UTSJSONObject) => {
            emit('change', value);
            //TODO 测试
            if (props.triggerChange) {
                valid({
                    success() {
                        console.log("?", " at components/uc/u-form/u-form.uvue:122");
                    },
                    fail(res) {
                        console.log("???", res, " at components/uc/u-form/u-form.uvue:125");
                    }
                } as FormValidResult);
            }
        }, {
            deep: true
        });
        __expose({
            onSubmit,
            pushFielditem,
            validItem,
            valid
        });
        return (): any | null => {
            return createElementVNode("view", utsMapOf({
                class: "u-form",
                style: normalizeStyle([_ctx.customStyle])
            }), [
                renderSlot(_ctx.$slots, "default")
            ], 4 /* STYLE */);
        };
    }
});
export default __sfc__;
const GenComponentsUcUFormUFormStyles = [];
//# sourceMappingURL=u-form.uvue.map