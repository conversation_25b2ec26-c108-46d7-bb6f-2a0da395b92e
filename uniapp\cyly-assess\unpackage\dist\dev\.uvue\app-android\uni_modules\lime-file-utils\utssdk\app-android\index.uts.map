{"version": 3, "file": "index.uts", "sourceRoot": "", "sources": ["uni_modules/lime-file-utils/utssdk/app-android/index.uts"], "names": [], "mappings": "AAAA,OAAO,MAAM,MAAM,qBAAqB,CAAC;AACzC,OAAO,WAAW,MAAM,4BAA4B,CAAC;AACrD,OAAO,qBAAqB,MAAM,+BAA+B,CAAC;AAElE,OAAO,IAAI,MAAM,cAAc,CAAC;AAChC,OAAO,eAAe,MAAM,yBAAyB,CAAC;AACtD,OAAO,gBAAgB,MAAM,0BAA0B,CAAC;AACxD,OAAO,WAAW,MAAM,qBAAqB,CAAC;AAE9C,iDAAiD;AACjD,OAAO,EAAE,kBAAkB,EAAE,cAAc,EAAE,MAAM,cAAc,CAAA;AACjE,KAAK,aAAa,GAAG,SAAS,GAAG,IAAI,CAAA;AAErC,SAAS,kBAAkB,CAAC,WAAW,EAAG,WAAW,GAAI,aAAa;IACrE,IAAI;QACH,IAAI,GAAG,EAAG,qBAAqB,GAAG,IAAI,qBAAqB,EAAE,CAAA;QAC7D,IAAI,KAAK,EAAG,SAAS,GAAG,IAAI,SAAS,CAAC,IAAI,CAAC,CAAA;QAE3C,GAAG;YACF,IAAI,MAAM,GAAG,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;YACpC,IAAI,MAAM,IAAI,CAAC,CAAC,EAAE;gBACjB,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE,MAAM,CAAC,CAAA;aAC3B;iBAAM;gBACN,MAAK;aACL;SACD,QAAQ,IAAI,EAAC;QACd,GAAG,CAAC,KAAK,EAAE,CAAA;QACX,OAAO,GAAG,CAAC,WAAW,EAAE,CAAA;KACxB;IAAC,OAAO,CAAC,EAAG,SAAS,EAAE;QACvB,OAAO,IAAI,CAAC;KACZ;AACF,CAAC;AAED,SAAS,WAAW,CAAC,QAAQ,EAAG,MAAM,GAAI,cAAc;IACvD,MAAM,SAAS,GAAG,WAAW,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC;IAChE,IAAI,SAAS,IAAI,IAAI;QAAE,OAAO,IAAI,CAAA;IAClC,OAAO,WAAW,CAAC,YAAY,EAAE,CAAC,wBAAwB,CAAC,SAAS,CAAC,CAAC;AACvE,CAAC;AAED,MAAM,UAAU,eAAe,CAAC,IAAI,EAAG,MAAM,GAAI,MAAM,GAAG,IAAI;IAC7D,IAAI,GAAG,GAAG,IAAI,CAAA;IACd,IAAI,GAAG,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,UAAU,CAAC,aAAa,CAAC,EAAE;QACtF,OAAO,GAAG,CAAA;KACV;IACD,IAAI,GAAG,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE;QAC9B,GAAG,GAAG,GAAG,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;KACrC;SAAM,IAAI,GAAG,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE;QACxC,GAAG,GAAG,UAAU,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAA;KACzC;SAAM;QACN,GAAG,GAAG,UAAU,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAA;QACzC,IAAI,GAAG,CAAC,UAAU,CAAC,iBAAiB,CAAC,EAAE;YACtC,IAAI;gBACH,MAAM,OAAO,GAAG,UAAU,CAAC,cAAc,EAAE,CAAC,CAAC;gBAC7C,MAAM,WAAW,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAAC,CAAA;gBACjG,WAAW,CAAC,KAAK,EAAE,CAAC;gBACpB,OAAO,GAAG,CAAA;aACV;YAAC,OAAO,CAAC,KAAA,EAAE;gBACX,OAAO,IAAI,CAAA;aACX;SACD;KACD;IACD,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,CAAA;IAC1B,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE;QAClB,OAAO,GAAG,CAAA;KACV;IACD,OAAO,IAAI,CAAA;AACZ,CAAC;AAGD;;;;GAIG;AACH,MAAM,UAAU,cAAc,CAAC,QAAQ,EAAG,MAAM,GAAE,OAAO,EAAE;IAC1D,MAAM,IAAI,GAAG,eAAe,CAAC,QAAQ,CAAC,CAAA;IACtC,IAAG,IAAI,IAAI,IAAI;QAAE,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;IACtC,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAA;IAC3B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,CAAA;IAE5B,IAAG,MAAM,EAAE;QACV,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAA;KAC/B;SAAM;QACN,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;KACrB;AACF,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,QAAQ,CAAC,QAAQ,EAAG,MAAM,GAAE,OAAO;IAClD,MAAM,MAAM,GAAG,cAAc,CAAC,QAAQ,CAAC,CAAC;IACxC,OAAO,MAAM,CAAC,CAAC,CAAC,CAAA;AACjB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,WAAW,CAAC,QAAQ,EAAG,MAAM,GAAE,OAAO;IACrD,MAAM,MAAM,GAAG,cAAc,CAAC,QAAQ,CAAC,CAAC;IACxC,OAAO,MAAM,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAA;AAC9B,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,MAAM,CAAC,QAAQ,EAAG,MAAM,GAAE,OAAO;IAChD,MAAM,MAAM,GAAG,cAAc,CAAC,QAAQ,CAAC,CAAC;IACxC,OAAO,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;AAC/B,CAAC;AAID,MAAM,UAAU,YAAY,CAAC,QAAQ,EAAG,MAAM,GAAI,cAAc;IAC/D,IAAI;QACH,MAAM,OAAO,GAAG,UAAU,CAAC,cAAc,EAAE,CAAC,CAAC;QAC7C,IAAI,IAAI,GAAG,QAAQ,CAAC;QACpB,IAAI,UAAU,EAAG,aAAa,GAAG,IAAI,CAAA;QAErC,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE;YAC/B,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAA;SAClC;aAAM;YACN,0EAA0E;YAC1E,0CAA0C;YAC1C,IAAI,GAAG,UAAU,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA;SAC3C;QAED,IAAI,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,EAAE;YACvC,UAAU,GAAG,kBAAkB,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAAC,CAAC,CAAA;SAC9G;aAAM;YACN,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAA;YAC3B,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE;gBAClB,IAAI,GAAG,EAAG,eAAe,GAAG,IAAI,eAAe,CAAC,IAAI,CAAC,CAAC;gBACtD,UAAU,GAAG,kBAAkB,CAAC,GAAG,CAAC,CAAC;gBACrC,GAAG,CAAC,KAAK,EAAE,CAAC;aACZ;SACD;QACD,IAAI,UAAU,IAAI,IAAI;YAAE,OAAO,IAAI,CAAA;QACnC,OAAO,MAAM,CAAC,cAAc,CAAC,UAAU,EAAE,MAAM,CAAC,OAAO,CAAC,CAAA;KACxD;IAAC,OAAO,CAAC,KAAA,EAAE;QACX,OAAO,IAAI,CAAA;KACX;AACF,CAAC;AACD,MAAM,UAAU,aAAa,CAAC,QAAQ,EAAG,MAAM,GAAI,cAAc;IAChE,MAAM,MAAM,GAAG,YAAY,CAAC,QAAQ,CAAC,CAAA;IACrC,MAAM,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC,CAAC;IACvC,IAAI,MAAM,IAAI,IAAI,IAAI,QAAQ,IAAI,IAAI;QAAE,OAAO,IAAI,CAAC;IACpD,OAAO,OAAO,GAAG,QAAQ,GAAG,UAAU,GAAG,MAAM,CAAC;AACjD,CAAC;AAGD,SAAS,2BAA2B,CAAC,OAAO,EAAG,MAAM,GAAI,MAAM;IAC9D,MAAM,UAAU,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IACxC,MAAM,QAAQ,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;IAC9F,MAAM,aAAa,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAC1C,OAAO,aAAa,CAAC,CAAC,CAAC,CAAC;AACzB,CAAC;AACD,SAAS,cAAc,CAAC,OAAO,EAAG,MAAM,GAAI,SAAS;IACpD,MAAM,UAAU,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IACxC,MAAM,MAAM,GAAG,OAAO,CAAC,SAAS,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;IACjD,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;AAC9C,CAAC;AAED,MAAM,UAAU,aAAa,CAAC,OAAO,EAAG,MAAM,EAAE,QAAQ,EAAG,cAAc,GAAG,IAAI,GAAI,cAAc;IACjG,IAAI;QACH,MAAM,KAAK,GAAG,cAAc,CAAC,OAAO,CAAC,CAAC;QACtC,MAAM,IAAI,GAAG,QAAQ,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE,IAAI,2BAA2B,CAAC,OAAO,CAAC,EAAE,CAAC;QACjF,MAAM,QAAQ,GAAG,UAAU,CAAC,eAAe,EAAE,CAAC,CAAC;QAC/C,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QAC1C,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;QAChC,IAAG,CAAC,IAAI,CAAC,MAAM,EAAE,EAAC;YACjB,IAAI,CAAC,KAAK,EAAE,CAAC;SACb;QACD,MAAM,GAAG,GAAG,IAAI,gBAAgB,CAAC,QAAQ,CAAC,CAAA;QAC1C,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACjB,GAAG,CAAC,KAAK,EAAE,CAAC;QACZ,OAAO,GAAG,QAAQ,GAAG,IAAI,EAAE,CAAA;KAC3B;IAAC,OAAO,CAAC,KAAA,EAAE;QACX,KAAK,CAAC,OAAO,EAAC,iEAAiE,EAAC,iBAAiB,EAAE,CAAC,CAAC,CAAA;QACrG,OAAO,IAAI,CAAA;KACX;AACF,CAAC;AAGD,oDAAoD;AACpD,sEAAsE;AACtE,kIAAkI;AAClI,oBAAoB;AACpB,eAAe;AACf,yGAAyG;AACzG,WAAW;AACX,aAAa;AACb,iBAAiB;AACjB,MAAM;AACN,6CAA6C;AAC7C,gBAAgB;AAChB,MAAM;AACN,IAAI;AAGJ,MAAM,UAAU,WAAW,CAAC,OAAO,EAAG,kBAAkB;IAEvD,IAAI,OAAO,CAAC,IAAI,IAAI,UAAU,EAAE;QAC/B,MAAM,GAAG,GAAG,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;QACtC,MAAM,GAAG,GAAG,oBAAoB,CAAA;QAChC,IAAI,GAAG,IAAI,IAAI,EAAE;YAChB,OAAO,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,CAAA;YACtB,OAAO,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,CAAA;SACvB;aAAM;YACN,OAAO,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,CAAA;YACvB,OAAO,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,CAAA;SACnB;KACD;SAAM,IAAI,OAAO,CAAC,IAAI,IAAI,WAAW,EAAE;QACvC,MAAM,GAAG,GAAG,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;QACvC,MAAM,GAAG,GAAG,qBAAqB,CAAA;QACjC,IAAI,GAAG,IAAI,IAAI,EAAE;YAChB,OAAO,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,CAAA;YACtB,OAAO,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,CAAA;SACvB;aAAM;YACN,OAAO,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,CAAA;YACvB,OAAO,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,CAAA;SACnB;KACD;SAAM,IAAI,OAAO,CAAC,IAAI,IAAI,QAAQ,EAAE;QACpC,MAAM,GAAG,GAAG,aAAa,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAA;QACzD,MAAM,GAAG,GAAG,qBAAqB,CAAA;QACjC,IAAI,GAAG,IAAI,IAAI,EAAE;YAChB,OAAO,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,CAAA;YACtB,OAAO,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,CAAA;SACvB;aAAM;YACN,OAAO,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,CAAA;YACvB,OAAO,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,CAAA;SACnB;KACD;AACF,CAAC", "sourcesContent": ["import Base64 from \"android.util.Base64\";\r\nimport MimeTypeMap from \"android.webkit.MimeTypeMap\";\r\nimport ByteArrayOutputStream from 'java.io.ByteArrayOutputStream';\r\n\r\nimport File from \"java.io.File\";\r\nimport FileInputStream from \"java.io.FileInputStream\";\r\nimport FileOutputStream from \"java.io.FileOutputStream\";\r\nimport InputStream from 'java.io.InputStream';\r\n\r\n// import IOException from \"java.io.IOException\";\r\nimport { ProcessFileOptions, NullableString } from '../interface'\r\ntype NullByteArray = ByteArray | null\r\n\r\nfunction inputStreamToArray(inputStream : InputStream) : NullByteArray {\r\n\ttry {\r\n\t\tlet bos : ByteArrayOutputStream = new ByteArrayOutputStream()\r\n\t\tlet bytes : ByteArray = new ByteArray(1024)\r\n\r\n\t\tdo {\r\n\t\t\tlet length = inputStream.read(bytes)\r\n\t\t\tif (length != -1) {\r\n\t\t\t\tbos.write(bytes, 0, length)\r\n\t\t\t} else {\r\n\t\t\t\tbreak\r\n\t\t\t}\r\n\t\t} while (true)\r\n\t\tbos.close()\r\n\t\treturn bos.toByteArray()\r\n\t} catch (e : Throwable) {\r\n\t\treturn null;\r\n\t}\r\n}\r\n\r\nfunction getMimeType(filePath : string) : NullableString {\r\n\tconst extension = MimeTypeMap.getFileExtensionFromUrl(filePath);\r\n\tif (extension == null) return null\r\n\treturn MimeTypeMap.getSingleton().getMimeTypeFromExtension(extension);\r\n}\r\n\r\nexport function getResourcePath(path : string) : string | null {\r\n\tlet uri = path\r\n\tif (uri.startsWith(\"http\") || uri.startsWith(\"<svg\") || uri.startsWith(\"data:image/\")) {\r\n\t\treturn uri\r\n\t}\r\n\tif (uri.startsWith(\"file://\")) {\r\n\t\turi = uri.substring(\"file://\".length)\r\n\t} else if (uri.startsWith(\"unifile://\")) {\r\n\t\turi = UTSAndroid.convert2AbsFullPath(uri)\r\n\t} else {\r\n\t\turi = UTSAndroid.convert2AbsFullPath(uri)\r\n\t\tif (uri.startsWith(\"/android_asset/\")) {\r\n\t\t\ttry {\r\n\t\t\t\tconst context = UTSAndroid.getUniActivity()!;\r\n\t\t\t\tconst inputStream = context.getResources()!.getAssets().open(path.replace('/android_asset/', ''))\r\n\t\t\t\tinputStream.close();\r\n\t\t\t\treturn uri\r\n\t\t\t} catch (e) {\r\n\t\t\t\treturn null\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\tconst file = new File(uri)\r\n\tif (file.exists()) {\r\n\t\treturn uri\r\n\t}\r\n\treturn null\r\n}\r\n\r\n\r\n/**\r\n * 检查路径存在性及类型 (Android 实现)\r\n * @param path 要检查的完整路径（支持内部存储和外部存储路径）\r\n * @return Pair<是否存在, 是否是目录>\r\n */\r\nexport function checkExistence(filePath : string):boolean[] {\r\n\tconst path = getResourcePath(filePath)\r\n\tif(path == null) return [false, false]\r\n\tconst file = new File(path)\r\n\tconst exists = file.exists()\r\n\t\r\n\tif(exists) {\r\n\t\treturn [true, file.isDirectory]\r\n\t} else {\r\n\t\treturn [false, false]\r\n\t}\r\n}\r\n\r\n/**\r\n * 检查路径是否存在\r\n * @param path 要检查的完整路径\r\n */\r\nexport function isExists(filePath : string):boolean {\r\n\tconst result = checkExistence(filePath);\r\n\treturn result[0]\r\n}\r\n\r\n/**\r\n * 检查路径是否是存在的目录\r\n * @param path 要检查的完整路径\r\n */\r\nexport function isDirectory(filePath : string):boolean {\r\n\tconst result = checkExistence(filePath);\r\n\treturn result[0] && result[1]\r\n}\r\n\r\n/**\r\n * 检查指定路径是否为存在的文件\r\n * @param path 要检查的完整路径\r\n * @return 当且仅当路径存在且是普通文件时返回 true\r\n */\r\nexport function isFile(filePath : string):boolean {\r\n\tconst result = checkExistence(filePath);\r\n\treturn result[0] && !result[1]\r\n}\r\n\r\n\r\n\r\nexport function fileToBase64(filePath : string) : NullableString {\r\n\ttry {\r\n\t\tconst context = UTSAndroid.getUniActivity()!;\r\n\t\tlet path = filePath;\r\n\t\tlet imageBytes : NullByteArray = null\r\n\r\n\t\tif (path.startsWith(\"file://\")) {\r\n\t\t\tpath = path.replace(\"file://\", \"\")\r\n\t\t} else {\r\n\t\t\t// if(!path.startsWith(\"/storage\") && !path.startsWith(\"/android_asset/\"))\r\n\t\t\t// path = UTSAndroid.getResourcePath(path)\r\n\t\t\tpath = UTSAndroid.convert2AbsFullPath(path)\r\n\t\t}\r\n\r\n\t\tif (path.startsWith(\"/android_asset/\")) {\r\n\t\t\timageBytes = inputStreamToArray(context.getResources()!.getAssets().open(path.replace('/android_asset/', '')))\r\n\t\t} else {\r\n\t\t\tconst file = new File(path)\r\n\t\t\tif (file.exists()) {\r\n\t\t\t\tlet fis : FileInputStream = new FileInputStream(file);\r\n\t\t\t\timageBytes = inputStreamToArray(fis);\r\n\t\t\t\tfis.close();\r\n\t\t\t}\r\n\t\t}\r\n\t\tif (imageBytes == null) return null\r\n\t\treturn Base64.encodeToString(imageBytes, Base64.DEFAULT)\r\n\t} catch (e) {\r\n\t\treturn null\r\n\t}\r\n}\r\nexport function fileToDataURL(filePath : string) : NullableString {\r\n\tconst base64 = fileToBase64(filePath)\r\n\tconst mimeType = getMimeType(filePath);\r\n\tif (base64 == null || mimeType == null) return null;\r\n\treturn \"data:\" + mimeType + \";base64,\" + base64;\r\n}\r\n\r\n\r\nfunction getFileExtensionFromDataURL(dataURL : string) : string {\r\n\tconst commaIndex = dataURL.indexOf(\",\");\r\n\tconst mimeType = dataURL.substring(0, commaIndex).replace(\"data:\", \"\").replace(\";base64\", \"\");\r\n\tconst mimeTypeParts = mimeType.split(\"/\");\r\n\treturn mimeTypeParts[1];\r\n}\r\nfunction dataURLToBytes(dataURL : string) : ByteArray {\r\n\tconst commaIndex = dataURL.indexOf(\",\");\r\n\tconst base64 = dataURL.substring(commaIndex + 1);\r\n\treturn Base64.decode(base64, Base64.DEFAULT);\r\n}\r\n\r\nexport function dataURLToFile(dataURL : string, filename : NullableString = null) : NullableString {\r\n\ttry {\r\n\t\tconst bytes = dataURLToBytes(dataURL);\r\n\t\tconst name = filename ?? `${Date.now()}.${getFileExtensionFromDataURL(dataURL)}`;\r\n\t\tconst cacheDir = UTSAndroid.getAppCachePath()!;\r\n\t\tconst destFile = new File(cacheDir, name);\r\n\t\tconst path = new File(cacheDir); \r\n\t\tif(!path.exists()){\r\n\t\t\tpath.mkdir(); \r\n\t\t}\r\n\t\tconst fos = new FileOutputStream(destFile)\r\n\t\tfos.write(bytes);\r\n\t\tfos.close();\r\n\t\treturn `${cacheDir}${name}`\r\n\t} catch (e) {\r\n\t\t__f__('error','at uni_modules/lime-file-utils/utssdk/app-android/index.uts:183','dataURLToFile::', e)\r\n\t\treturn null\r\n\t}\r\n}\r\n\r\n\r\n// function requestSystemPermission(fun:()=> void) {\r\n// \tlet permissionNeed = [\"android.permission.WRITE_EXTERNAL_STORAGE\"]\r\n// \tUTSAndroid.requestSystemPermission(UTSAndroid.getUniActivity()!, permissionNeed, function (allRight : boolean, _ : string[]) {\r\n// \t\tif (allRight) {\r\n// \t\t\t// 权限请求成功\r\n// \t\t\t__f__('log','at uni_modules/lime-file-utils/utssdk/app-android/index.uts:194',`allRight`, allRight)\r\n// \t\t\tfun()\r\n// \t\t} else {\r\n// \t\t\t//用户拒绝了部分权限\r\n// \t\t}\r\n// \t}, function (_ : boolean, _ : string[]) {\r\n// \t\t//用户拒绝了部分权限\r\n// \t})\r\n// }\r\n\r\n\r\nexport function processFile(options : ProcessFileOptions) {\r\n\r\n\tif (options.type == 'toBase64') {\r\n\t\tconst res = fileToBase64(options.path)\r\n\t\tconst err = 'fileToBase64: 解析失败'\r\n\t\tif (res != null) {\r\n\t\t\toptions.success?.(res)\r\n\t\t\toptions.complete?.(res)\r\n\t\t} else {\r\n\t\t\toptions.complete?.(err)\r\n\t\t\toptions.fail?.(err)\r\n\t\t}\r\n\t} else if (options.type == 'toDataURL') {\r\n\t\tconst res = fileToDataURL(options.path)\r\n\t\tconst err = 'fileToDataURL: 解析失败'\r\n\t\tif (res != null) {\r\n\t\t\toptions.success?.(res)\r\n\t\t\toptions.complete?.(res)\r\n\t\t} else {\r\n\t\t\toptions.complete?.(err)\r\n\t\t\toptions.fail?.(err)\r\n\t\t}\r\n\t} else if (options.type == 'toFile') {\r\n\t\tconst res = dataURLToFile(options.path, options.filename)\r\n\t\tconst err = 'dataURLToFile: 解析失败'\r\n\t\tif (res != null) {\r\n\t\t\toptions.success?.(res)\r\n\t\t\toptions.complete?.(res)\r\n\t\t} else {\r\n\t\t\toptions.complete?.(err)\r\n\t\t\toptions.fail?.(err)\r\n\t\t}\r\n\t}\r\n}"]}