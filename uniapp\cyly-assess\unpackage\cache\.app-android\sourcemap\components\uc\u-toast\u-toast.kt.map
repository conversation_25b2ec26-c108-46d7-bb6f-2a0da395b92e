{"version": 3, "sources": ["components/uc/u-toast/u-toast.uvue", "main.uts"], "sourcesContent": ["<template>\n  <view class=\"u-toast-container\" :style=\"containerStyle\" v-if=\"visible\">\n    <fuiRow class=\"u-toast\" :class=\"[toastClass, animationClass]\" :style=\"toastStyle\">\n      <!-- 图标 -->\n      <view class=\"u-toast-icon\" v-if=\"showIcon\">\n        <!-- 文字图标 -->\n        <text class=\"u-toast-icon-text\" v-if=\"iconType === 'text'\" style=\"color: white;\">{{ iconValue }}</text>\n        <!-- 自定义图标插槽 -->\n        <view class=\"u-toast-icon-custom\" v-else-if=\"iconType === 'custom'\">\n          <slot name=\"icon\"></slot>\n        </view>\n      </view>\n\n      <!-- 消息内容 -->\n      <view class=\"u-toast-content\">\n        <text class=\"u-toast-message\" :style=\"messageStyle\">{{ message }}</text>\n      </view>\n    </fuiRow>\n  </view>\n</template>\n\n<script lang=\"uts\" setup>\n  import { ToastType, ToastPosition } from \"../types\"\n\timport fuiRow from \"@/components/firstui/fui-row/fui-row\";\n\n  // import lSvg from \"@/uni_modules/lime-svg/components/l-svg/l-svg\"\n\n  // Props 定义\n  const props = defineProps({\n    // 是否显示\n    visible: {\n      type: Boolean,\n      default: false\n    },\n    // 消息内容\n    message: {\n      type: String,\n      default: ''\n    },\n    // 提示类型\n    type: {\n      type: String as PropType<ToastType>,\n      default: 'info' as ToastType\n    },\n    // 自定义背景色\n    backgroundColor: {\n      type: String,\n      default: ''\n    },\n    // 自定义文字颜色\n    textColor: {\n      type: String,\n      default: ''\n    },\n    // 图标\n    icon: {\n      type: String,\n      default: ''\n    },\n    // 图标颜色（用于 SVG 图标）\n    iconColor: {\n      type: String,\n      default: ''\n    },\n    // 是否显示图标\n    showIcon: {\n      type: Boolean,\n      default: true\n    },\n    // 自动关闭时间（毫秒）\n    duration: {\n      type: Number,\n      default: 3000\n    },\n    // 位置\n    position: {\n      type: String as PropType<ToastPosition>,\n      default: 'top' as ToastPosition\n    },\n    // 距离顶部的距离\n    top: {\n      type: Number,\n      default: 0\n    },\n    // 自定义样式\n    customStyle: {\n      type: Object as PropType<UTSJSONObject>,\n      default: {} as UTSJSONObject\n    }\n  })\n\n  // Emits 定义\n  const emit = defineEmits(['close', 'click'])\n\n  // 响应式数据\n  const visible = ref(props.visible)\n  const timer = ref<number | null>(null)\n  const animationClass = ref('')\n  const isAnimating = ref(false)\n\n  // 计算属性 - 获取图标值\n  const iconValue = computed(() => {\n    if (props.icon !== '') return props.icon\n\n    // 根据类型返回默认图标\n    switch (props.type) {\n      case 'success':\n        return '✓'\n      case 'error':\n        return '✕'\n      case 'warning':\n        return '⚠'\n      case 'info':\n        return 'ℹ'\n      default:\n        return '●'\n    }\n  })\n\n  // 计算属性 - 图标类型\n  const iconType = computed(() => {\n    const value = iconValue.value\n    if (value === '') return 'none'\n    // 检查是否是单个字符（emoji 或文字图标）\n    if (value.length <= 2 || /^[\\u{1F600}-\\u{1F64F}]|[\\u{1F300}-\\u{1F5FF}]|[\\u{1F680}-\\u{1F6FF}]|[\\u{1F1E0}-\\u{1F1FF}]|[\\u{2600}-\\u{26FF}]|[\\u{2700}-\\u{27BF}]/u.test(value)) return 'text'\n    return 'custom'\n  })\n\n  const toastClass = computed(() => {\n    const classes = ['u-toast-' + props.type]\n    if (props.position === 'top') classes.push('u-toast-top')\n    if (props.position === 'bottom') classes.push('u-toast-bottom')\n    return classes.join(' ')\n  })\n\n  const containerStyle = computed(() => {\n    const style: UTSJSONObject = {}\n    const topValue = props.top != null ? props.top : 0\n    if (props?.position === 'top') {\n      style['top'] = topValue + 'rpx'\n    } else if (props?.position === 'bottom') {\n      style['bottom'] = topValue + 'rpx'\n    }\n    return style\n  })\n\n  const toastStyle = computed(() => {\n    const style: UTSJSONObject = {}\n    if (props.backgroundColor != null && props.backgroundColor !== '') {\n      style['background-color'] = props.backgroundColor\n    }\n    // 合并自定义样式\n    for (const key in props.customStyle) {\n      style[key] = props.customStyle[key]\n    }\n\n    return style\n  })\n\n  const messageStyle = computed(() => {\n    const style: UTSJSONObject = {}\n    if (props.textColor != null && props.textColor !== '') {\n      style['color'] = props.textColor\n    } else {\n      // 安卓端兼容：确保文字是白色\n      style['color'] = 'white'\n    }\n    return style\n  })\n\n  // 动画方法 - 使用CSS类\n  function startShowAnimation() {\n    console.log('startShowAnimation called')\n    isAnimating.value = true\n\n    // 初始隐藏状态\n    animationClass.value = 'u-toast-enter-from'\n    console.log('Animation class set to:', animationClass.value)\n\n    // 下一帧开始显示动画\n    setTimeout(() => {\n      animationClass.value = 'u-toast-enter-active u-toast-enter-to'\n      console.log('Animation class updated to:', animationClass.value)\n\n      // 动画完成后清理\n      setTimeout(() => {\n        animationClass.value = ''\n        isAnimating.value = false\n        console.log('Animation completed, class cleared')\n      }, 300)\n    }, 50)\n  }\n\n  function startHideAnimation() {\n    isAnimating.value = true\n\n    // 开始隐藏动画\n    animationClass.value = 'u-toast-leave-active u-toast-leave-to'\n\n    setTimeout(() => {\n      animationClass.value = ''\n      isAnimating.value = false\n    }, 200)\n  }\n\n  // 方法 - 先声明 clearTimer\n  function clearTimer() {\n    const timerId = timer.value\n    if (timerId != null) {\n      clearTimeout(timerId)\n      timer.value = null\n    }\n  }\n\n  function hide() {\n    console.log('Toast hide() called')\n\n    // 暂时简化，直接隐藏\n    visible.value = false\n    clearTimer()\n    emit('close')\n  }\n\n  function show() {\n    console.log('Toast show() called, visible:', visible.value, 'isAnimating:', isAnimating.value)\n\n    visible.value = true\n    console.log('Toast visible set to true')\n\n    // 暂时简化，先不使用动画，确保基本功能工作\n    // startShowAnimation()\n\n    // 添加延迟自动隐藏\n    if (props.duration > 0) {\n      clearTimer()\n      timer.value = setTimeout(() => {\n        console.log('Toast auto hide after duration:', props.duration)\n        hide()\n      }, props.duration)\n    }\n  }\n\n  // 监听 props.visible 变化\n  watchEffect(() => {\n    console.log('Toast watchEffect triggered, props.visible:', props.visible, 'props.message:', props.message)\n    if (props.visible) {\n      show()\n    } else {\n      hide()\n    }\n  })\n\n  // 组件卸载时清理定时器\n  onUnmounted(() => {\n    clearTimer()\n  })\n\n  // 暴露方法\n  defineExpose({\n    show,\n    hide\n  })\n</script>\n\n<style lang=\"scss\" scoped>\n.u-toast-container {\n  position: fixed;\n  left: 0;\n  right: 0;\n  z-index: 9999;\n  pointer-events: none;\n  display: flex;\n  justify-content: center;\n  display: flex;\n  padding: 0 32rpx;\n}\n\n.u-toast {\n  display: flex;\n  align-items: center;\n  min-height: 80rpx;\n  padding: 16rpx 24rpx;\n  border-radius: 20px;\n  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);\n\n  pointer-events: auto;\n  max-width: 85%;\n  word-wrap: break-word;\n  border: 1rpx solid rgba(255, 255, 255, 0.2);\n\n\n\n  /* 添加渐变光泽效果 */\n  position: relative;\n  overflow: hidden;\n\n}\n\n\n\n.u-toast-top {\n  transform-origin: top center;\n}\n\n.u-toast-bottom {\n  transform-origin: bottom center;\n}\n\n/* 不同类型的默认样式 */\n.u-toast-success {\n  background: linear-gradient(135deg, rgba(82, 196, 26, 0.95) 0%, rgba(115, 209, 61, 0.95) 100%);\n  color: white;\n  border-color: rgba(82, 196, 26, 0.3);\n}\n\n.u-toast-error {\n  background: linear-gradient(135deg, #e53835e5 0%, rgba(255, 120, 117, 0.95) 100%);\n  color: white;\n  border-color: rgba(255, 77, 79, 0.3);\n}\n\n.u-toast-warning {\n  background: linear-gradient(135deg, rgba(250, 173, 20, 0.95) 0%, rgba(255, 197, 61, 0.95) 100%);\n  color: white;\n  border-color: rgba(250, 173, 20, 0.3);\n}\n\n.u-toast-info {\n  background: linear-gradient(135deg, rgba(24, 144, 255, 0.95) 0%, rgba(64, 169, 255, 0.95) 100%);\n  color: white;\n  border-color: rgba(24, 144, 255, 0.3);\n}\n\n.u-toast-default {\n  background: linear-gradient(135deg, rgba(0, 0, 0, 0.85) 0%, rgba(64, 64, 64, 0.85) 100%);\n  color: white;\n  border-color: rgba(255, 255, 255, 0.1);\n}\n\n.u-toast-icon {\n  margin-right: 16rpx;\n  flex-shrink: 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 36rpx;\n  height: 36rpx;\n  padding: 4rpx;\n  border-radius: 18px;\n  background-color: rgba(255, 255, 255, 0.3);\n}\n\n.u-toast-icon-text {\n  font-size: 24rpx;\n  line-height: 1;\n  font-weight: bold;\n  color: white !important;\n  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);\n}\n\n.u-toast-content {\n  flex: 1;\n  min-width: 0;\n}\n\n.u-toast-message {\n  font-size: 28rpx;\n  line-height: 1.4;\n  word-wrap: break-word;\n  font-weight: 500;\n  color: white !important;\n  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);\n  letter-spacing: 0.3rpx;\n}\n\n\n\n\n\n/* 响应式设计 */\n@media (max-width: 575px) {\n  .u-toast {\n    min-height: 72rpx;\n    padding: 12rpx 20rpx;\n    border-radius: 18px;\n  }\n\n  .u-toast-icon {\n    width: 32rpx;\n    height: 32rpx;\n    margin-right: 10rpx;\n  }\n\n  .u-toast-icon-text {\n    font-size: 20rpx;\n  }\n\n  .u-toast-message {\n    font-size: 26rpx;\n  }\n}\n\n/* 安卓端兼容的动画类 */\n.u-toast-enter-from {\n  opacity: 0;\n  transform: translateY(-20px);\n}\n\n.u-toast-enter-active {\n  transition: opacity 0.3s ease-out, transform 0.3s ease-out;\n}\n\n.u-toast-enter-to {\n  opacity: 1;\n  transform: translateY(0);\n}\n\n.u-toast-leave-active {\n  transition: opacity 0.2s ease-in, transform 0.2s ease-in;\n}\n\n.u-toast-leave-to {\n  opacity: 0;\n  transform: translateY(-10px);\n}\n</style>\n", null], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAsBE,EAAA;;;;;;;kDAAA,EAAA;;;;;;;;;;;;YAMA,IAAM,QAAQ;YAgEd,IAAA,KAAA,OAAA,MAAA,EAAA,OAAA,yBAAA,GAAA,CAAA,EAAA;gBAAA,MAAA,IAAA,CAAA,QAAA;YAAA;YAGA,IAAM,UAAU,IAAI,MAAM,OAAO;YACjC,IAAM,QAAQ,IAAI,MAAM,GAAS,IAAI;YACrC,IAAM,iBAAiB,IAAI;YAC3B,IAAM,cAAc,IAAI,KAAK;YAG7B,IAAM,YAAY,SAAS,OAAA,MAAA,CAAK;gBAC9B,IAAI,MAAM,IAAI,KAAK;oBAAI,OAAO,MAAM,IAAI;;gBAGxC,MAAQ,MAAM,IAAI;oBACX;wBACH,OAAO;oBACJ;wBACH,OAAO;oBACJ;wBACH,OAAO;oBACJ;wBACH,OAAO;oBACT;wBACE,OAAO;;YAEb;;YAGA,IAAM,WAAW,SAAS,OAAA,MAAA,CAAK;gBAC7B,IAAM,QAAQ,UAAU,KAAK;gBAC7B,IAAI,UAAU;oBAAI,OAAO;;gBAEzB,IAAI,MAAM,MAAM,IAAI,CAAC,IAAI,+JAAoI,IAAI,CAAC;oBAAQ,OAAO;;gBACjL,OAAO;YACT;;YAEA,IAAM,aAAa,SAAS,OAAA,MAAA,CAAK;gBAC/B,IAAM,UAAU;oBAAC,aAAa,MAAM,IAAI;iBAAC;gBACzC,IAAI,MAAM,QAAQ,KAAK;oBAAO,QAAQ,IAAI,CAAC;;gBAC3C,IAAI,MAAM,QAAQ,KAAK;oBAAU,QAAQ,IAAI,CAAC;;gBAC9C,OAAO,QAAQ,IAAI,CAAC;YACtB;;YAEA,IAAM,iBAAiB,SAAS,OAAA,cAAK;gBACnC,IAAM,OAAO,gBAAgB,uBAAA,qBAAA,SAAA,sCAAA,GAAA,EAAA,EAAA;iBAAE;gBAC/B,IAAM,WAAW,IAAA,MAAM,GAAG,IAAI,IAAI;oBAAG,MAAM,GAAG;;AAAG,qBAAC;;gBAClD,IAAI,OAAO,aAAa,OAAO;oBAC7B,KAAK,CAAC,MAAM,GAAG,WAAW;uBACrB,IAAI,OAAO,aAAa,UAAU;oBACvC,KAAK,CAAC,SAAS,GAAG,WAAW;;gBAE/B,OAAO;YACT;;YAEA,IAAM,aAAa,SAAS,OAAA,cAAK;gBAC/B,IAAM,OAAO,gBAAgB,uBAAA,qBAAA,SAAA,sCAAA,GAAA,EAAA,EAAA;iBAAE;gBAC/B,IAAI,MAAM,eAAe,IAAI,IAAI,IAAI,MAAM,eAAe,KAAK,IAAI;oBACjE,KAAK,CAAC,mBAAmB,GAAG,MAAM,eAAe;;gBAGnD,IAAW,6BAAO,MAAM,WAAW,EAAE;oBACnC,KAAK,CAAC,IAAI,GAAG,MAAM,WAAW,CAAC,IAAI;;gBAGrC,OAAO;YACT;;YAEA,IAAM,eAAe,SAAS,OAAA,cAAK;gBACjC,IAAM,OAAO,gBAAgB,uBAAA,qBAAA,SAAA,sCAAA,GAAA,EAAA,EAAA;iBAAE;gBAC/B,IAAI,MAAM,SAAS,IAAI,IAAI,IAAI,MAAM,SAAS,KAAK,IAAI;oBACrD,KAAK,CAAC,QAAQ,GAAG,MAAM,SAAS;uBAC3B;oBAEL,KAAK,CAAC,QAAQ,GAAG;;gBAEnB,OAAO;YACT;;YAGA,IAAS,4BAAkB;gBACzB,QAAQ,GAAG,CAAC,6BAA0B;gBACtC,YAAY,KAAK,GAAG,IAAI;gBAGxB,eAAe,KAAK,GAAG;gBACvB,QAAQ,GAAG,CAAC,2BAA2B,eAAe,KAAI,EAAA;gBAG1D,WAAW,KAAK;oBACd,eAAe,KAAK,GAAG;oBACvB,QAAQ,GAAG,CAAC,+BAA+B,eAAe,KAAI,EAAA;oBAG9D,WAAW,KAAK;wBACd,eAAe,KAAK,GAAG;wBACvB,YAAY,KAAK,GAAG,KAAK;wBACzB,QAAQ,GAAG,CAAC,sCAAmC;oBACjD;sBAAG,GAAG;gBACR;kBAAG,EAAE;YACP;gBApBS;YAsBT,IAAS,4BAAkB;gBACzB,YAAY,KAAK,GAAG,IAAI;gBAGxB,eAAe,KAAK,GAAG;gBAEvB,WAAW,KAAK;oBACd,eAAe,KAAK,GAAG;oBACvB,YAAY,KAAK,GAAG,KAAK;gBAC3B;kBAAG,GAAG;YACR;gBAVS;YAaT,IAAS,oBAAU;gBACjB,IAAM,UAAU,MAAM,KAAK;gBAC3B,IAAI,WAAW,IAAI,EAAE;oBACnB,aAAa;oBACb,MAAM,KAAK,GAAG,IAAI;;YAEtB;gBANS;YAQT,IAAS,cAAI;gBACX,QAAQ,GAAG,CAAC,uBAAoB;gBAGhC,QAAQ,KAAK,GAAG,KAAK;gBACrB;gBACA,KAAK;YACP;gBAPS;YAST,IAAS,cAAI;gBACX,QAAQ,GAAG,CAAC,iCAAiC,QAAQ,KAAK,EAAE,gBAAgB,YAAY,KAAI,EAAA;gBAE5F,QAAQ,KAAK,GAAG,IAAI;gBACpB,QAAQ,GAAG,CAAC,6BAA0B;gBAMtC,IAAI,MAAM,QAAQ,GAAG,CAAC,EAAE;oBACtB;oBACA,MAAM,KAAK,GAAG,WAAW,KAAK;wBAC5B,QAAQ,GAAG,CAAC,mCAAmC,MAAM,QAAO,EAAA;wBAC5D;oBACF;sBAAG,MAAM,QAAQ;;YAErB;gBAjBS;YAoBT,YAAY,KAAK;gBACf,QAAQ,GAAG,CAAC,+CAA+C,MAAM,OAAO,EAAE,kBAAkB,MAAM,OAAM,EAAA;gBACxG,IAAI,MAAM,OAAO,EAAE;oBACjB;uBACK;oBACL;;YAEJ;;YAGA,YAAY,KAAK;gBACf;YACF;;YAGA,4BACE,gBACA;;uBAnQF,IAAA,OAAA,MAAA;wDAAM,SAAK,CAAA,EAAsB,WAAK,wEACpC;oCAAa,8CAAC,SAAiD,WAAK,eAAE;4BAAA;4BAAA;gCAAA,MAAU;gCAAA,MAAA;6BAAA;yBAAA,wEAE7C,YAAA,gBAAQ,GAAA;mCAAA;2CAAzC,KAAA,QAAA;wEAAM,SAAK,CAAA,8BAE6B;kDAAtC,cAAA;gFAAM,SAAK,CAAA,EAAgD,WAAqB,+CAAI,SAAA,WAAS,YAEhD,gBAAQ,MAAA,aAAA,CAAA;;sDAArD,cAAA;oFAAM,SAAK,CAAA,qCACT;;;;;;;;;oCAKJ,mBAEO,QAAA,IAAA;iCAAA;mDADL,QAAwE,SAAA,WAAA,oBAAA;uDAA5D,QAAiB,SAAE,WAAK,6CAAmB,MAAA"}