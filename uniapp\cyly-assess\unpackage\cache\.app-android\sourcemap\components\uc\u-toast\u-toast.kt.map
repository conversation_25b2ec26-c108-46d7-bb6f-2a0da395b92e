{"version": 3, "sources": ["components/uc/u-toast/u-toast.uvue", "uni_modules/lime-file-utils/utssdk/interface.uts"], "sourcesContent": ["<template>\n  <view class=\"u-toast-container\" v-if=\"visible\" :style=\"containerStyle\">\n    <view class=\"u-toast\" :class=\"toastClass\" :style=\"toastStyle\">\n      <!-- 图标 -->\n      <view class=\"u-toast-icon\" v-if=\"showIcon\">\n        <!-- SVG 图标 -->\n        <l-svg v-if=\"iconType === 'svg'\" class=\"u-toast-icon-svg\" :src=\"iconSrc\" :color=\"iconColor\" />\n        <!-- 文字图标 -->\n        <text class=\"u-toast-icon-text\" v-else-if=\"iconType === 'text'\">{{ icon }}</text>\n        <!-- 自定义图标插槽 -->\n        <view class=\"u-toast-icon-custom\" v-else-if=\"iconType === 'custom'\">\n          <slot name=\"icon\"></slot>\n        </view>\n      </view>\n\n      <!-- 消息内容 -->\n      <view class=\"u-toast-content\">\n        <text class=\"u-toast-message\" :style=\"messageStyle\">{{ message }}</text>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script lang=\"uts\" setup>\n  import { ToastType, ToastPosition } from \"../types\"\n  import lSvg from \"@/uni_modules/lime-svg/components/l-svg/l-svg\"\n\n  // Props 定义\n  const props = defineProps({\n    // 是否显示\n    visible: {\n      type: Boolean,\n      default: false\n    },\n    // 消息内容\n    message: {\n      type: String,\n      default: ''\n    },\n    // 提示类型\n    type: {\n      type: String as PropType<ToastType>,\n      default: 'info' as ToastType\n    },\n    // 自定义背景色\n    backgroundColor: {\n      type: String,\n      default: ''\n    },\n    // 自定义文字颜色\n    textColor: {\n      type: String,\n      default: ''\n    },\n    // 图标\n    icon: {\n      type: String,\n      default: ''\n    },\n    // 图标颜色（用于 SVG 图标）\n    iconColor: {\n      type: String,\n      default: ''\n    },\n    // 是否显示图标\n    showIcon: {\n      type: Boolean,\n      default: true\n    },\n    // 自动关闭时间（毫秒）\n    duration: {\n      type: Number,\n      default: 3000\n    },\n    // 位置\n    position: {\n      type: String as PropType<ToastPosition>,\n      default: 'top' as ToastPosition\n    },\n    // 距离顶部的距离\n    top: {\n      type: Number,\n      default: 0\n    },\n    // 自定义样式\n    customStyle: {\n      type: Object as PropType<UTSJSONObject>,\n      default: {} as UTSJSONObject\n    }\n  })\n\n  // Emits 定义\n  const emit = defineEmits(['close', 'click'])\n\n  // 响应式数据\n  const visible = ref(props.visible)\n  const timer = ref<number | null>(null)\n\n  // 计算属性\n  const iconType = computed(() => {\n    if (props.icon === '') return 'none'\n    // 检查是否是 SVG 文件路径\n    if (props.icon.startsWith('/static/icons/') && props.icon.endsWith('.svg')) return 'svg'\n    // 检查是否是单个字符（emoji 或文字图标）\n    if (props.icon.length === 1 || /^[\\u{1F600}-\\u{1F64F}]|[\\u{1F300}-\\u{1F5FF}]|[\\u{1F680}-\\u{1F6FF}]|[\\u{1F1E0}-\\u{1F1FF}]|[\\u{2600}-\\u{26FF}]|[\\u{2700}-\\u{27BF}]/u.test(props.icon)) return 'text'\n    return 'custom'\n  })\n\n  const iconSrc = computed(() => {\n    if (iconType.value === 'svg') {\n      return props.icon\n    }\n    return ''\n  })\n\n  const toastClass = computed(() => {\n    const classes = ['u-toast-' + props.type]\n    if (props.position === 'top') classes.push('u-toast-top')\n    if (props.position === 'bottom') classes.push('u-toast-bottom')\n    return classes.join(' ')\n  })\n\n  const containerStyle = computed(() => {\n    const style: UTSJSONObject = {}\n    const topValue = props.top != null ? props.top : 0\n    if (props?.position === 'top') {\n      style['top'] = topValue + 'rpx'\n    } else if (props?.position === 'bottom') {\n      style['bottom'] = topValue + 'rpx'\n    }\n    return style\n  })\n\n  const toastStyle = computed(() => {\n    const style: UTSJSONObject = {}\n    if (props.backgroundColor) {\n      style['background-color'] = props.backgroundColor\n    }\n    // 合并自定义样式\n    Object.keys(props.customStyle).forEach(key => {\n      style[key] = props.customStyle[key]\n    })\n    return style\n  })\n\n  const messageStyle = computed(() => {\n    const style: UTSJSONObject = {}\n    if (props.textColor) {\n      style['color'] = props.textColor\n    }\n    return style\n  })\n\n  // 方法\n  function show() {\n    visible.value = true\n    if (props.duration > 0) {\n      clearTimer()\n      timer.value = setTimeout(() => {\n        hide()\n      }, props.duration)\n    }\n  }\n\n  function hide() {\n    visible.value = false\n    clearTimer()\n    emit('close')\n  }\n\n  function clearTimer() {\n    if (timer.value) {\n      clearTimeout(timer.value)\n      timer.value = null\n    }\n  }\n\n  // 监听 props.visible 变化\n  watch(() => props.visible, (newVal) => {\n    if (newVal) {\n      show()\n    } else {\n      hide()\n    }\n  }, { immediate: true })\n\n  // 组件卸载时清理定时器\n  onUnmounted(() => {\n    clearTimer()\n  })\n\n  // 暴露方法\n  defineExpose({\n    show,\n    hide\n  })\n</script>\n\n<style lang=\"scss\" scoped>\n.u-toast-container {\n  position: fixed;\n  left: 0;\n  right: 0;\n  z-index: 9999;\n  pointer-events: none;\n  display: flex;\n  justify-content: center;\n  padding: 0 32rpx;\n}\n\n.u-toast {\n  display: flex;\n  align-items: center;\n  min-height: 88rpx;\n  padding: 16rpx 24rpx;\n  border-radius: 12rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\n  backdrop-filter: blur(10rpx);\n  pointer-events: auto;\n  max-width: 100%;\n  word-wrap: break-word;\n\n  /* 动画效果 */\n  animation: toastSlideIn 0.3s ease-out;\n}\n\n.u-toast-top {\n  transform-origin: top center;\n}\n\n.u-toast-bottom {\n  transform-origin: bottom center;\n}\n\n/* 不同类型的默认样式 */\n.u-toast-success {\n  background-color: rgba(82, 196, 26, 0.9);\n  color: white;\n}\n\n.u-toast-error {\n  background-color: rgba(255, 77, 79, 0.9);\n  color: white;\n}\n\n.u-toast-warning {\n  background-color: rgba(250, 173, 20, 0.9);\n  color: white;\n}\n\n.u-toast-info {\n  background-color: rgba(24, 144, 255, 0.9);\n  color: white;\n}\n\n.u-toast-default {\n  background-color: rgba(0, 0, 0, 0.8);\n  color: white;\n}\n\n.u-toast-icon {\n  margin-right: 12rpx;\n  flex-shrink: 0;\n}\n\n.u-toast-icon-text {\n  font-size: 32rpx;\n  line-height: 1;\n}\n\n.u-toast-icon-svg {\n  width: 32rpx;\n  height: 32rpx;\n}\n\n.u-toast-content {\n  flex: 1;\n  min-width: 0;\n}\n\n.u-toast-message {\n  font-size: 28rpx;\n  line-height: 1.4;\n  word-wrap: break-word;\n}\n\n/* 动画 */\n@keyframes toastSlideIn {\n  0% {\n    opacity: 0;\n    transform: translateY(-20rpx) scale(0.95);\n  }\n\n  100% {\n    opacity: 1;\n    transform: translateY(0) scale(1);\n  }\n}\n\n/* 响应式设计 */\n@media (max-width: 575px) {\n  .u-toast {\n    min-height: 80rpx;\n    padding: 14rpx 20rpx;\n  }\n\n  .u-toast-message {\n    font-size: 26rpx;\n  }\n\n  .u-toast-icon-text {\n    font-size: 28rpx;\n  }\n}\n</style>\n", null], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAwBE,EAAA;;;;;;;kDAAA,EAAA;;;;;;;;;;;;YAIA,IAAM,QAAQ;YAgEd,IAAA,KAAA,OAAA,MAAA,EAAA,OAAA,yBAAA,GAAA,CAAA,EAAA;gBAAA,MAAA,IAAA,CAAA,QAAA;YAAA;YAGA,IAAM,UAAU,IAAI,MAAM,OAAO;YACjC,IAAM,QAAQ,IAAI,MAAM,GAAS,IAAI;YAGrC,IAAM,WAAW,SAAS,OAAA,MAAA,CAAK;gBAC7B,IAAI,MAAM,IAAI,KAAK;oBAAI,OAAO;;gBAE9B,IAAI,MAAM,IAAI,CAAC,UAAU,CAAC,qBAAqB,MAAM,IAAI,CAAC,QAAQ,CAAC;oBAAS,OAAO;;gBAEnF,IAAI,MAAM,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,+JAAoI,IAAI,CAAC,MAAM,IAAI;oBAAG,OAAO;;gBAC5L,OAAO;YACT;;YAEA,IAAM,UAAU,SAAS,OAAA,MAAA,CAAK;gBAC5B,IAAI,SAAS,KAAK,KAAK,OAAO;oBAC5B,OAAO,MAAM,IAAI;;gBAEnB,OAAO;YACT;;YAEA,IAAM,aAAa,SAAS,OAAA,MAAA,CAAK;gBAC/B,IAAM,UAAU;oBAAC,aAAa,MAAM,IAAI;iBAAC;gBACzC,IAAI,MAAM,QAAQ,KAAK;oBAAO,QAAQ,IAAI,CAAC;;gBAC3C,IAAI,MAAM,QAAQ,KAAK;oBAAU,QAAQ,IAAI,CAAC;;gBAC9C,OAAO,QAAQ,IAAI,CAAC;YACtB;;YAEA,IAAM,iBAAiB,SAAS,OAAA,cAAK;gBACnC,IAAM,OAAO,gBAAgB,uBAAA,qBAAA,SAAA,sCAAA,GAAA,EAAA,EAAA;iBAAE;gBAC/B,IAAM,WAAW,IAAA,MAAM,GAAG,IAAI,IAAI;oBAAG,MAAM,GAAG;;AAAG,qBAAC;;gBAClD,IAAI,OAAO,aAAa,OAAO;oBAC7B,KAAK,CAAC,MAAM,GAAG,WAAW;uBACrB,IAAI,OAAO,aAAa,UAAU;oBACvC,KAAK,CAAC,SAAS,GAAG,WAAW;;gBAE/B,OAAO;YACT;;YAEA,IAAM,aAAa,SAAS,OAAA,cAAK;gBAC/B,IAAM,OAAO,gBAAgB,uBAAA,qBAAA,SAAA,sCAAA,GAAA,EAAA,EAAA;iBAAE;gBAC/B,IAAI,MAAM,eAAe,EAAE;oBACzB,KAAK,CAAC,mBAAmB,GAAG,MAAM,eAAe;;gBAGnD,OAAO,IAAI,CAAC,MAAM,WAAW,EAAE,OAAO,CAAC,IAAA,IAAM;oBAC3C,KAAK,CAAC,IAAI,GAAG,MAAM,WAAW,CAAC,IAAI;gBACrC;;gBACA,OAAO;YACT;;YAEA,IAAM,eAAe,SAAS,OAAA,cAAK;gBACjC,IAAM,OAAO,gBAAgB,uBAAA,qBAAA,SAAA,sCAAA,GAAA,EAAA,EAAA;iBAAE;gBAC/B,IAAI,MAAM,SAAS,EAAE;oBACnB,KAAK,CAAC,QAAQ,GAAG,MAAM,SAAS;;gBAElC,OAAO;YACT;;YAGA,IAAS,cAAI;gBACX,QAAQ,KAAK,GAAG,IAAI;gBACpB,IAAI,MAAM,QAAQ,GAAG,CAAC,EAAE;oBACtB;oBACA,MAAM,KAAK,GAAG,WAAW,KAAK;wBAC5B;oBACF;sBAAG,MAAM,QAAQ;;YAErB;gBARS;YAUT,IAAS,cAAI;gBACX,QAAQ,KAAK,GAAG,KAAK;gBACrB;gBACA,KAAK;YACP;gBAJS;YAMT,IAAS,oBAAU;gBACjB,IAAI,MAAM,KAAK,EAAE;oBACf,aAAa,MAAM,KAAK;oBACxB,MAAM,KAAK,GAAG,IAAI;;YAEtB;gBALS;YAQT,MAAM;uBAAM,MAAM,OAAO;;cAAE,IAAC,OAAU;gBACpC,IAAI,QAAQ;oBACV;uBACK;oBACL;;YAEJ;2BAAK,YAAW,IAAI;YAGpB,YAAY,KAAK;gBACf;YACF;;YAGA,4BACE,gBACA;;uBAjMF,IAAA,OAAA,MAAA;wDAAM,SAAK,CAAA,EAAqC,WAAK,wEACnD;2CAAW,QAAC,SAA+B,WAAK,eAAE;4BAAA;4BAAA,MAAU;yBAAA;uCAE1D,KAAA,QAAA;oEAAM,SAAK,CAAA,8BAEI;8CAAb,cAA8F;iHAA7D,SAAK,CAAA,EAAqB,WAAK,oBAAU,SAAK,MAAE,sCAEtC,IAAA,EAAA,CAAA,EAAQ;4CAAA;4CAAA;yCAAA;;kDAAnD,cAAA;gFAAM,SAAK,CAAA,mCAEkC,gBAAQ,KAAA,IAAA,GAAA,CAAA;;sDAArD,cAAA;oFAAM,SAAK,CAAA,qCACT;;;;;;;;;;gCAKJ,mBAEO,QAAA,IAAA;6BAAA;+CADL,QAAwE,SAAA,WAAA,oBAAA;mDAA5D,QAAiB,SAAE,WAAK,6CAAmB,MAAA"}