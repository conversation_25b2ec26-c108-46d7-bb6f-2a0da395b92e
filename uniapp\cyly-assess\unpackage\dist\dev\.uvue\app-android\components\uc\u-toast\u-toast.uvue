import { ToastType, ToastPosition } from "../types";
// import lSvg from "@/uni_modules/lime-svg/components/l-svg/l-svg"
// Props 定义
const __sfc__ = defineComponent({
    __name: 'u-toast',
    props: {
        // 是否显示
        visible: {
            type: Boolean,
            default: false
        },
        // 消息内容
        message: {
            type: String,
            default: ''
        },
        // 提示类型
        type: {
            type: String as PropType<ToastType>,
            default: 'info' as ToastType
        },
        // 自定义背景色
        backgroundColor: {
            type: String,
            default: ''
        },
        // 自定义文字颜色
        textColor: {
            type: String,
            default: ''
        },
        // 图标
        icon: {
            type: String,
            default: ''
        },
        // 图标颜色（用于 SVG 图标）
        iconColor: {
            type: String,
            default: ''
        },
        // 是否显示图标
        showIcon: {
            type: Boolean,
            default: true
        },
        // 自动关闭时间（毫秒）
        duration: {
            type: Number,
            default: 3000
        },
        // 位置
        position: {
            type: String as PropType<ToastPosition>,
            default: 'top' as ToastPosition
        },
        // 距离顶部的距离
        top: {
            type: Number,
            default: 0
        },
        // 自定义样式
        customStyle: {
            type: Object as PropType<UTSJSONObject>,
            default: {} as UTSJSONObject
        }
    },
    emits: ['close', 'click'],
    setup(__props, { expose: __expose }: SetupContext): any | null {
        const __ins = getCurrentInstance()!;
        const _ctx = __ins.proxy as InstanceType<typeof __sfc__>;
        const _cache = __ins.renderCache;
        const props = __props;
        // Emits 定义
        function emit(event: string, ...do_not_transform_spread: Array<any | null>) {
            __ins.emit(event, ...do_not_transform_spread);
        }
        // 响应式数据
        const visible = ref(props.visible);
        const timer = ref<number | null>(null);
        const animating = ref(false);
        // 计算属性 - 获取图标值
        const iconValue = computed((): string => {
            if (props.icon !== '')
                return props.icon;
            // 根据类型返回默认图标
            switch (props.type) {
                case 'success':
                    return '✓';
                case 'error':
                    return '✕';
                case 'warning':
                    return '⚠';
                case 'info':
                    return 'ℹ';
                default:
                    return '●';
            }
        });
        // 计算属性 - 图标类型
        const iconType = computed((): string => {
            const value = iconValue.value;
            if (value === '')
                return 'none';
            // 检查是否是单个字符（emoji 或文字图标）
            if (value.length <= 2 || /^[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/u.test(value))
                return 'text';
            return 'custom';
        });
        const toastClass = computed((): string => {
            const classes = ['u-toast-' + props.type];
            if (props.position === 'top')
                classes.push('u-toast-top');
            if (props.position === 'bottom')
                classes.push('u-toast-bottom');
            return classes.join(' ');
        });
        const containerStyle = computed((): UTSJSONObject => {
            const style: UTSJSONObject = { __$originalPosition: new UTSSourceMapPosition("style", "components/uc/u-toast/u-toast.uvue", 135, 11), };
            const topValue = props.top != null ? props.top : 0;
            if (props?.position === 'top') {
                style['top'] = topValue + 'rpx';
            }
            else if (props?.position === 'bottom') {
                style['bottom'] = topValue + 'rpx';
            }
            return style;
        });
        const toastStyle = computed((): UTSJSONObject => {
            const style: UTSJSONObject = { __$originalPosition: new UTSSourceMapPosition("style", "components/uc/u-toast/u-toast.uvue", 146, 11), };
            if (props.backgroundColor != null && props.backgroundColor !== '') {
                style['background-color'] = props.backgroundColor;
            }
            // 合并自定义样式
            for (const key in props.customStyle) {
                style[key] = props.customStyle[key];
            }
            return style;
        });
        const messageStyle = computed((): UTSJSONObject => {
            const style: UTSJSONObject = { __$originalPosition: new UTSSourceMapPosition("style", "components/uc/u-toast/u-toast.uvue", 158, 11), };
            if (props.textColor != null && props.textColor !== '') {
                style['color'] = props.textColor;
            }
            return style;
        });
        // 方法 - 先声明 clearTimer
        function clearTimer() {
            const timerId = timer.value;
            if (timerId != null) {
                clearTimeout(timerId);
                timer.value = null;
            }
        }
        function hide() {
            if (animating.value)
                return;
            animating.value = true;
            visible.value = false;
            clearTimer();
            // 延迟触发 close 事件，等待动画完成
            setTimeout(() => {
                animating.value = false;
                emit('close');
            }, 200);
        }
        function show() {
            if (animating.value)
                return;
            animating.value = true;
            visible.value = true;
            // 动画完成后重置状态
            setTimeout(() => {
                animating.value = false;
            }, 300);
            // 添加延迟自动隐藏
            if (props.duration > 0) {
                clearTimer();
                timer.value = setTimeout(() => {
                    hide();
                }, props.duration);
            }
        }
        // 监听 props.visible 变化
        watchEffect(() => {
            if (props.visible) {
                show();
            }
            else {
                hide();
            }
        });
        // 组件卸载时清理定时器
        onUnmounted(() => {
            clearTimer();
        });
        // 暴露方法
        __expose({
            show,
            hide
        });
        return (): any | null => {
            return isTrue(unref(visible) || unref(animating))
                ? createElementVNode("view", utsMapOf({
                    key: 0,
                    class: "u-toast-container",
                    style: normalizeStyle(unref(containerStyle))
                }), [
                    createElementVNode("view", utsMapOf({
                        class: normalizeClass(["u-toast", [unref(toastClass), utsMapOf({ 'u-toast-show': unref(visible), 'u-toast-hide': !unref(visible) && unref(animating) })]]),
                        style: normalizeStyle(unref(toastStyle))
                    }), [
                        isTrue(_ctx.showIcon)
                            ? createElementVNode("view", utsMapOf({
                                key: 0,
                                class: "u-toast-icon"
                            }), [
                                unref(iconType) === 'text'
                                    ? createElementVNode("text", utsMapOf({
                                        key: 0,
                                        class: "u-toast-icon-text"
                                    }), toDisplayString(unref(iconValue)), 1 /* TEXT */)
                                    : unref(iconType) === 'custom'
                                        ? createElementVNode("view", utsMapOf({
                                            key: 1,
                                            class: "u-toast-icon-custom"
                                        }), [
                                            renderSlot(_ctx.$slots, "icon")
                                        ])
                                        : createCommentVNode("v-if", true)
                            ])
                            : createCommentVNode("v-if", true),
                        createElementVNode("view", utsMapOf({ class: "u-toast-content" }), [
                            createElementVNode("text", utsMapOf({
                                class: "u-toast-message",
                                style: normalizeStyle(unref(messageStyle))
                            }), toDisplayString(_ctx.message), 5 /* TEXT, STYLE */)
                        ])
                    ], 6 /* CLASS, STYLE */)
                ], 4 /* STYLE */)
                : createCommentVNode("v-if", true);
        };
    }
});
export default __sfc__;
const GenComponentsUcUToastUToastStyles = [utsMapOf([["u-toast-container", padStyleMapOf(utsMapOf([["position", "fixed"], ["left", 0], ["right", 0], ["zIndex", 9999], ["pointerEvents", "none"], ["display", "flex"], ["justifyContent", "center"], ["paddingTop", 0], ["paddingRight", "32rpx"], ["paddingBottom", 0], ["paddingLeft", "32rpx"]]))], ["u-toast", padStyleMapOf(utsMapOf([["display", "flex"], ["alignItems", "center"], ["minHeight", "80rpx"], ["paddingTop", "16rpx"], ["paddingRight", "24rpx"], ["paddingBottom", "16rpx"], ["paddingLeft", "24rpx"], ["borderTopLeftRadius", "40rpx"], ["borderTopRightRadius", "40rpx"], ["borderBottomRightRadius", "40rpx"], ["borderBottomLeftRadius", "40rpx"], ["boxShadow", "0 8rpx 24rpx rgba(0, 0, 0, 0.12)"], ["backdropFilter", "blur(20rpx)"], ["pointerEvents", "auto"], ["wordWrap", "break-word"], ["borderTopWidth", "1rpx"], ["borderRightWidth", "1rpx"], ["borderBottomWidth", "1rpx"], ["borderLeftWidth", "1rpx"], ["borderTopStyle", "solid"], ["borderRightStyle", "solid"], ["borderBottomStyle", "solid"], ["borderLeftStyle", "solid"], ["borderTopColor", "rgba(255,255,255,0.2)"], ["borderRightColor", "rgba(255,255,255,0.2)"], ["borderBottomColor", "rgba(255,255,255,0.2)"], ["borderLeftColor", "rgba(255,255,255,0.2)"], ["position", "relative"], ["overflow", "hidden"], ["content::before", "\"\""], ["position::before", "absolute"], ["top::before", 0], ["left::before", "-100%"], ["width::before", "100%"], ["height::before", "100%"], ["backgroundImage::before", "linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent)"], ["backgroundColor::before", "rgba(0,0,0,0)"], ["animation::before", "shine 2s ease-in-out infinite"], ["opacity", 0], ["transform", "translateY(-20rpx)"], ["transitionDuration", "0.3s"], ["transitionTimingFunction", "ease-out"]]))], ["u-toast-top", padStyleMapOf(utsMapOf([["transformOrigin", "top center"]]))], ["u-toast-bottom", padStyleMapOf(utsMapOf([["transformOrigin", "bottom center"]]))], ["u-toast-success", padStyleMapOf(utsMapOf([["backgroundImage", "linear-gradient(135deg, rgba(82, 196, 26, 0.95) 0%, rgba(115, 209, 61, 0.95) 100%)"], ["backgroundColor", "rgba(0,0,0,0)"], ["color", "#FFFFFF"], ["borderTopColor", "rgba(82,196,26,0.3)"], ["borderRightColor", "rgba(82,196,26,0.3)"], ["borderBottomColor", "rgba(82,196,26,0.3)"], ["borderLeftColor", "rgba(82,196,26,0.3)"]]))], ["u-toast-error", padStyleMapOf(utsMapOf([["backgroundImage", "linear-gradient(135deg, rgba(255, 77, 79, 0.95) 0%, rgba(255, 120, 117, 0.95) 100%)"], ["backgroundColor", "rgba(0,0,0,0)"], ["color", "#FFFFFF"], ["borderTopColor", "rgba(255,77,79,0.3)"], ["borderRightColor", "rgba(255,77,79,0.3)"], ["borderBottomColor", "rgba(255,77,79,0.3)"], ["borderLeftColor", "rgba(255,77,79,0.3)"]]))], ["u-toast-warning", padStyleMapOf(utsMapOf([["backgroundImage", "linear-gradient(135deg, rgba(250, 173, 20, 0.95) 0%, rgba(255, 197, 61, 0.95) 100%)"], ["backgroundColor", "rgba(0,0,0,0)"], ["color", "#FFFFFF"], ["borderTopColor", "rgba(250,173,20,0.3)"], ["borderRightColor", "rgba(250,173,20,0.3)"], ["borderBottomColor", "rgba(250,173,20,0.3)"], ["borderLeftColor", "rgba(250,173,20,0.3)"]]))], ["u-toast-info", padStyleMapOf(utsMapOf([["backgroundImage", "linear-gradient(135deg, rgba(24, 144, 255, 0.95) 0%, rgba(64, 169, 255, 0.95) 100%)"], ["backgroundColor", "rgba(0,0,0,0)"], ["color", "#FFFFFF"], ["borderTopColor", "rgba(24,144,255,0.3)"], ["borderRightColor", "rgba(24,144,255,0.3)"], ["borderBottomColor", "rgba(24,144,255,0.3)"], ["borderLeftColor", "rgba(24,144,255,0.3)"]]))], ["u-toast-default", padStyleMapOf(utsMapOf([["backgroundImage", "linear-gradient(135deg, rgba(0, 0, 0, 0.85) 0%, rgba(64, 64, 64, 0.85) 100%)"], ["backgroundColor", "rgba(0,0,0,0)"], ["color", "#FFFFFF"], ["borderTopColor", "rgba(255,255,255,0.1)"], ["borderRightColor", "rgba(255,255,255,0.1)"], ["borderBottomColor", "rgba(255,255,255,0.1)"], ["borderLeftColor", "rgba(255,255,255,0.1)"]]))], ["u-toast-icon", padStyleMapOf(utsMapOf([["marginRight", "12rpx"], ["flexShrink", 0], ["display", "flex"], ["alignItems", "center"], ["justifyContent", "center"], ["width", "36rpx"], ["height", "36rpx"], ["backgroundColor", "rgba(255,255,255,0.25)"], ["backdropFilter", "blur(8rpx)"]]))], ["u-toast-icon-text", padStyleMapOf(utsMapOf([["fontSize", "24rpx"], ["lineHeight", 1], ["fontWeight", "bold"], ["textShadow", "0 1rpx 2rpx rgba(0, 0, 0, 0.2)"]]))], ["u-toast-content", padStyleMapOf(utsMapOf([["flex", 1], ["minWidth", 0]]))], ["u-toast-message", padStyleMapOf(utsMapOf([["fontSize", "28rpx"], ["lineHeight", 1.4], ["wordWrap", "break-word"], ["textShadow", "0 1rpx 2rpx rgba(0, 0, 0, 0.1)"], ["letterSpacing", "0.3rpx"]]))], ["u-toast-show", padStyleMapOf(utsMapOf([["opacity", 1], ["transform", "translateY(0)"]]))], ["u-toast-hide", padStyleMapOf(utsMapOf([["opacity", 0], ["transform", "translateY(-10rpx)"], ["transitionDuration", "0.2s"], ["transitionTimingFunction", "ease-in"]]))], ["@FONT-FACE", utsMapOf([["0", utsMapOf([])], ["1", utsMapOf([["u-toast", utsMapOf([["", utsMapOf([["minHeight", "72rpx"], ["paddingTop", "12rpx"], ["paddingRight", "20rpx"], ["paddingBottom", "12rpx"], ["paddingLeft", "20rpx"], ["borderTopLeftRadius", "36rpx"], ["borderTopRightRadius", "36rpx"], ["borderBottomRightRadius", "36rpx"], ["borderBottomLeftRadius", "36rpx"]])]])], ["u-toast-icon", utsMapOf([["", utsMapOf([["width", "32rpx"], ["height", "32rpx"], ["marginRight", "10rpx"]])]])], ["u-toast-icon-text", utsMapOf([["", utsMapOf([["fontSize", "20rpx"]])]])], ["u-toast-message", utsMapOf([["", utsMapOf([["fontSize", "26rpx"]])]])]])]])], ["@TRANSITION", utsMapOf([["u-toast", utsMapOf([["duration", "0.3s"], ["timingFunction", "ease-out"]])], ["u-toast-hide", utsMapOf([["duration", "0.2s"], ["timingFunction", "ease-in"]])]])]])];
//# sourceMappingURL=u-toast.uvue.map