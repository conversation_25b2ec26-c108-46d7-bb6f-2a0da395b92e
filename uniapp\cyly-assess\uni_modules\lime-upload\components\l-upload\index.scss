@import '~@/uni_modules/lime-style/index.scss';
$prefix: l !default;
$upload: #{$prefix}-upload;

$upload-add-color: create-var(upload-add-color, $text-color-4);
$upload-add-bg-color: create-var(upload-add-bg-color, $gray-1);
$upload-add-disabled-bg-color: create-var(upload-add-disabled-bg-color, $gray-2);
$upload-add-icon-font-size: create-var(upload-add-icon-font-size, 28px);
// $upload-add-icon-disabled-color: create-var(upload-add-icon-disabled-color, $text-color-4);

$upload-radius: create-var(upload-radius, $border-radius);
$upload-width: create-var(upload-width, 80px);
$upload-height: create-var(upload-height, 80px);
$upload-background: create-var(upload-background, $upload-add-bg-color);
$upload-delete-icon-color: create-var(upload-delete-icon-color, white);
$upload-gap: create-var(upload-gap, 8px);

// $upload-disabled-mask: create-var(upload-disabled-mask, rgba(0, 0, 0, 0.6));
// $upload-drag-z-index: create-var(upload-drag-z-index, 999);

.#{$upload} {
	/* #ifndef UNI-APP-X */
	display: flex;
	/* #endif */
	flex: 1;
	position: relative;
	flex-direction: row;
	flex-wrap: wrap;

	&__item {
	    position: relative;
		display: flex;
	    // flex: 1;
		/* #ifdef APP-ANDROID || APP-IOS || APP-HARMONY ||  UNI-APP-X && WEB */
		width: $upload-width;
	    height: $upload-height;
		border-radius: $upload-radius;
		overflow: hidden;
		align-items: center;
		justify-content: center;
		/* #endif */
		/* #ifndef APP-ANDROID || APP-IOS || APP-HARMONY || UNI-APP-X && WEB */
		&-inner {
			overflow: hidden;
			display: flex;
			border-radius: $upload-radius;
			align-items: center;
			justify-content: center;
			min-width: $upload-width;
			// width: 100%;
			// height: 100%;
			// flex: 1;
			height: $upload-height;
			position: relative;
			
		}
		
		/* #endif */
	    &--add {
			/* #ifdef APP-ANDROID || APP-IOS || APP-HARMONY ||  UNI-APP-X && WEB */
			background: $upload-background;
			/* #endif */
			
			/* #ifndef APP-ANDROID || APP-IOS || APP-HARMONY ||  UNI-APP-X && WEB */
			& > view {
				background: $upload-background;
			}
			/* #endif */
			
			/* #ifndef APP-ANDROID || APP-IOS || APP-HARMONY */
			font-size: $upload-add-icon-font-size;
			color: $upload-add-color;
			// text {
			// 	font-size: inherit;
			// }
			/* #endif*/
	    }
		&--disabled {
			// background-color: $upload-add-disabled-bg-color;
			// color: $upload-add-icon-disabled-color;
			opacity: 0.5;
			/* #ifndef APP-ANDROID || APP-IOS || APP-HARMONY */
			cursor: not-allowed;
			/* #endif */
		}
	}
	&__image {
		width: 100%;
		height: 100%;
	}
	&__add-icon {
		font-size: $upload-add-icon-font-size;
		color: $upload-add-color;
	    // position: absolute;
	    // top: 50%;
	    // left: 50%;
	    // transform: translate3d(-50%, -50%, 0);
	}
	&__delete-btn {
	    position: absolute;
	    top: 0;
	    right: 0;
	    display: flex;
	    align-items: center;
	    justify-content: center;
	    width: 20px;
	    height: 20px;
	    
	    border-top-right-radius: $upload-radius;
	    border-bottom-left-radius: $upload-radius;
	    background-color: $text-color-2;
		/* #ifndef APP-ANDROID || APP-IOS || APP-HARMONY*/
		color: $upload-delete-icon-color;
		/* #endif */
	}
	 &__progress {
	    &-mask {
			position: absolute;
			left: 0;
			top: 0;
			bottom: 0;
			width: 100%;
			background-color: $bg-color-mask;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			border-radius: $upload-radius;
			padding: 16px 0;
			/* #ifndef APP-ANDROID || APP-IOS || APP-HARMONY */
			display: flex;
			color: white;
			/* #endif */
	    }
	
	    &-text {
			font-size: 12px;
			line-height: 20px;
			margin-top: 4px;
			color: white;
	    }
	
	    &-loading {
			align-self: center;
	    }
		&-icon {
			color: white;
			font-size: 24px !important;
			width: 24px!important;
		}
	}
}