{"version": 3, "sources": ["uni_modules/lime-file-utils/utssdk/app-android/index.uts", "uni_modules/lime-file-utils/utssdk/interface.uts"], "sourcesContent": ["import Base64 from \"android.util.Base64\";\r\nimport MimeTypeMap from \"android.webkit.MimeTypeMap\";\r\nimport ByteArrayOutputStream from 'java.io.ByteArrayOutputStream';\r\n\r\nimport File from \"java.io.File\";\r\nimport FileInputStream from \"java.io.FileInputStream\";\r\nimport FileOutputStream from \"java.io.FileOutputStream\";\r\nimport InputStream from 'java.io.InputStream';\r\n\r\n// import IOException from \"java.io.IOException\";\r\nimport { ProcessFileOptions, NullableString } from '../interface'\r\ntype NullByteArray = ByteArray | null\r\n\r\nfunction inputStreamToArray(inputStream : InputStream) : NullByteArray {\r\n\ttry {\r\n\t\tlet bos : ByteArrayOutputStream = new ByteArrayOutputStream()\r\n\t\tlet bytes : ByteArray = new ByteArray(1024)\r\n\r\n\t\tdo {\r\n\t\t\tlet length = inputStream.read(bytes)\r\n\t\t\tif (length != -1) {\r\n\t\t\t\tbos.write(bytes, 0, length)\r\n\t\t\t} else {\r\n\t\t\t\tbreak\r\n\t\t\t}\r\n\t\t} while (true)\r\n\t\tbos.close()\r\n\t\treturn bos.toByteArray()\r\n\t} catch (e : Throwable) {\r\n\t\treturn null;\r\n\t}\r\n}\r\n\r\nfunction getMimeType(filePath : string) : NullableString {\r\n\tconst extension = MimeTypeMap.getFileExtensionFromUrl(filePath);\r\n\tif (extension == null) return null\r\n\treturn MimeTypeMap.getSingleton().getMimeTypeFromExtension(extension);\r\n}\r\n\r\nexport function getResourcePath(path : string) : string | null {\r\n\tlet uri = path\r\n\tif (uri.startsWith(\"http\") || uri.startsWith(\"<svg\") || uri.startsWith(\"data:image/\")) {\r\n\t\treturn uri\r\n\t}\r\n\tif (uri.startsWith(\"file://\")) {\r\n\t\turi = uri.substring(\"file://\".length)\r\n\t} else if (uri.startsWith(\"unifile://\")) {\r\n\t\turi = UTSAndroid.convert2AbsFullPath(uri)\r\n\t} else {\r\n\t\turi = UTSAndroid.convert2AbsFullPath(uri)\r\n\t\tif (uri.startsWith(\"/android_asset/\")) {\r\n\t\t\ttry {\r\n\t\t\t\tconst context = UTSAndroid.getUniActivity()!;\r\n\t\t\t\tconst inputStream = context.getResources()!.getAssets().open(path.replace('/android_asset/', ''))\r\n\t\t\t\tinputStream.close();\r\n\t\t\t\treturn uri\r\n\t\t\t} catch (e) {\r\n\t\t\t\treturn null\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\tconst file = new File(uri)\r\n\tif (file.exists()) {\r\n\t\treturn uri\r\n\t}\r\n\treturn null\r\n}\r\n\r\n\r\n/**\r\n * 检查路径存在性及类型 (Android 实现)\r\n * @param path 要检查的完整路径（支持内部存储和外部存储路径）\r\n * @return Pair<是否存在, 是否是目录>\r\n */\r\nexport function checkExistence(filePath : string):boolean[] {\r\n\tconst path = getResourcePath(filePath)\r\n\tif(path == null) return [false, false]\r\n\tconst file = new File(path)\r\n\tconst exists = file.exists()\r\n\t\r\n\tif(exists) {\r\n\t\treturn [true, file.isDirectory]\r\n\t} else {\r\n\t\treturn [false, false]\r\n\t}\r\n}\r\n\r\n/**\r\n * 检查路径是否存在\r\n * @param path 要检查的完整路径\r\n */\r\nexport function isExists(filePath : string):boolean {\r\n\tconst result = checkExistence(filePath);\r\n\treturn result[0]\r\n}\r\n\r\n/**\r\n * 检查路径是否是存在的目录\r\n * @param path 要检查的完整路径\r\n */\r\nexport function isDirectory(filePath : string):boolean {\r\n\tconst result = checkExistence(filePath);\r\n\treturn result[0] && result[1]\r\n}\r\n\r\n/**\r\n * 检查指定路径是否为存在的文件\r\n * @param path 要检查的完整路径\r\n * @return 当且仅当路径存在且是普通文件时返回 true\r\n */\r\nexport function isFile(filePath : string):boolean {\r\n\tconst result = checkExistence(filePath);\r\n\treturn result[0] && !result[1]\r\n}\r\n\r\n\r\n\r\nexport function fileToBase64(filePath : string) : NullableString {\r\n\ttry {\r\n\t\tconst context = UTSAndroid.getUniActivity()!;\r\n\t\tlet path = filePath;\r\n\t\tlet imageBytes : NullByteArray = null\r\n\r\n\t\tif (path.startsWith(\"file://\")) {\r\n\t\t\tpath = path.replace(\"file://\", \"\")\r\n\t\t} else {\r\n\t\t\t// if(!path.startsWith(\"/storage\") && !path.startsWith(\"/android_asset/\"))\r\n\t\t\t// path = UTSAndroid.getResourcePath(path)\r\n\t\t\tpath = UTSAndroid.convert2AbsFullPath(path)\r\n\t\t}\r\n\r\n\t\tif (path.startsWith(\"/android_asset/\")) {\r\n\t\t\timageBytes = inputStreamToArray(context.getResources()!.getAssets().open(path.replace('/android_asset/', '')))\r\n\t\t} else {\r\n\t\t\tconst file = new File(path)\r\n\t\t\tif (file.exists()) {\r\n\t\t\t\tlet fis : FileInputStream = new FileInputStream(file);\r\n\t\t\t\timageBytes = inputStreamToArray(fis);\r\n\t\t\t\tfis.close();\r\n\t\t\t}\r\n\t\t}\r\n\t\tif (imageBytes == null) return null\r\n\t\treturn Base64.encodeToString(imageBytes, Base64.DEFAULT)\r\n\t} catch (e) {\r\n\t\treturn null\r\n\t}\r\n}\r\nexport function fileToDataURL(filePath : string) : NullableString {\r\n\tconst base64 = fileToBase64(filePath)\r\n\tconst mimeType = getMimeType(filePath);\r\n\tif (base64 == null || mimeType == null) return null;\r\n\treturn \"data:\" + mimeType + \";base64,\" + base64;\r\n}\r\n\r\n\r\nfunction getFileExtensionFromDataURL(dataURL : string) : string {\r\n\tconst commaIndex = dataURL.indexOf(\",\");\r\n\tconst mimeType = dataURL.substring(0, commaIndex).replace(\"data:\", \"\").replace(\";base64\", \"\");\r\n\tconst mimeTypeParts = mimeType.split(\"/\");\r\n\treturn mimeTypeParts[1];\r\n}\r\nfunction dataURLToBytes(dataURL : string) : ByteArray {\r\n\tconst commaIndex = dataURL.indexOf(\",\");\r\n\tconst base64 = dataURL.substring(commaIndex + 1);\r\n\treturn Base64.decode(base64, Base64.DEFAULT);\r\n}\r\n\r\nexport function dataURLToFile(dataURL : string, filename : NullableString = null) : NullableString {\r\n\ttry {\r\n\t\tconst bytes = dataURLToBytes(dataURL);\r\n\t\tconst name = filename ?? `${Date.now()}.${getFileExtensionFromDataURL(dataURL)}`;\r\n\t\tconst cacheDir = UTSAndroid.getAppCachePath()!;\r\n\t\tconst destFile = new File(cacheDir, name);\r\n\t\tconst path = new File(cacheDir); \r\n\t\tif(!path.exists()){\r\n\t\t\tpath.mkdir(); \r\n\t\t}\r\n\t\tconst fos = new FileOutputStream(destFile)\r\n\t\tfos.write(bytes);\r\n\t\tfos.close();\r\n\t\treturn `${cacheDir}${name}`\r\n\t} catch (e) {\r\n\t\t__f__('error','at uni_modules/lime-file-utils/utssdk/app-android/index.uts:183','dataURLToFile::', e)\r\n\t\treturn null\r\n\t}\r\n}\r\n\r\n\r\n// function requestSystemPermission(fun:()=> void) {\r\n// \tlet permissionNeed = [\"android.permission.WRITE_EXTERNAL_STORAGE\"]\r\n// \tUTSAndroid.requestSystemPermission(UTSAndroid.getUniActivity()!, permissionNeed, function (allRight : boolean, _ : string[]) {\r\n// \t\tif (allRight) {\r\n// \t\t\t// 权限请求成功\r\n// \t\t\t__f__('log','at uni_modules/lime-file-utils/utssdk/app-android/index.uts:194',`allRight`, allRight)\r\n// \t\t\tfun()\r\n// \t\t} else {\r\n// \t\t\t//用户拒绝了部分权限\r\n// \t\t}\r\n// \t}, function (_ : boolean, _ : string[]) {\r\n// \t\t//用户拒绝了部分权限\r\n// \t})\r\n// }\r\n\r\n\r\nexport function processFile(options : ProcessFileOptions) {\r\n\r\n\tif (options.type == 'toBase64') {\r\n\t\tconst res = fileToBase64(options.path)\r\n\t\tconst err = 'fileToBase64: 解析失败'\r\n\t\tif (res != null) {\r\n\t\t\toptions.success?.(res)\r\n\t\t\toptions.complete?.(res)\r\n\t\t} else {\r\n\t\t\toptions.complete?.(err)\r\n\t\t\toptions.fail?.(err)\r\n\t\t}\r\n\t} else if (options.type == 'toDataURL') {\r\n\t\tconst res = fileToDataURL(options.path)\r\n\t\tconst err = 'fileToDataURL: 解析失败'\r\n\t\tif (res != null) {\r\n\t\t\toptions.success?.(res)\r\n\t\t\toptions.complete?.(res)\r\n\t\t} else {\r\n\t\t\toptions.complete?.(err)\r\n\t\t\toptions.fail?.(err)\r\n\t\t}\r\n\t} else if (options.type == 'toFile') {\r\n\t\tconst res = dataURLToFile(options.path, options.filename)\r\n\t\tconst err = 'dataURLToFile: 解析失败'\r\n\t\tif (res != null) {\r\n\t\t\toptions.success?.(res)\r\n\t\t\toptions.complete?.(res)\r\n\t\t} else {\r\n\t\t\toptions.complete?.(err)\r\n\t\t\toptions.fail?.(err)\r\n\t\t}\r\n\t}\r\n}", "// @ts-nocheck\r\nexport type NullableString = string | null\r\nexport type ConversionType = 'toBase64' | 'toDataURL' | 'toFile'\r\nexport type ProcessFileOptions = {\r\n  type : ConversionType\r\n  path: string\r\n  filename?: string\r\n  success ?: (res : string) => void\r\n  fail ?: (res : any) => void\r\n  complete ?: (res : any) => void\r\n}\r\n\r\n\r\n\r\n/**\r\n * 错误码\r\n * 根据uni错误码规范要求，建议错误码以90开头，以下是错误码示例：\r\n * - 9010001 错误信息1\r\n * - 9010002 错误信息2\r\n */\r\nexport type ProcessFileErrorCode = 9010001 | 9010002;\r\n/**\r\n * myApi 的错误回调参数\r\n */\r\nexport interface ProcessFileFail extends IUniError {\r\n  errCode : ProcessFileErrorCode\r\n};\r\n"], "names": [], "mappings": ";;AAAA,OAAmB,mBAAqB,AAAC;AACzC,OAAwB,0BAA4B,AAAC;;;;;;;;;;;;AACrD,OAAkC,6BAA+B,AAAC;AAElE,OAAiB,YAAc,AAAC;AAChC,OAA4B,uBAAyB,AAAC;AACtD,OAA6B,wBAA0B,AAAC;AACxD,OAAwB,mBAAqB,AAAC;;;;;UCNlC,iBAAiB,MAAM;UACvB,iBAAiB,MAAU;AACN,WAArB;IACV;mBAAO,eAAc;IACrB;mBAAM,MAAM,CAAA;IACZ,mBAAW,MAAM,SAAA;IACjB,oBAAY,KAAM,MAAM,KAAK,IAAI,UAAA;IACjC,iBAAS,KAAM,GAAG,KAAK,IAAI,UAAA;IAC3B,qBAAa,KAAM,GAAG,KAAK,IAAI,UAAA;;;;;;UDE5B,gBAAgB;AAErB,IAAS,mBAAmB,aAAc,WAAW,GAAI,cAAa;IACrE,IAAI;QACH,IAAI,KAAM,wBAAwB,AAAI;QACtC,IAAI,OAAQ,YAAY,AAAI,UAAU,IAAI;QAE1C,GAAG;YACF,IAAI,SAAS,YAAY,IAAI,CAAC;YAC9B,IAAI,UAAU,CAAC,CAAC,EAAE;gBACjB,IAAI,KAAK,CAAC,OAAO,CAAC,EAAE;mBACd;gBACN,KAAK;;;eAEE,IAAI,CAAC;QACd,IAAI,KAAK;QACT,OAAO,IAAI,WAAW;;KACrB,OAAO,GAAI,WAAW;QACvB,OAAO,IAAI;;AAEb;AAEA,IAAS,YAAY,UAAW,MAAM,kBAAkB;IACvD,IAAM,YAAY,YAAY,uBAAuB,CAAC;IACtD,IAAI,aAAa,IAAI;QAAE,OAAO,IAAI;;IAClC,OAAO,YAAY,YAAY,GAAG,wBAAwB,CAAC;AAC5D;AAEM,IAAU,gBAAgB,MAAO,MAAM,GAAI,MAAM,EAAO;IAC7D,IAAI,MAAM;IACV,IAAI,IAAI,UAAU,CAAC,WAAW,IAAI,UAAU,CAAC,WAAW,IAAI,UAAU,CAAC,gBAAgB;QACtF,OAAO;;IAER,IAAI,IAAI,UAAU,CAAC,YAAY;QAC9B,MAAM,IAAI,SAAS,CAAC,CAAS;WACvB,IAAI,IAAI,UAAU,CAAC,eAAe;QACxC,MAAM,WAAW,mBAAmB,CAAC;WAC/B;QACN,MAAM,WAAW,mBAAmB,CAAC;QACrC,IAAI,IAAI,UAAU,CAAC,oBAAoB;YACtC,IAAI;gBACH,IAAM,UAAU,WAAW,cAAc;gBACzC,IAAM,cAAc,QAAQ,YAAY,KAAI,SAAS,GAAG,IAAI,CAAC,KAAK,OAAO,CAAC,mBAAmB;gBAC7F,YAAY,KAAK;gBACjB,OAAO;;aACN,OAAO,cAAG;gBACX,OAAO,IAAI;;;;IAId,IAAM,OAAO,AAAI,KAAK;IACtB,IAAI,KAAK,MAAM,IAAI;QAClB,OAAO;;IAER,OAAO,IAAI;AACZ;AAQM,IAAU,eAAe,UAAW,MAAM,YAAE,OAAO,EAAE;IAC1D,IAAM,OAAO,gBAAgB;IAC7B,IAAG,QAAQ,IAAI;QAAE,OAAO;YAAC,KAAK;YAAE,KAAK;SAAC;;IACtC,IAAM,OAAO,AAAI,KAAK;IACtB,IAAM,SAAS,KAAK,MAAM;IAE1B,IAAG,QAAQ;QACV,OAAO;YAAC,IAAI;YAAE,KAAK,WAAW;SAAC;WACzB;QACN,OAAO;YAAC,KAAK;YAAE,KAAK;SAAC;;AAEvB;AAMM,IAAU,SAAS,UAAW,MAAM,GAAE,OAAO,CAAA;IAClD,IAAM,SAAS,eAAe;IAC9B,OAAO,MAAM,CAAC,CAAC,CAAC;AACjB;AAMM,IAAU,YAAY,UAAW,MAAM,GAAE,OAAO,CAAA;IACrD,IAAM,SAAS,eAAe;IAC9B,OAAO,MAAM,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC;AAC9B;AAOM,IAAU,OAAO,UAAW,MAAM,GAAE,OAAO,CAAA;IAChD,IAAM,SAAS,eAAe;IAC9B,OAAO,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;AAC/B;AAIM,IAAU,aAAa,UAAW,MAAM,kBAAkB;IAC/D,IAAI;QACH,IAAM,UAAU,WAAW,cAAc;QACzC,IAAI,OAAO;QACX,IAAI,YAAa,gBAAgB,IAAI;QAErC,IAAI,KAAK,UAAU,CAAC,YAAY;YAC/B,OAAO,KAAK,OAAO,CAAC,WAAW;eACzB;YAGN,OAAO,WAAW,mBAAmB,CAAC;;QAGvC,IAAI,KAAK,UAAU,CAAC,oBAAoB;YACvC,aAAa,mBAAmB,QAAQ,YAAY,KAAI,SAAS,GAAG,IAAI,CAAC,KAAK,OAAO,CAAC,mBAAmB;eACnG;YACN,IAAM,OAAO,AAAI,KAAK;YACtB,IAAI,KAAK,MAAM,IAAI;gBAClB,IAAI,KAAM,kBAAkB,AAAI,gBAAgB;gBAChD,aAAa,mBAAmB;gBAChC,IAAI,KAAK;;;QAGX,IAAI,cAAc,IAAI;YAAE,OAAO,IAAI;;QACnC,OAAO,OAAO,cAAc,CAAC,YAAY,OAAO,OAAO;;KACtD,OAAO,cAAG;QACX,OAAO,IAAI;;AAEb;AACM,IAAU,cAAc,UAAW,MAAM,kBAAkB;IAChE,IAAM,SAAS,aAAa;IAC5B,IAAM,WAAW,YAAY;IAC7B,IAAI,UAAU,IAAI,IAAI,YAAY,IAAI;QAAE,OAAO,IAAI;;IACnD,OAAO,UAAU,WAAW,aAAa;AAC1C;AAGA,IAAS,4BAA4B,SAAU,MAAM,GAAI,MAAM,CAAA;IAC9D,IAAM,aAAa,QAAQ,OAAO,CAAC;IACnC,IAAM,WAAW,QAAQ,SAAS,CAAC,CAAC,EAAE,YAAY,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,WAAW;IAC1F,IAAM,gBAAgB,SAAS,KAAK,CAAC;IACrC,OAAO,aAAa,CAAC,CAAC,CAAC;AACxB;AACA,IAAS,eAAe,SAAU,MAAM,GAAI,UAAS;IACpD,IAAM,aAAa,QAAQ,OAAO,CAAC;IACnC,IAAM,SAAS,QAAQ,SAAS,CAAC,aAAa,CAAC;IAC/C,OAAO,OAAO,MAAM,CAAC,QAAQ,OAAO,OAAO;AAC5C;AAEM,IAAU,cAAc,SAAU,MAAM,EAAE,2BAA4B,IAAI,kBAAkB;IACjG,IAAI;QACH,IAAM,QAAQ,eAAe;QAC7B,IAAM,OAAO,YAAY,KAAG,KAAK,GAAG,KAAE,MAAI,4BAA4B;QACtE,IAAM,WAAW,WAAW,eAAe;QAC3C,IAAM,WAAW,AAAI,KAAK,UAAU;QACpC,IAAM,OAAO,AAAI,KAAK;QACtB,IAAG,CAAC,KAAK,MAAM,IAAG;YACjB,KAAK,KAAK;;QAEX,IAAM,MAAM,AAAI,iBAAiB;QACjC,IAAI,KAAK,CAAC;QACV,IAAI,KAAK;QACT,OAAO,KAAG,WAAW;;KACpB,OAAO,cAAG;QACX,cAAgF,mBAAmB;QACnG,OAAO,IAAI;;AAEb;AAmBM,IAAU,YAAY,2BAA4B,EAAA;IAEvD,IAAI,QAAQ,IAAI,IAAI,YAAY;QAC/B,IAAM,MAAM,aAAa,QAAQ,IAAI;QACrC,IAAM,MAAM;QACZ,IAAI,OAAO,IAAI,EAAE;YAChB,QAAQ,OAAO,SAAG;YAClB,QAAQ,QAAQ,SAAG;eACb;YACN,QAAQ,QAAQ,SAAG;YACnB,QAAQ,IAAI,SAAG;SACf;WACK,IAAI,QAAQ,IAAI,IAAI,aAAa;QACvC,IAAM,MAAM,cAAc,QAAQ,IAAI;QACtC,IAAM,MAAM;QACZ,IAAI,OAAO,IAAI,EAAE;YAChB,QAAQ,OAAO,SAAG;YAClB,QAAQ,QAAQ,SAAG;eACb;YACN,QAAQ,QAAQ,SAAG;YACnB,QAAQ,IAAI,SAAG;SACf;WACK,IAAI,QAAQ,IAAI,IAAI,UAAU;QACpC,IAAM,MAAM,cAAc,QAAQ,IAAI,EAAE,QAAQ,QAAQ;QACxD,IAAM,MAAM;QACZ,IAAI,OAAO,IAAI,EAAE;YAChB,QAAQ,OAAO,SAAG;YAClB,QAAQ,QAAQ,SAAG;eACb;YACN,QAAQ,QAAQ,SAAG;YACnB,QAAQ,IAAI,SAAG;;;AAGlB"}