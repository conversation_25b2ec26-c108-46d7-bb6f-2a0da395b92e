import { ToastOptions, ToastType } from "../types"

/**
 * Toast 管理器
 * 用于全局管理 Toast 提示
 */
class ToastManager {
  private static instance: ToastManager | null = null
  private toastQueue: ToastOptions[] = []
  private currentToast: ToastOptions | null = null
  private isShowing: boolean = false
  
  static getInstance(): ToastManager {
    if (!ToastManager.instance) {
      ToastManager.instance = new ToastManager()
    }
    return ToastManager.instance
  }
  
  /**
   * 显示 Toast
   */
  show(options: ToastOptions): void {
    const defaultOptions: ToastOptions = {
      message: '',
      type: 'default',
      showIcon: true,
      closable: false,
      duration: 3000,
      position: 'top',
      top: 0
    }
    
    const finalOptions = { ...defaultOptions, ...options }
    
    if (this.isShowing) {
      this.toastQueue.push(finalOptions)
    } else {
      this.showToast(finalOptions)
    }
  }
  
  /**
   * 显示成功提示
   */
  success(message: string, options?: Partial<ToastOptions>): void {
    this.show({
      message,
      type: 'success',
      icon: '✓',
      ...options
    })
  }
  
  /**
   * 显示错误提示
   */
  error(message: string, options?: Partial<ToastOptions>): void {
    this.show({
      message,
      type: 'error',
      icon: '✕',
      ...options
    })
  }
  
  /**
   * 显示警告提示
   */
  warning(message: string, options?: Partial<ToastOptions>): void {
    this.show({
      message,
      type: 'warning',
      icon: '⚠',
      ...options
    })
  }
  
  /**
   * 显示信息提示
   */
  info(message: string, options?: Partial<ToastOptions>): void {
    this.show({
      message,
      type: 'info',
      icon: 'ℹ',
      ...options
    })
  }
  
  /**
   * 隐藏当前 Toast
   */
  hide(): void {
    this.isShowing = false
    this.currentToast = null
    
    // 显示队列中的下一个 Toast
    if (this.toastQueue.length > 0) {
      const nextToast = this.toastQueue.shift()
      if (nextToast) {
        setTimeout(() => {
          this.showToast(nextToast)
        }, 100)
      }
    }
  }
  
  /**
   * 清空队列
   */
  clear(): void {
    this.toastQueue = []
    this.hide()
  }
  
  private showToast(options: ToastOptions): void {
    this.isShowing = true
    this.currentToast = options
    
    // 这里需要与具体的 Toast 组件实例进行通信
    // 在实际使用中，可能需要通过事件总线或其他方式来实现
    uni.$emit('showToast', options)
  }
}

// 导出单例实例
export const toast = ToastManager.getInstance()

// 导出便捷方法
export function showToast(options: ToastOptions): void {
  toast.show(options)
}

export function showSuccess(message: string, options?: Partial<ToastOptions>): void {
  toast.success(message, options)
}

export function showError(message: string, options?: Partial<ToastOptions>): void {
  toast.error(message, options)
}

export function showWarning(message: string, options?: Partial<ToastOptions>): void {
  toast.warning(message, options)
}

export function showInfo(message: string, options?: Partial<ToastOptions>): void {
  toast.info(message, options)
}

export function hideToast(): void {
  toast.hide()
}

export function clearToast(): void {
  toast.clear()
}
