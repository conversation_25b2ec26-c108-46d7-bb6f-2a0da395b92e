{"id": "lime-svg", "displayName": "lime-svg", "version": "0.1.9", "description": "lime-svg 是一款UTS原生图标插件,支持修改单色svg的颜色，支持本地、base64、网络等路径。支持uniapp/uniappx", "keywords": ["lime-svg", "svg", "uvue", "vue"], "repository": "", "engines": {"HBuilderX": "^4.17"}, "dcloudext": {"type": "uts-vue-component", "sale": {"regular": {"price": "5.99"}, "sourcecode": {"price": "6.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "无", "permissions": "无"}, "npmurl": ""}, "uni_modules": {"dependencies": ["lime-file-utils"], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y", "alipay": "n"}, "client": {"Vue": {"vue2": "y", "vue3": "y"}, "App": {"app-android": "y", "app-ios": "y", "app-harmony": "y"}, "H5-mobile": {"Safari": "y", "Android Browser": "y", "微信浏览器(Android)": "y", "QQ浏览器(Android)": "y"}, "H5-pc": {"Chrome": "y", "IE": "u", "Edge": "u", "Firefox": "u", "Safari": "u"}, "小程序": {"微信": "u", "阿里": "u", "百度": "u", "字节跳动": "u", "QQ": "u", "钉钉": "u", "快手": "u", "飞书": "u", "京东": "u"}, "快应用": {"华为": "u", "联盟": "u"}}}}}