{"version": 3, "sources": ["D:/HBuilderX/plugins/uniapp-cli-vite/node_modules/@dcloudio/uni-console/src/runtime/app/socket.ts", "App.uvue", "uni_modules/lime-svg/components/l-svg/utils.uts", "uni_modules/lime-svg/components/l-svg/utils.ts", "components/firstui/fui-status-bar/fui-status-bar.uvue", "D:/HBuilderX/plugins/uniapp-cli-vite/node_modules/@dcloudio/uni-console/src/runtime/app/index.ts", "components/firstui/fui-types/index.uts", "components/uc/types/index.uts", "uni_modules/lime-svg/components/l-svg/type.uts", "uni_modules/lime-svg/components/l-svg/type.ts", "uni_modules/lime-file-utils/utssdk/interface.uts", "components/uc/utils/index.uts", "pages/login/types.uts", "pages/login/login.uvue?type=page", "pages/login/login.uvue", "main.uts"], "sourcesContent": ["/// <reference types=\"@dcloudio/uni-app-x/types/uni/global\" />\n// 之所以又写了一份，是因为外层的socket，connectSocket的时候必须传入multiple:true\n// 但是android又不能传入，目前代码里又不能写条件编译之类的。\nexport function initRuntimeSocket(\n  hosts: string,\n  port: string,\n  id: string\n): Promise<SocketTask | null> {\n  if (hosts == '' || port == '' || id == '') return Promise.resolve(null)\n  return hosts\n    .split(',')\n    .reduce<Promise<SocketTask | null>>(\n      (\n        promise: Promise<SocketTask | null>,\n        host: string\n      ): Promise<SocketTask | null> => {\n        return promise.then((socket): Promise<SocketTask | null> => {\n          if (socket != null) return Promise.resolve(socket)\n          return tryConnectSocket(host, port, id)\n        })\n      },\n      Promise.resolve(null)\n    )\n}\n\nconst SOCKET_TIMEOUT = 500\nfunction tryConnectSocket(\n  host: string,\n  port: string,\n  id: string\n): Promise<SocketTask | null> {\n  return new Promise((resolve, reject) => {\n    const socket = uni.connectSocket({\n      url: `ws://${host}:${port}/${id}`,\n      fail() {\n        resolve(null)\n      },\n    })\n    const timer = setTimeout(() => {\n      // @ts-expect-error\n      socket.close({\n        code: 1006,\n        reason: 'connect timeout',\n      } as CloseSocketOptions)\n      resolve(null)\n    }, SOCKET_TIMEOUT)\n\n    socket.onOpen((e) => {\n      clearTimeout(timer)\n      resolve(socket)\n    })\n    socket.onClose((e) => {\n      clearTimeout(timer)\n      resolve(null)\n    })\n    socket.onError((e) => {\n      clearTimeout(timer)\n      resolve(null)\n    })\n  })\n}\n", "<script lang=\"uts\">\n  let firstBackTime = 0\n  export default {\n\n\n\n\n\n\n\n\n\n\n    onLaunch: function (options) {\n      console.log('App Launch')\n    },\n    onShow: function (options) {\n      console.log('App Show')\n    },\n    onHide: function () {\n      console.log('App Hide')\n    },\n\n    onLastPageBackPress: function () {\n      console.log('App LastPageBackPress')\n      if (firstBackTime == 0) {\n        uni.showToast({\n          title: '再按一次退出应用',\n          position: 'bottom',\n        })\n        firstBackTime = Date.now()\n        setTimeout(() => {\n          firstBackTime = 0\n        }, 2000)\n      } else if (Date.now() - firstBackTime < 2000) {\n        firstBackTime = Date.now()\n        uni.exit()\n      }\n    },\n    onExit() {\n      console.log('App Exit')\n    },\n\n    onError: function(err: any) {\n      console.log('App Error', err)\n    },\n    methods: {\n\n\n\n\n\n\n\n\n\n\n\n    }\n  }\n</script>\n\n<style>\n/* @import './styles/common.scss';\n@import './static/css/iconfont.css'; */\n</style>\n", "// @ts-nocheck\nimport { fileToDataURL } from '@/uni_modules/lime-file-utils';\n/**\n * 小程序把路径转成base64\n * @param {string} path\n * @return 表示 SVG 的 Data URL。\n */\nexport function pathToDataUrl(path: string): Promise<string> {\n    return new Promise((resolve, reject) => {\n        const url = fileToDataURL(path);\n        if (url == null) {\n            reject('路径错误');\n        }\n        resolve(url!.replace(/\\s+/g, ''));\n    });\n}\n/**\n * 将 SVG 字符串转换为 Data URL。\n * @param {string} svg - 要转换的 SVG 字符串。\n * @returns {string} 表示 SVG 的 Data URL。\n */\nexport function svgToDataUrl(svgString: string): string {\n    const encodedSvg = encodeURIComponent(svgString)!.replace(/\\+/g, '%20');\n    return `data:image/svg+xml,${encodedSvg}`;\n}\n//# sourceMappingURL=utils.uts.map", "// @ts-nocheck\r\n\r\nimport { fileToDataURL } from '@/uni_modules/lime-file-utils'\r\n\r\n\r\n\r\n/**\r\n * 小程序把路径转成base64\r\n * @param {string} path \r\n * @return 表示 SVG 的 Data URL。\r\n */\r\nexport function pathToDataUrl(path : string) : Promise<string> {\r\n\t\r\n\treturn new Promise((resolve, reject) => {\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\t\tconst url = fileToDataURL(path)\r\n\t\tif(url == null) {\r\n\t\t\treject('路径错误')\r\n\t\t}\r\n\t\tresolve(url!.replace(/\\s+/g,''))\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\t})\r\n\r\n}\r\n\r\n/**\r\n * 将 SVG 字符串转换为 Data URL。\r\n * @param {string} svg - 要转换的 SVG 字符串。\r\n * @returns {string} 表示 SVG 的 Data URL。\r\n */\r\nexport function svgToDataUrl(svgString : string) : string {\r\n\tconst encodedSvg = encodeURIComponent(svgString)!.replace(/\\+/g, '%20');\r\n\treturn `data:image/svg+xml,${encodedSvg}`\r\n}", "<template>\r\n\t<view :style=\"{ height: statusBarHeight,zIndex:isFixed?zIndex:1,background:background }\" class=\"fui-status__bar\"\r\n\t\t:class=\"{'fui-status__bar-fixed':isFixed}\">\r\n\t\t<slot />\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tvar statusBarHeight = `${uni.getSystemInfoSync().statusBarHeight}px`\r\n\t/**\r\n\t * StatusBar 状态栏\r\n\t * @description StatusBar 状态栏，主要用于自定义导航栏时状态栏占位，高度与状态栏相等。\r\n\t * @tutorial https://unix.firstui.cn/\r\n\t * @property {String} background {String} 状态栏背景色\r\n\t * @property {Boolean} isFixed {Boolean}是否固定在顶部\r\n\t * @property {Number} zIndex {Number} z-index值，isFixed为true时生效\r\n\t * @event {Function} init 初始化时触发,返回状态栏高度，(event: string) => void\r\n\t */\r\n\texport default {\r\n\t\tname: \"fui-status-bar\",\r\n\t\temits: ['init'],\r\n\t\tprops: {\r\n\t\t\tbackground: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'transparent'\r\n\t\t\t},\r\n\t\t\tisFixed: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\tzIndex: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 99\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tstatusBarHeight: statusBarHeight as string\r\n\t\t\t};\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tthis.$emit('init', statusBarHeight)\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style scoped>\r\n\t.fui-status__bar {\r\n\t\twidth: 100%;\r\n\t\theight: 20px;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.fui-status__bar-fixed {\r\n\t\tposition: fixed;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\ttop: 0;\r\n\t}\r\n</style>", "import { initRuntimeSocket } from './socket'\n\nexport function initRuntimeSocketService(): Promise<boolean> {\n  const hosts: string = process.env.UNI_SOCKET_HOSTS\n  const port: string = process.env.UNI_SOCKET_PORT\n  const id: string = process.env.UNI_SOCKET_ID\n  if (hosts == '' || port == '' || id == '') return Promise.resolve(false)\n  let socketTask: SocketTask | null = null\n  __registerWebViewUniConsole(\n    (): string => {\n      return process.env.UNI_CONSOLE_WEBVIEW_EVAL_JS_CODE\n    },\n    (data: string) => {\n      socketTask?.send({\n        data,\n      } as SendSocketMessageOptions)\n    }\n  )\n  return Promise.resolve()\n    .then((): Promise<boolean> => {\n      return initRuntimeSocket(hosts, port, id).then((socket): boolean => {\n        if (socket == null) {\n          return false\n        }\n        socketTask = socket\n        return true\n      })\n    })\n    .catch((): boolean => {\n      return false\n    })\n}\n\ninitRuntimeSocketService()\n", "/*!\r\n* type：组件内props属性参数、事件回调参数、方法参数类型\r\n* fui-types - v1.0.0 (2023/11/11, 11:07:14 AM)\r\n*\r\n* 注意：当页面使用时，Object、Array等类型使用any接收，暂时需要转化为UTSJSONObject使用，不可直接使用“.”访问属性\r\n* @example：const param  = JSON.parse(JSON.stringify(e)) as UTSJSONObject\r\n* 在 uts 中，只适合转 type，不适合使用 interface。[interface 中使用? 编译报错，需要使用 type]\r\n*\r\n* 官网地址：https://firstui.cn/\r\n* 文档地址：https://unix.firstui.cn/\r\n*/\r\n\r\n/**\r\n* fui-text 文本组件 @onclick 事件 回调参数类型\r\n* @description this.$emit('onclick',FuiTextClickParam)\r\n* @param {string} text {string} text文本\r\n* @param {string} param {string} 自定义参数 \r\n*/\r\nexport type FuiTextClickParam = {\r\n\ttext : string;\r\n\tparam : string;\r\n}\r\n\r\n/**\r\n* fui-number 数字组件 @onclick 事件 回调参数类型\r\n* @description this.$emit('onclick',FuiNumberClickParam)\r\n* @param {string} text {string} 数字文本内容\r\n* @param {string} param {string} 自定义参数 \r\n*/\r\nexport type FuiNumberClickParam = {\r\n\ttext : string;\r\n\tparam : string;\r\n};\r\n\r\n\r\n/**\r\n* fui-footer 页脚组件 navigate 属性参数类型\r\n* @description props navigate：Arrary：FuiFooterNavigateParam[]\r\n* @param {string} text {string} 链接文本\r\n* @param {string} url {string} 当前应用内的跳转链接，可选\r\n* @param {string} openType {string} 跳转方式，可选值：navigate、redirect、switchTab、reLaunch、navigateBack等，可选\r\n* @param {number} delta {number} 返回的页面数，如果 delta 大于现有页面数，则返回到首页【仅openType=navigateBack 有效】，可选\r\n* @param {string} color {string} 链接文本字体颜色，可选\r\n* @param {number} size {number} 链接文本字体大小，单位rpx，可选\r\n*/\r\nexport type FuiFooterNavigateParam = {\r\n\ttext : string;\r\n\turl ?: string;\r\n\topenType ?: string;\r\n\tdelta ?: number;\r\n\tcolor ?: string;\r\n\tsize ?: number;\r\n}\r\n\r\n/**\r\n* fui-input-number 数字输入框组件 @change 事件 回调参数类型\r\n* @description this.$emit('change',FuiInputNumberChangeParam)\r\n* @param {number} value {number} 数字文本内容\r\n* @param {number} index {number} 索引值\r\n* @param {string} param {string} 自定义参数 \r\n*/\r\nexport type FuiInputNumberChangeParam = {\r\n\tvalue : number;\r\n\tindex : number;\r\n\tparam : string;\r\n}\r\n\r\n/**\r\n* fui-checkbox 复选框组件 @change 事件 回调参数类型（单独使用fui-checkbox组件）\r\n* @description this.$emit('change',FuiCheckboxChangeParam)\r\n* @param {boolean} checked {boolean} 是否选中\r\n* @param {string} value {string} checkbox 标识，值\r\n*/\r\nexport type FuiCheckboxChangeParam = {\r\n\tchecked : boolean;\r\n\tvalue : string;\r\n}\r\n\r\n\r\n/**\r\n* fui-actionsheet 上拉菜单组件 itemList 属性 参数类型\r\n* @description props itemList：Arrary：FuiActionSheetItemParam[]\r\n* @param {string} text {string} 菜单按钮文本\r\n* @param {string} color {string} 菜单按钮文本颜色（主题（theme）为 light 下使用），可选\r\n* @param {string} darkColor {string} 菜单按钮文本颜色（主题（theme）为 dark 下使用），可选\r\n* @param {string} param {string} 自定义参数，可选\r\n* @param {number} index {number} 按钮索引值，点击按钮时内部返回，无需传值\r\n*/\r\nexport type FuiActionSheetItemParam = {\r\n\ttext : string;\r\n\tcolor ?: string;\r\n\tdarkColor ?: string;\r\n\tparam ?: string;\r\n\tindex ?: number;\r\n}\r\n\r\n\r\n/**\r\n* fui-toast 轻提示 组件show方法 参数类型\r\n* @description Toast 轻提示组件show方法参数类型\r\n* @param {number} duration {number} 显示持续时间，单位ms，可选\r\n* @param {string} src {string} 提示图标，可选\r\n* @param {string} text {string} 提示信息，使用插槽自定义内容时可不传\r\n*/\r\nexport type FuiToastShowParam = {\r\n\tduration ?: number;\r\n\tsrc ?: string;\r\n\ttext ?: string\r\n}\r\n\r\n\r\n/**\r\n* fui-dialog 对话框 组件 buttons属性 参数类型\r\n* @description Dialog 对话框组件 buttons属性 参数类型\r\n* @param {string} text {string} 按钮文本\r\n* @param {string} color {string} 按钮字体颜色，可选\r\n* @param {boolean} primary {boolean} 按钮字体颜色是否显示为primary主题色，color为空时有效，可选\r\n* @param {string} param {string} 自定义参数，可选\r\n* @param {number} index {number} 按钮索引值，点击按钮时内部返回，无需传值\r\n*/\r\nexport type FuiDialogButtonsParam = {\r\n\ttext : string;\r\n\tcolor ?: string;\r\n\tprimary ?: boolean;\r\n\tparam ?: string;\r\n\tindex ?: number;\r\n}\r\n\r\n/**\r\n* fui-dropdown-menu 下拉菜单 组件 menus属性 参数类型\r\n* @description DropdownMenu 下拉菜单组件 menus属性 参数类型\r\n* @param {string} text {string} 下拉菜单item项显示文本，必选\r\n* @param {string} value {string} 下拉菜单item项文本对应value值，可选\r\n* @param {string} src {string} 下拉菜单item项icon图片地址，可选\r\n* @param {boolean} checked {boolean} 是否选中，可选\r\n* @param {boolean} disabled {boolean} 是否禁用选择，可选\r\n* @param {string} param {string} 自定义参数，可选\r\n* @param {number} index {number} 索引值，点击菜单时内部返回，无需传值\r\n*/\r\nexport type FuiDropdownMenuOptionParam = {\r\n\ttext : string;\r\n\tvalue ?: string;\r\n\tsrc ?: string;\r\n\tchecked ?: boolean;\r\n\tdisabled ?: boolean;\r\n\tparam ?: string;\r\n\tindex ?: number;\r\n}\r\n\r\n/**\r\n* SwipeAction 滑动菜单 组件 buttons属性 参数类型\r\n* @description SwipeAction 滑动菜单 组件 buttons属性 参数类型\r\n* @param {string} text {string} 按钮文本，必选\r\n* @param {string} background {string} 按钮背景色，不传或为空则默认使用danger主题色，可选\r\n* @param {number} size {number} 按钮字体大小，单位rpx，可选\r\n* @param {string} color {string} 按钮字体颜色，可选\r\n* @param {number} param {number} 自定义参数，可选\r\n* @param {number} index {number} 当前点击的按钮索引，点击事件返回，无需传值\r\n*/\r\nexport type FuiSwipeActionButtonParam = {\r\n\ttext : string;\r\n\tbackground ?: string;\r\n\tsize ?: number;\r\n\tcolor ?: string;\r\n\tparam ?: number;\r\n\tindex ?: number;\r\n}\r\n\r\n/**\r\n* fui-pagination 分页器 组件 @change 事件 回调参数类型\r\n* @description Pagination 分页器组件 change 事件 回调参数类型\r\n* @param {string} type {string} 按钮类型（prev、next、pageNumber）\r\n* @param {number} current {number} 当前页码\r\n*/\r\nexport type FuiPaginationChangeParam = {\r\n\ttype : string;\r\n\tcurrent : number;\r\n}\r\n\r\n/**\r\n* fui-segmented-control 分段器 组件props 属性 values 参数类型\r\n* @description props 属性 values 参数类型\r\n* @param {string} name {string} 分段器显示文本，必选\r\n* @param {boolean} disabled {boolean} 是否禁用当前项，可选\r\n* @param {number} value {number} 对应的值，可选\r\n* @param {string} param {string} 自定义参数，可选\r\n* @param {number} index {number} 当前点击项索引值，点击事件返回，无需传值，可选\r\n*/\r\nexport type FuiSegmentedControlValueParam = {\r\n\tname : string;\r\n\tdisabled ?: boolean;\r\n\tvalue ?: number;\r\n\tparam ?: string;\r\n\tindex ?: number;\r\n}\r\n\r\n/**\r\n* fui-tabs 标签页 组件props 属性 tabs 参数类型\r\n* @description props 属性 tabs（item项） 参数类型\r\n* @param {string} name {string} 标签显示文本，必选\r\n* @param {string} src {string} 标签显示图标，图标尺寸默认为 40*40，单位rpx，可选\r\n* @param {string} selectedIcon {string} 当前选项图标src，可选，不传默认使用icon\r\n* @param {number} badge {number} 角标数值，可选\r\n* @param {boolean} isDot {boolean} 角标是否显示为圆点，可选\r\n* @param {boolean} disabled {boolean} 是否禁用当前项，可选\r\n* @param {number} index {number} 当前点击项索引值，change 事件返回，无需传值，可选\r\n*/\r\nexport type FuiTabsItemParam = {\r\n\tname : string;\r\n\ticon ?: string;\r\n\tselectedIcon ?: string;\r\n\tbadge ?: number;\r\n\tisDot ?: boolean;\r\n\tdisabled ?: boolean;\r\n\tindex ?: number;\r\n}\r\n\r\n/**\r\n*  Collapse 折叠面板组件 @change 事件 回调参数类型\r\n* @description Collapse 折叠面板组件 change 事件 回调参数类型\r\n* @param {number} index {number} item项索引\r\n* @param {boolean} isOpen {boolean} 是否展开\r\n*/\r\nexport type FuiCollapseChangeParam = {\r\n\tindex : number;\r\n\tisOpen : boolean;\r\n}\r\n\r\n\r\n/**\r\n * 【fui-validator 和 fui-form 组件】 FuiFormRulesValidatorParam 表单校验规则自定义方法参数类型\r\n * @description 表单校验自定义方法参数类型\r\n * @param {String} msg {String} 错误提示内容\r\n * @param {Function} method (value : any) => boolean 校验方法\r\n */\r\nexport type FuiFormRulesValidatorParam = {\r\n\tmsg : string;\r\n\tmethod : (value : any | null) => boolean;\r\n}\r\n\r\n/**\r\n * 【fui-validator 和 fui-form 组件】FuiFormRulesParam 表单校验规则参数类型\r\n * @description 表单校验规则参数类型\r\n * @param {String} name {String} 元素的名称，与formData中key一致\r\n * @param {Array} rule {string[]} 内置的校验方法名称集合，详细见下方说明\r\n * @param {Array} msg {string[]} 对应rule规则方法的错误提示信息\r\n * @param {Array} validator {FuiFormRulesValidatorParam[]} 表单校验规则自定义方法参数类型\r\n */\r\nexport type FuiFormRulesParam = {\r\n\tname : string;\r\n\trule ?: string[];\r\n\tmsg ?: string[];\r\n\tvalidator ?: FuiFormRulesValidatorParam[]\r\n}\r\n\r\n/**\r\n * 【fui-validator 和 fui-form 组件】FuiFormErrorMsgParam 表单校验错误消息参数类型\r\n * @description 表单校验错误消息参数类型\r\n * @param {String} name {String} 元素的名称，与formData中key一致\r\n * @param {String} msg {String} 错误提示信息\r\n */\r\nexport type FuiFormErrorMsgParam = {\r\n\tname : string;\r\n\tmsg : string;\r\n}\r\n\r\n/**\r\n * 【fui-validator 和 fui-form 组件】FuiFormValidatorResParam 表单校验结果参数类型\r\n * @description 表单校验结果参数类型\r\n * @param {Boolean} isPassed {Boolean} 是否校验通过\r\n * @param {Array} errorMsg {FuiFormErrorMsgParam[]} 错误提示信息\r\n */\r\nexport type FuiFormValidatorResParam = {\r\n\tisPassed : boolean;\r\n\terrorMsg : FuiFormErrorMsgParam[];\r\n}", "export type FormItemType =\n\t| 'input'\n\t| 'textarea'\n\t| 'select'\n\t| 'radio'\n\t| 'checkbox'\n\t| 'switch'\n\t| 'date'\n\t| 'time'\n\t| 'datetime'\n\t| 'upload'\n\t| 'custom'\nexport type FormItem = {\n\tlabel: string\n\tname: string\n\ttype: FormItemType\n\tvalue?: any\n\tplaceholder?: string\n\toptions?: Array<{ label: string; value: any }>\n\trules?: Array<{ required?: boolean; message?: string; trigger?: string }>\n\tdisabled?: boolean\n\treadonly?: boolean\n\tclearable?: boolean\n\tshowWordLimit?: boolean\n\tmaxLength?: number\n\tminLength?: number\n\tstep?: number\n\tmin?: number | string\n\tmax?: number | string\n\tformat?: string\n}\nexport type FormItemData = {\n\tfield: string\n\tinstance: ComponentPublicInstance\n}\nexport type FormItemRule = {\n\t/**\n\t * 字段类型\n\t */\n\ttype?:\n\t\t| 'string'\n\t\t| 'number'\n\t\t| 'boolean'\n\t\t| 'integer'\n\t\t| 'float'\n\t\t| 'array'\n\t\t| 'object'\n\t\t| 'enum'\n\t\t| 'url'\n\t\t| 'email'\n\t\t| 'phone'\n\t\t| 'idcard'\n\t\t| null\n\t/**\n\t * 是否必填\n\t */\n\trequired?: boolean | null\n\t/**\n\t * 校验失败提示信息\n\t */\n\tmessage?: string | null\n\t/**\n\t * 正则校验规则\n\t */\n\tpattern?: RegExp | null\n\t/**\n\t * 最小长度\n\t */\n\tmin?: number | null\n\t/**\n\t * 最大长度\n\t */\n\tmax?: number | null\n\t/**\n\t * 值的长度（同时设置 min、max 和 len，以len的值为准）\n\t */\n\tlen?: number | null\n\t/**\n\t * 值的枚举值，限制值只能为此枚举数组的子项\n\t */\n\tenum?: Array<any> | null\n\t/**\n\t * 数据转换函数，校验前先执行此函数对原始数据进行处理\n\t */\n\ttransform?: ((value: any) => any) | null\n\t/**\n\t * 自定义校验函数，在默认的校验前先执行此函数。\n\t *\n\t * 返回空文本串表示校验通过；返回其他字符串表示校验失败，且返回的字符串将作为校验失败的提示信息\n\t */\n\tvalid?: ((value: any) => string) | null\n}\nexport type FormItemVerifyResult = {\n\t/**\n\t * 子项校验是否通过\n\t */\n\tvalid: boolean\n\t/**\n\t * 子项校验失败的提示信息\n\t */\n\tmessage?: string | null\n\t/**\n\t * 子项的名称\n\t */\n\tfield: string\n}\nexport type FormValidResult = {\n\t/**\n\t * 表单校验成功回调\n\t */\n\tsuccess?: (() => void) | null\n\t/**\n\t * 表单校验失败回调，会将所有校验失败的子项的错误信息作为 failResults 数组参数\n\t */\n\tfail?: ((failResults: FormItemVerifyResult[]) => void) | null\n}\nexport type FormValidResultItem = {\n\t/**\n\t * 表单校验成功回调\n\t */\n\tsuccess?: (() => void) | null\n\t/**\n\t * 表单校验失败回调，会将所有校验失败的子项的错误信息作为 failResults 数组参数\n\t */\n\tfail?: ((failResults?: FormItemVerifyResult) => void) | null\n}\n\n/**\n * Toast 提示类型\n */\nexport type ToastType = 'success' | 'error' | 'warning' | 'info' | 'default'\n\n/**\n * Toast 位置\n */\nexport type ToastPosition = 'top' | 'bottom'\n\n/**\n * Toast 配置选项\n */\nexport type ToastOptions = {\n\t/**\n\t * 消息内容\n\t */\n\tmessage: string\n\t/**\n\t * 提示类型\n\t */\n\ttype?: ToastType\n\t/**\n\t * 自定义背景色\n\t */\n\tbackgroundColor?: string\n\t/**\n\t * 自定义文字颜色\n\t */\n\ttextColor?: string\n\t/**\n\t * 图标\n\t */\n\ticon?: string\n\t/**\n\t * 图标颜色（用于 SVG 图标）\n\t */\n\ticonColor?: string\n\t/**\n\t * 是否显示图标\n\t */\n\tshowIcon?: boolean\n\t/**\n\t * 自动关闭时间（毫秒）\n\t */\n\tduration?: number\n\t/**\n\t * 位置\n\t */\n\tposition?: ToastPosition\n\t/**\n\t * 距离顶部的距离\n\t */\n\ttop?: number\n\t/**\n\t * 自定义样式\n\t */\n\tcustomStyle?: UTSJSONObject\n}\n", "export interface LSvpProps {\n    src: string;\n    color: string;\n    web: boolean;\n    inherit: boolean;\n}\n//# sourceMappingURL=type.uts.map", "export interface LSvpProps {\n\tsrc: string;\n\tcolor: string;\r\n\tweb: boolean;\r\n\tinherit: boolean;\n}", "// @ts-nocheck\r\nexport type NullableString = string | null\r\nexport type ConversionType = 'toBase64' | 'toDataURL' | 'toFile'\nexport type ProcessFileOptions = {\n  type : ConversionType\r\n  path: string\r\n  filename?: string\n  success ?: (res : string) => void\n  fail ?: (res : any) => void\n  complete ?: (res : any) => void\n}\n\n\n\n/**\n * 错误码\n * 根据uni错误码规范要求，建议错误码以90开头，以下是错误码示例：\n * - 9010001 错误信息1\n * - 9010002 错误信息2\n */\nexport type ProcessFileErrorCode = 9010001 | 9010002;\n/**\n * myApi 的错误回调参数\n */\nexport interface ProcessFileFail extends IUniError {\n  errCode : ProcessFileErrorCode\n};\n", "import { FormItemVerifyResult, FormItemRule } from '../types'\n\nexport function throttle(\n\tfun: () => void,\n\twait: number,\n\tid: string = 'default'\n): void {\n\tif (wait <= 0) {\n\t\tfun()\n\t} else if (!this.flags.has(id) || !this.flags.get(id)!) {\n\t\tthis.flags.set(id, true)\n\t\tthis.timers.set(\n\t\t\tid,\n\t\t\tsetTimeout(() => {\n\t\t\t\tthis.flags.set(id, false)\n\t\t\t\tfun()\n\t\t\t}, wait)\n\t\t)\n\t}\n}\n\n/**\n * @name 防抖\n * @description 创建一个防抖函数，指定的时间运行多次，将重新计时，并只执行最后一次调用。\n * @param {Function} fun - 需要防抖的函数。\n * @param {number} wait - 需要等待的时间（毫秒）。\n * @param {string} [id='default'] - 防抖函数的唯一标识符，用于多组件区分不同的防抖实例。\n * @returns {void}\n * @example\n * // 示例用法\n * const myDebouncedFunction = () => {\n *   __f__('log','at components/uc/utils/index.uts:32','只会在停止触发后执行一次');\n * };\n * debounce(myDebouncedFunction, 300, 'uniqueId');\n */\nexport function debounce(\n\tfun: () => void,\n\twait: number,\n\tid: string = 'default'\n): void {\n\tclearTimeout(this.timers.get(id) ?? -1)\n\tif (wait <= 0) {\n\t\tfun()\n\t} else {\n\t\tthis.timers.set(\n\t\t\tid,\n\t\t\tsetTimeout(() => {\n\t\t\t\tfun()\n\t\t\t}, wait)\n\t\t)\n\t}\n}\nexport function isNumber(value: any | null): boolean {\n\n\treturn [\n\t\t'Byte',\n\t\t'UByte',\n\t\t'Short',\n\t\t'UShort',\n\t\t'Int',\n\t\t'UInt',\n\t\t'Long',\n\t\t'ULong',\n\t\t'Float',\n\t\t'Double',\n\t\t'number',\n\t].includes(typeof value)\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n}\nexport function isURL(url: string): boolean {\n\tlet reg =\n\t\t/^((https|http|ftp|rtsp|mms|ws):\\/\\/)(([0-9a-zA-Z_!~*'().&=+$%-]+: )?[0-9a-zA-Z_!~*'().&=+$%-]+@)?(([0-9]{1,3}.){3}[0-9]{1,3}|([0-9a-zA-Z_!~*'()-]+.)*([0-9a-zA-Z][0-9a-zA-Z-]{0,61})?[0-9a-zA-Z].[a-zA-Z]{2,6})(:[0-9]{1,4})?((\\/?)|(\\/[0-9a-zA-Z_!~*'().;?:@&=+$,%#-]+)+\\/?)$/\n\treturn reg.test(url)\n}\n\nexport function isEmail(email: string): boolean {\n\tconst reg = /^\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*$/g\n\treturn reg.test(email)\n}\n\n/**\n * @name 是否为手机号\n * @description 验证输入的字符串是否为有效的手机号。\n * @param {string} phone - 输入的字符串。\n * @returns {boolean} 如果输入的字符串是一个有效的手机号，返回 true，否则返回 false。\n * @example\n * // 示例用法\n * const isValidPhone = isPhone('13800138000'); // 返回 true\n * const isNotValidPhone = isPhone('not a valid phone'); // 返回 false\n */\nexport function isPhone(phone: string): boolean {\n\tconst reg = /^[1][3,4,5,6,7,8,9][0-9]{9}$/g\n\treturn reg.test(phone)\n}\n\nexport function isIDCard(code: string): boolean {\n\tlet cities = [\n\t\t'11',\n\t\t'12',\n\t\t'13',\n\t\t'14',\n\t\t'15',\n\t\t'21',\n\t\t'22',\n\t\t'23',\n\t\t'31',\n\t\t'32',\n\t\t'33',\n\t\t'34',\n\t\t'35',\n\t\t'36',\n\t\t'37',\n\t\t'41',\n\t\t'42',\n\t\t'43',\n\t\t'44',\n\t\t'45',\n\t\t'46',\n\t\t'50',\n\t\t'51',\n\t\t'52',\n\t\t'53',\n\t\t'54',\n\t\t'61',\n\t\t'62',\n\t\t'63',\n\t\t'64',\n\t\t'65',\n\t\t'71',\n\t\t'81',\n\t\t'82',\n\t\t'91',\n\t]\n\tlet case1 = code.length != 18\n\tlet case2 =\n\t\tcode.trim() == '' ||\n\t\t!/^\\d{6}(18|19|20)?\\d{2}(0[1-9]|1[012])(0[1-9]|[12]\\d|3[01])\\d{3}(\\d|X)$/i.test(\n\t\t\tcode\n\t\t)\n\tlet case3 = !cities.includes(code.slice(0, 2))\n\tif (case1 || case2 || case3) return false\n\n\t//验证最后一位校验位\n\tif (code.length == 18) {\n\t\tlet _code = code.split('')\n\t\t//∑(ai×Wi)(mod 11)\n\t\t//加权因子\n\t\tlet factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]\n\t\t//校验位\n\t\tlet parity = ['1', '0', 'X', '9', '8,', '7', '6', '5', '4', '3', '2']\n\t\tlet sum = 0\n\t\tlet ai = 0\n\t\tlet wi = 0\n\t\tfor (let i = 0; i < 17; i++) {\n\t\t\tai = parseInt(_code[i])\n\t\t\twi = factor[i]\n\t\t\tsum += ai * wi\n\t\t}\n\n\t\tif (parity[sum % 11] != _code[17]) {\n\t\t\treturn false\n\t\t}\n\t}\n\treturn true\n}\nexport function isImage(filename: string): boolean {\n\tconst reg =\n\t\t/^(?:[^\\s]+\\/)*[^\\s]+\\.(bmp|jpg|jpeg|ico|jfif|dpg|png|tif|gif|pcx|tga|exif|fpx|svg|psd|cdr|pcd|dxf|ufo|eps|ai|raw|wmf|webp|avif|apng)$/i\n\treturn reg.test(filename)\n}\nexport function isFloat(num: number): boolean {\n\treturn !isNaN(num) && isFinite(num) && Math.trunc(num) != num\n}\nexport function isInteger(num: number): boolean {\n\treturn !isNaN(num) && isFinite(num) && Math.trunc(num) == num\n}\nexport function isEmpty(str: string): boolean {\n\treturn str.trim() == ''\n}\nexport function useVerify(\n\tisValid: Ref<boolean>,\n\thintMessage: Ref<string>,\n\tfield: string,\n\titemRule: FormItemRule,\n\tvalue: any,\n\tcallback: (res: FormItemVerifyResult) => void\n) {\n\tlet rule = itemRule\n\tlet type = rule.type\n\tlet valid = true\n\tlet message = rule.message\n\tlet empty = false\n\n\tif (typeof value == 'string') {\n\t\tif ((value as string) == '') empty = true\n\t} else if (Array.isArray(value)) {\n\t\tif ((value as any[]).length == 0) empty = true\n\t}\n\n\t// 验证前进行数据转换处理\n\tif (rule.transform != null) {\n\t\tvalue = rule.transform!(value)\n\t}\n\n\t// 执行自定义验证方法\n\tif (rule.valid != null) {\n\t\tlet msg = rule.valid!(value)\n\t\tvalid = msg.trim() == ''\n\t\tmessage = valid ? message : msg\n\t}\n\n\t// 验证正则表达式\n\tif (rule.pattern != null && typeof value == 'string') {\n\t\tif (!(rule.pattern as RegExp).test(value as string)) valid = false\n\t}\n\n\t// 验证必填项\n\tif (rule.required == true) {\n\t\t// 对字符串进行额外判空\n\n\t\tif (typeof value == 'string') {\n\t\t\tif (isEmpty(value as string)) valid = false\n\t\t}\n\t}\n\n\tif (type != null) {\n\t\tfunction check(type: string) {\n\t\t\tif (typeof value != type) {\n\t\t\t\tvalid = false\n\t\t\t}\n\t\t}\n\t\tswitch (type) {\n\t\t\tcase 'string':\n\t\t\t\tcheck('string')\n\t\t\t\tbreak\n\t\t\tcase 'number':\n\t\t\t\tif (!isNumber(value)) {\n\t\t\t\t\tvalid = false\n\t\t\t\t}\n\t\t\t\tbreak\n\t\t\tcase 'boolean':\n\t\t\t\tcheck('boolean')\n\t\t\t\tbreak\n\t\t\tcase 'object':\n\t\t\t\tcheck('object')\n\t\t\t\tbreak\n\t\t\tcase 'array':\n\t\t\t\tcheck('array')\n\t\t\t\tbreak\n\t\t\tcase 'integer':\n\t\t\t\tif (isNumber(value)) {\n\t\t\t\t\tif (!isInteger(value as number)) {\n\t\t\t\t\t\tvalid = false\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tvalid = false\n\t\t\t\t}\n\t\t\t\tbreak\n\t\t\tcase 'float':\n\t\t\t\tif (isNumber(value)) {\n\t\t\t\t\tif (!isFloat(value as number)) {\n\t\t\t\t\t\tvalid = false\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tvalid = false\n\t\t\t\t}\n\t\t\t\tbreak\n\t\t\tcase 'enum':\n\t\t\t\tif (\n\t\t\t\t\trule.enum != null &&\n\t\t\t\t\t!(rule.enum as any[]).includes(value)\n\t\t\t\t) {\n\t\t\t\t\tvalid = false\n\t\t\t\t}\n\t\t\t\tbreak\n\t\t\tcase 'url':\n\t\t\t\tif (!empty && !isURL(value as string)) valid = false\n\t\t\t\tbreak\n\t\t\tcase 'email':\n\t\t\t\tif (!empty && !isEmail(value as string)) valid = false\n\t\t\t\tbreak\n\t\t\tcase 'idcard':\n\t\t\t\tif (!empty && !isIDCard(value as string)) valid = false\n\t\t\t\tbreak\n\t\t\tcase 'phone':\n\t\t\t\tif (!empty && !isPhone(value as string)) valid = false\n\t\t\t\tbreak\n\t\t}\n\t}\n\tif (typeof value == 'string') {\n\t\tif (\n\t\t\trule.min != null &&\n\t\t\t(value as string).length < (rule.min as number)\n\t\t) {\n\t\t\tvalid = false\n\t\t}\n\t\tif (\n\t\t\trule.max != null &&\n\t\t\t(value as string).length > (rule.max as number)\n\t\t) {\n\t\t\tvalid = false\n\t\t}\n\n\t\t// 如果同时设置 len, max, min，以len的结果为准\n\t\tif (rule.len != null) {\n\t\t\tif ((value as string).length != (rule.len as number)) {\n\t\t\t\tvalid = false\n\t\t\t} else {\n\t\t\t\tvalid = true\n\t\t\t}\n\t\t}\n\t}\n\tif (isNumber(value)) {\n\t\tif (rule.min != null && (value as number) < (rule.min as number)) {\n\t\t\tvalid = false\n\t\t}\n\t\tif (rule.max != null && (value as number) > (rule.max as number)) {\n\t\t\tvalid = false\n\t\t}\n\t}\n\n\tisValid.value = valid\n\thintMessage.value = message ?? ''\n\n\tcallback({\n\t\tmessage,\n\t\tvalid,\n\t\tfield,\n\t})\n}\nexport function findParent(\n\tinstance: ComponentPublicInstance,\n\tparentNames: string[]\n): ComponentPublicInstance | null {\n\tfor (let i = 0; i < parentNames.length; i++) {\n\t\tlet parent = instance.$parent\n\n\n\n\n\t\tlet name = parent?.$options?.name\n\n\t\twhile (parent != null && (name == null || parentNames[i] != name)) {\n\t\t\tparent = parent.$parent\n\t\t\tif (parent != null) {\n\t\t\t\tname = parent.$options.name\n\t\t\t}\n\t\t}\n\t\tif (parent != null) {\n\t\t\treturn parent as ComponentPublicInstance\n\t\t}\n\t}\n\n\treturn null\n}\n", "export type phoneModelType = {\n  phone: string,\n  code: string,\n};\r\n\n", "import fuiStatusBar from \"@/components/firstui/fui-status-bar/fui-status-bar\";\nimport fuiFooter from \"@/components/firstui/fui-footer/fui-footer\";\nimport fuiRow from \"@/components/firstui/fui-row/fui-row\";\nimport fuiTabs from \"@/components/firstui/fui-tabs/fui-tabs\";\nimport fuiInput from \"@/components/firstui/fui-input/fui-input\";\nimport fuiButton from \"@/components/firstui/fui-button/fui-button\";\nimport fuiIcon from \"@/components/firstui/fui-icon/fui-icon\";\nimport fuiCheckbox from \"@/components/firstui/fui-checkbox/fui-checkbox\";\nimport { FuiTabsItemParam } from \"@/components/firstui/fui-types/index.uts\";\nimport uForm from \"@/components/uc/u-form/u-form\";\nimport uFromItem from \"@/components/uc/u-from-item/u-from-item\";\nimport uToast from \"@/components/uc/u-toast/u-toast\";\nimport { phoneModelType } from \"./types\";\nimport { FormItemVerifyResult, FormValidResult, FormValidResultItem, FormItemData, FormItemRule, ToastType, ToastPosition } from \"@/components/uc/types/index.uts\";\nimport { ComponentPublicInstance } from 'vue';\ninterface ShowToastOptions {\n    message: string;\n    type?: ToastType;\n    icon?: string;\n    iconColor?: string;\n    duration?: number;\n}\n// Toast 方法\nconst __sfc__ = defineComponent({\n    __name: 'login',\n    setup(__props): any | null {\n        const __ins = getCurrentInstance()!;\n        const _ctx = __ins.proxy as InstanceType<typeof __sfc__>;\n        const _cache = __ins.renderCache;\n        const inputStyle = { __$originalPosition: new UTSSourceMapPosition(\"inputStyle\", \"pages/login/login.uvue\", 78, 8),\n            inputBorder: true,\n            size: 28,\n            radius: 12,\n            marginTop: 16,\n            type: \"number\"\n        } as UTSJSONObject;\n        // 引入组件\n        const instance = getCurrentInstance();\n        const loginForm = ref<ComponentPublicInstance | null>(null);\n        function onBlur(field: 'phone' | 'code') {\n            console.log(\"field:\", field, \" at pages/login/login.uvue:105\");\n            const f = loginForm.value;\n            if (f != null) {\n                // 使用 $callMethod 调用组件方法\n                f.$callMethod('validItem', field, {\n                    success() {\n                        console.log(\"success\", \" at pages/login/login.uvue:111\");\n                    },\n                    fail(res) {\n                        console.log(\"fail:\", res, \" at pages/login/login.uvue:114\");\n                    }\n                } as FormValidResultItem);\n            }\n        }\n        // 标签页数据\n        const tabsList = ref([\n            { name: '手机号登录', id: 0 },\n            { name: '账号登录', id: 1 },\n        ]);\n        // 当前选中的标签页\n        const current = ref(0);\n        function handleChangeTab(e: FuiTabsItemParam) {\n            console.log(\"handleChangeTab:\", e, \" at pages/login/login.uvue:128\");\n            if (e.index != null) {\n                current.value = e.index as number;\n            }\n        }\n        // 手机号登录表单\n        const phoneModel = reactive<phoneModelType>({\n            phone: '',\n            code: ''\n        } as phoneModelType);\n        // 表单配置\n        const phoneRules = ref<FormItemRule>({\n            type: 'phone',\n            required: true,\n            message: '请输入正确的手机号'\n        } as FormItemRule);\n        const codeRules = ref<FormItemRule>({\n            type: 'number',\n            required: true,\n            message: '请输入验证码'\n        } as FormItemRule);\n        // 国家区号\n        const countryCode = ref('86');\n        // 验证码相关\n        const codeText = ref('获取验证码');\n        const canGetCode = ref(true);\n        //-------\n        // 账号登录表单\n        const accountForm = reactive({\n            username: '',\n            password: '',\n        });\n        // 协议同意\n        const isAgreeProtocol = ref(false);\n        const isDisabledloginButton = computed((): boolean => {\n            return !isAgreeProtocol.value;\n        });\n        // 登录按钮是否禁用\n        function ChangeIsAgreeProtocol() {\n            isAgreeProtocol.value = !isAgreeProtocol.value;\n        }\n        function onSubmit(e: any) {\n            console.log(\"onSubmit:\", e, \" at pages/login/login.uvue:173\");\n        }\n        // Toast 相关数据\n        const toastRef = ref<ComponentPublicInstance | null>(null);\n        const toastVisible = ref(false);\n        const toastMessage = ref('');\n        const toastType = ref<ToastType>('info');\n        const toastIcon = ref('');\n        const toastIconColor = ref('');\n        // 定义 Toast 选项接口\n        function showToast(options: ShowToastOptions) {\n            toastMessage.value = options.message;\n            const optionType = options.type;\n            toastType.value = optionType != null ? optionType : 'info';\n            const optionIcon = options.icon;\n            toastIcon.value = optionIcon != null ? optionIcon : '';\n            const optionIconColor = options.iconColor;\n            toastIconColor.value = optionIconColor != null ? optionIconColor : '';\n            toastVisible.value = true;\n        }\n        function onToastClose() {\n            toastVisible.value = false;\n        }\n        // 测试验证功能\n        function testValidation() {\n            const formInstance = loginForm.value;\n            if (formInstance != null) {\n                // 调用表单验证\n                (formInstance as any).valid({\n                    success: () => {\n                        console.log(\"表单验证成功\", \" at pages/login/login.uvue:216\");\n                    },\n                    fail: (failResults: FormItemVerifyResult[]) => {\n                        console.log(\"表单验证失败:\", failResults, \" at pages/login/login.uvue:219\");\n                    }\n                });\n            }\n        }\n        // 测试 SVG 图标 Toast\n        function testSvgToast() {\n            showToast({\n                message: '这是使用 SVG 图标的提示！',\n                type: 'success',\n                icon: '/static/icons/assessment.svg',\n                iconColor: '#52c41a'\n            });\n        }\n        // 测试 Emoji 图标 Toast\n        function testEmojiToast() {\n            showToast({\n                message: '这是使用 Emoji 图标的提示！',\n                type: 'warning',\n                icon: '🎉'\n            });\n        }\n        return (): any | null => {\n            const _component_block = resolveComponent(\"block\");\n            return createElementVNode(\"view\", utsMapOf({ class: \"login-container\" }), [\n                createVNode(unref(uToast), utsMapOf({\n                    ref_key: \"toastRef\",\n                    ref: toastRef,\n                    visible: unref(toastVisible),\n                    message: unref(toastMessage),\n                    type: unref(toastType),\n                    icon: unref(toastIcon),\n                    iconColor: unref(toastIconColor),\n                    duration: 4000,\n                    position: 'top',\n                    top: 120,\n                    onClose: onToastClose\n                }), null, 8 /* PROPS */, [\"visible\", \"message\", \"type\", \"icon\", \"iconColor\"]),\n                createVNode(unref(fuiStatusBar)),\n                createElementVNode(\"view\", utsMapOf({ class: \"gradient-circle top-right\" })),\n                createElementVNode(\"view\", utsMapOf({ class: \"gradient-circle bottom-left\" })),\n                createElementVNode(\"view\", utsMapOf({ class: \"form-container\" }), [\n                    createElementVNode(\"view\", utsMapOf({ class: \"logo-title\" }), [\n                        createElementVNode(\"text\", utsMapOf({ class: \"title-text\" }), \"登录\")\n                    ]),\n                    createVNode(unref(fuiTabs), utsMapOf({\n                        class: \"form-login-tabs\",\n                        tabs: unref(tabsList),\n                        short: false,\n                        center: \"\",\n                        current: unref(current),\n                        onChange: handleChangeTab\n                    }), null, 8 /* PROPS */, [\"tabs\", \"current\"]),\n                    createElementVNode(\"view\", utsMapOf({ class: \"login-form-container\" }), [\n                        unref(current) === 0\n                            ? createVNode(_component_block, utsMapOf({ key: 0 }), utsMapOf({\n                                default: withSlotCtx((): any[] => [\n                                    createVNode(unref(uForm), utsMapOf({\n                                        ref_key: \"loginForm\",\n                                        ref: loginForm,\n                                        onSubmited: onSubmit,\n                                        modelValue: unref(phoneModel),\n                                        \"onUpdate:modelValue\": $event => { trySetRefValue(phoneModel, $event); },\n                                        showToast: true,\n                                        toastTop: 120,\n                                        toastDuration: 4000\n                                    }), utsMapOf({\n                                        default: withSlotCtx((): any[] => [\n                                            createVNode(unref(uFromItem), utsMapOf({\n                                                field: \"phone\",\n                                                rule: unref(phoneRules),\n                                                marginTop: 16\n                                            }), utsMapOf({\n                                                default: withSlotCtx((): any[] => [\n                                                    createVNode(unref(fuiInput), utsMapOf({\n                                                        ref: \"phone\",\n                                                        onBlur: () => { onBlur('phone'); },\n                                                        borderBottom: false,\n                                                        size: 28,\n                                                        radius: 12,\n                                                        type: \"number\",\n                                                        placeholder: \"请输入手机号\",\n                                                        modelValue: unref(phoneModel).phone,\n                                                        \"onUpdate:modelValue\": ($event: string) => { (unref(phoneModel).phone) = $event; }\n                                                    }), null, 8 /* PROPS */, [\"onBlur\", \"modelValue\", \"onUpdate:modelValue\"])\n                                                ]),\n                                                _: 1 /* STABLE */\n                                            }), 8 /* PROPS */, [\"rule\"]),\n                                            createVNode(unref(uFromItem), utsMapOf({\n                                                field: \"code\",\n                                                rule: unref(codeRules),\n                                                marginTop: 16\n                                            }), utsMapOf({\n                                                default: withSlotCtx((): any[] => [\n                                                    createVNode(unref(fuiInput), utsMapOf({\n                                                        ref: \"code\",\n                                                        onBlur: () => { onBlur('code'); },\n                                                        borderBottom: false,\n                                                        size: 28,\n                                                        radius: 12,\n                                                        type: \"number\",\n                                                        placeholder: \"请输入验证码\",\n                                                        modelValue: unref(phoneModel).code,\n                                                        \"onUpdate:modelValue\": ($event: string) => { (unref(phoneModel).code) = $event; }\n                                                    }), null, 8 /* PROPS */, [\"onBlur\", \"modelValue\", \"onUpdate:modelValue\"])\n                                                ]),\n                                                _: 1 /* STABLE */\n                                            }), 8 /* PROPS */, [\"rule\"])\n                                        ]),\n                                        _: 1 /* STABLE */\n                                    }), 8 /* PROPS */, [\"modelValue\"])\n                                ]),\n                                _: 1 /* STABLE */\n                            }))\n                            : createVNode(_component_block, utsMapOf({ key: 1 }), utsMapOf({\n                                default: withSlotCtx((): any[] => [\" 2 \"]),\n                                _: 1 /* STABLE */\n                            }))\n                    ]),\n                    createElementVNode(\"view\", utsMapOf({ class: \"agreement\" }), [\n                        createVNode(unref(fuiRow), utsMapOf({\n                            justify: \"center\",\n                            class: \"agreement-row\"\n                        }), utsMapOf({\n                            default: withSlotCtx((): any[] => [\n                                createVNode(unref(fuiCheckbox), utsMapOf({\n                                    class: \"agreement-checkbox\",\n                                    checked: unref(isAgreeProtocol),\n                                    borderRadius: \"8rpx\",\n                                    scaleRatio: 0.78,\n                                    onChange: ChangeIsAgreeProtocol\n                                }), null, 8 /* PROPS */, [\"checked\"]),\n                                createElementVNode(\"text\", utsMapOf({ class: \"agreement-text\" }), \"我已阅读并同意\"),\n                                createElementVNode(\"text\", utsMapOf({ class: \"agreement-text agreement-link\" }), \"《用户服务条款》\"),\n                                createElementVNode(\"text\", utsMapOf({ class: \"agreement-text\" }), \"和\"),\n                                createElementVNode(\"text\", utsMapOf({ class: \"agreement-text agreement-link\" }), \"《隐私协议》\")\n                            ]),\n                            _: 1 /* STABLE */\n                        }))\n                    ]),\n                    createElementVNode(\"view\", utsMapOf({ class: \"test-buttons\" }), [\n                        createElementVNode(\"button\", utsMapOf({\n                            class: \"test-btn validate\",\n                            onClick: testValidation\n                        }), \"测试验证\"),\n                        createElementVNode(\"button\", utsMapOf({\n                            class: \"test-btn svg\",\n                            onClick: testSvgToast\n                        }), \"SVG图标\"),\n                        createElementVNode(\"button\", utsMapOf({\n                            class: \"test-btn emoji\",\n                            onClick: testEmojiToast\n                        }), \"Emoji图标\")\n                    ])\n                ]),\n                createElementVNode(\"view\", utsMapOf({ class: \"footer\" }), [\n                    createVNode(unref(fuiFooter), utsMapOf({ text: \"by@海南长养乔智能科技有限责任公司\" }))\n                ])\n            ]);\n        };\n    }\n});\nexport default __sfc__;\nconst GenPagesLoginLoginStyles = [utsMapOf([[\"login-container\", padStyleMapOf(utsMapOf([[\"height\", \"100%\"], [\"display\", \"flex\"], [\"flexDirection\", \"column\"], [\"alignItems\", \"center\"], [\"position\", \"relative\"]]))], [\"gradient-circle\", padStyleMapOf(utsMapOf([[\"position\", \"absolute\"], [\"borderTopLeftRadius\", \"200rpx\"], [\"borderTopRightRadius\", \"200rpx\"], [\"borderBottomRightRadius\", \"200rpx\"], [\"borderBottomLeftRadius\", \"200rpx\"], [\"backgroundColor\", \"#33a1fd\"], [\"zIndex\", 0], [\"fontSize::after\", 12], [\"position::after\", \"absolute\"], [\"top::after\", 0], [\"left::after\", 0]]))], [\"top-right\", padStyleMapOf(utsMapOf([[\"top\", \"-80rpx\"], [\"right\", \"-30rpx\"], [\"width\", \"300rpx\"], [\"height\", \"300rpx\"], [\"opacity\", 0.08]]))], [\"bottom-left\", padStyleMapOf(utsMapOf([[\"bottom\", \"-60rpx\"], [\"left\", \"-60rpx\"], [\"width\", \"280rpx\"], [\"height\", \"280rpx\"], [\"opacity\", 0.1]]))], [\"logo-title\", padStyleMapOf(utsMapOf([[\"textAlign\", \"center\"], [\"marginTop\", \"10rpx\"], [\"marginRight\", 0], [\"marginBottom\", \"30rpx\"], [\"marginLeft\", 0]]))], [\"title-text\", padStyleMapOf(utsMapOf([[\"fontSize\", \"58rpx\"], [\"fontWeight\", \"bold\"], [\"color\", \"#465CFF\"]]))], [\"form-container\", padStyleMapOf(utsMapOf([[\"display\", \"flex\"], [\"justifyContent\", \"center\"], [\"alignItems\", \"center\"], [\"width\", \"100%\"], [\"height\", \"70%\"]]))], [\"login-form-container\", utsMapOf([[\".form-container \", utsMapOf([[\"paddingTop\", 0], [\"paddingRight\", \"16rpx\"], [\"paddingBottom\", 0], [\"paddingLeft\", \"16rpx\"], [\"display\", \"flex\"], [\"minWidth\", \"580rpx\"]])]])], [\"agreement\", padStyleMapOf(utsMapOf([[\"display\", \"flex\"], [\"alignItems\", \"flex-start\"], [\"marginTop\", \"24rpx\"], [\"color\", \"#CCCCCC\"]]))], [\"agreement-row\", utsMapOf([[\".agreement \", utsMapOf([[\"alignItems\", \"center\"]])]])], [\"agreement-text\", padStyleMapOf(utsMapOf([[\"fontSize\", \"24rpx\"]]))], [\"agreement-link\", padStyleMapOf(utsMapOf([[\"color\", \"#465CFF\"], [\"textDecoration\", \"none\"]]))], [\"footer\", padStyleMapOf(utsMapOf([[\"textAlign\", \"center\"], [\"paddingTop\", \"16rpx\"], [\"paddingRight\", \"16rpx\"], [\"paddingBottom\", \"16rpx\"], [\"paddingLeft\", \"16rpx\"], [\"color\", \"#CCCCCC\"], [\"fontSize\", \"32rpx\"], [\"marginTop\", \"20rpx\"]]))], [\"test-buttons\", padStyleMapOf(utsMapOf([[\"display\", \"flex\"], [\"flexWrap\", \"wrap\"], [\"gap\", \"16rpx\"], [\"marginTop\", \"32rpx\"], [\"justifyContent\", \"center\"]]))], [\"test-btn\", utsMapOf([[\"\", utsMapOf([[\"paddingTop\", \"16rpx\"], [\"paddingRight\", \"24rpx\"], [\"paddingBottom\", \"16rpx\"], [\"paddingLeft\", \"24rpx\"], [\"borderTopLeftRadius\", \"8rpx\"], [\"borderTopRightRadius\", \"8rpx\"], [\"borderBottomRightRadius\", \"8rpx\"], [\"borderBottomLeftRadius\", \"8rpx\"], [\"borderTopWidth\", \"medium\"], [\"borderRightWidth\", \"medium\"], [\"borderBottomWidth\", \"medium\"], [\"borderLeftWidth\", \"medium\"], [\"borderTopStyle\", \"none\"], [\"borderRightStyle\", \"none\"], [\"borderBottomStyle\", \"none\"], [\"borderLeftStyle\", \"none\"], [\"borderTopColor\", \"#000000\"], [\"borderRightColor\", \"#000000\"], [\"borderBottomColor\", \"#000000\"], [\"borderLeftColor\", \"#000000\"], [\"fontSize\", \"24rpx\"], [\"color\", \"#FFFFFF\"], [\"cursor\", \"pointer\"], [\"transitionDuration\", \"0.3s\"], [\"transitionTimingFunction\", \"ease\"], [\"minWidth\", \"120rpx\"]])], [\".validate\", utsMapOf([[\"backgroundColor\", \"#ff4d4f\"]])], [\".validate:hover\", utsMapOf([[\"backgroundColor\", \"#ff7875\"]])], [\".svg\", utsMapOf([[\"backgroundColor\", \"#52c41a\"]])], [\".svg:hover\", utsMapOf([[\"backgroundColor\", \"#73d13d\"]])], [\".emoji\", utsMapOf([[\"backgroundColor\", \"#faad14\"]])], [\".emoji:hover\", utsMapOf([[\"backgroundColor\", \"#ffc53d\"]])]])], [\"@TRANSITION\", utsMapOf([[\"test-btn\", utsMapOf([[\"duration\", \"0.3s\"], [\"timingFunction\", \"ease\"]])]])]])];\n//# sourceMappingURL=login.uvue.map", "<template>\r\n  <view class=\"login-container\">\r\n    <!-- 页面级 Toast 提示框 -->\r\n    <uToast ref=\"toastRef\" :visible=\"toastVisible\" :message=\"toastMessage\" :type=\"toastType\" :icon=\"toastIcon\"\r\n      :iconColor=\"toastIconColor\" :duration=\"4000\" :position=\"'top'\" :top=\"120\" @close=\"onToastClose\" />\r\n\r\n    <fui-status-bar></fui-status-bar>\r\n    <!-- 右上角渐变球 -->\r\n    <view class=\"gradient-circle top-right\"></view>\r\n    <!-- 左下角渐变球 -->\r\n    <view class=\"gradient-circle bottom-left\"></view>\r\n    <!-- 切换登录 -->\r\n    <!-- 登录表单 -->\r\n    <view class=\"form-container\">\r\n      <!-- Logo/标题 -->\r\n      <view class=\"logo-title\">\r\n        <text class=\"title-text\">登录</text>\r\n      </view>\r\n      <!-- 手机号登录 -->\r\n      <fui-tabs class=\"form-login-tabs\" :tabs=\"tabsList\" :short=\"false\" center :current=\"current\"\r\n        @change=\"handleChangeTab\"></fui-tabs>\r\n      <view class=\"login-form-container\">\r\n\r\n        <block v-if=\"current === 0\">\r\n          <!-- TODO: 要加前缀 +86 fui-dropdown-menu -->\r\n          <uForm ref=\"loginForm\" @submited=\"onSubmit\" v-model=\"phoneModel\" :showToast=\"true\" :toastTop=\"120\"\r\n            :toastDuration=\"4000\">\r\n            <uFromItem field=\"phone\" :rule=\"phoneRules\" :marginTop=\"16\">\r\n              <fui-input ref=\"phone\" @blur=\"onBlur('phone')\" :borderBottom=\"false\" :size=\"28\" :radius=\"12\" type=\"number\"\r\n                placeholder=\"请输入手机号\" v-model=\"phoneModel.phone as string\">\r\n              </fui-input>\r\n            </uFromItem>\r\n            <uFromItem field=\"code\" :rule=\"codeRules\" :marginTop=\"16\">\r\n              <fui-input ref=\"code\" @blur=\"onBlur('code')\" :borderBottom=\"false\" :size=\"28\" :radius=\"12\" type=\"number\"\r\n                placeholder=\"请输入验证码\" v-model=\"phoneModel.code as string\">\r\n\r\n              </fui-input>\r\n            </uFromItem>\r\n          </uForm>\r\n        </block>\r\n\r\n        <!-- 账号登录 -->\r\n        <block v-else>\r\n          2\r\n        </block>\r\n      </view>\r\n\r\n      <!-- 协议同意 -->\r\n      <view class=\"agreement\">\r\n        <fui-row justify=\"center\" class=\"agreement-row\">\r\n          <!-- 必须要这样写，转kt的时候不这样写会出现问题 -->\r\n          <fui-checkbox class=\"agreement-checkbox\" :checked=\"isAgreeProtocol\" borderRadius=\"8rpx\" :scaleRatio=\"0.78\"\r\n            @change=\"ChangeIsAgreeProtocol\"></fui-checkbox>\r\n          <text class=\"agreement-text\">我已阅读并同意</text>\r\n          <text class=\"agreement-text agreement-link\">《用户服务条款》</text>\r\n          <text class=\"agreement-text\">和</text>\r\n          <text class=\"agreement-text agreement-link\">《隐私协议》</text>\r\n        </fui-row>\r\n      </view>\r\n\r\n      <!-- 验证测试按钮 -->\r\n      <view class=\"test-buttons\">\r\n        <button class=\"test-btn validate\" @click=\"testValidation\">测试验证</button>\r\n        <button class=\"test-btn svg\" @click=\"testSvgToast\">SVG图标</button>\r\n        <button class=\"test-btn emoji\" @click=\"testEmojiToast\">Emoji图标</button>\r\n      </view>\r\n\r\n      <!-- 登录按钮 -->\r\n    </view>\r\n    <!-- 底部 -->\r\n    <view class=\"footer\">\r\n      <fui-footer text=\"by@海南长养乔智能科技有限责任公司\"></fui-footer>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup lang=\"uts\">\r\n\tconst inputStyle = {\r\n\t\tinputBorder: true,\r\n\t\tsize: 28,\r\n\t\tradius: 12,\r\n\t\tmarginTop: 16,\r\n\t\ttype: \"number\"\r\n\t}\r\n\t// 引入组件\r\n\timport fuiStatusBar from \"@/components/firstui/fui-status-bar/fui-status-bar\";\r\n\timport fuiFooter from \"@/components/firstui/fui-footer/fui-footer\";\r\n\timport fuiRow from \"@/components/firstui/fui-row/fui-row\";\r\n\timport fuiTabs from \"@/components/firstui/fui-tabs/fui-tabs\";\r\n\timport fuiInput from \"@/components/firstui/fui-input/fui-input\";\r\n\timport fuiButton from \"@/components/firstui/fui-button/fui-button\";\r\n\timport fuiIcon from \"@/components/firstui/fui-icon/fui-icon\";\r\n\timport fuiCheckbox from \"@/components/firstui/fui-checkbox/fui-checkbox\";\r\n\timport { FuiTabsItemParam } from \"@/components/firstui/fui-types/index.uts\";\r\n\timport uForm from \"@/components/uc/u-form/u-form\";\r\n\timport uFromItem from \"@/components/uc/u-from-item/u-from-item\";\r\n\timport uToast from \"@/components/uc/u-toast/u-toast\";\r\n\timport { phoneModelType } from \"./types\";\r\n\timport { FormItemVerifyResult, FormValidResult, FormValidResultItem, FormItemData, FormItemRule, ToastType, ToastPosition } from \"@/components/uc/types/index.uts\"\r\n\timport { ComponentPublicInstance } from 'vue'\r\n\tconst instance = getCurrentInstance();\r\n\r\n\tconst loginForm = ref<ComponentPublicInstance | null>(null)\r\n\tfunction onBlur(field : 'phone' | 'code') {\r\n\t\tconsole.log(\"field:\", field)\r\n\t\tconst f = loginForm.value;\r\n\t\tif (f != null) {\r\n\t\t\t// 使用 $callMethod 调用组件方法\r\n\t\t\tf.$callMethod('validItem', field, {\r\n\t\t\t\tsuccess() {\r\n\t\t\t\t\tconsole.log(\"success\");\r\n\t\t\t\t},\r\n\t\t\t\tfail(res) {\r\n\t\t\t\t\tconsole.log(\"fail:\", res);\r\n\t\t\t\t}\r\n\t\t\t} as FormValidResultItem)\r\n\t\t}\r\n\t}\r\n\t// 标签页数据\r\n\tconst tabsList = ref([\r\n\t\t{ name: '手机号登录', id: 0 },\r\n\t\t{ name: '账号登录', id: 1 },\r\n\t]);\r\n\r\n\t// 当前选中的标签页\r\n\tconst current = ref(0);\r\n\tfunction handleChangeTab(e : FuiTabsItemParam) {\r\n\t\tconsole.log(\"handleChangeTab:\", e);\r\n\t\tif (e.index !== null) {\r\n\t\t\tcurrent.value = e.index as number;\r\n\t\t}\r\n\t}\r\n\t// 手机号登录表单\r\n\tconst phoneModel = reactive<phoneModelType>({\r\n\t\tphone: '',\r\n\t\tcode: ''\r\n\t})\r\n\t// 表单配置\r\n\tconst phoneRules = ref<FormItemRule>({\r\n\t\ttype: 'phone',\r\n\t\trequired: true,\r\n\t\tmessage: '请输入正确的手机号'\r\n\t})\r\n\tconst codeRules = ref<FormItemRule>({\r\n\t\ttype: 'number',\r\n\t\trequired: true,\r\n\t\tmessage: '请输入验证码'\r\n\t})\r\n\t// 国家区号\r\n\tconst countryCode = ref('86');\r\n\r\n\t// 验证码相关\r\n\tconst codeText = ref('获取验证码');\r\n\tconst canGetCode = ref(true);\r\n\t//-------\r\n\t// 账号登录表单\r\n\tconst accountForm = reactive({\r\n\t\tusername: '',\r\n\t\tpassword: '',\r\n\t});\r\n\r\n\t// 协议同意\r\n\tconst isAgreeProtocol = ref(false);\r\n\tconst isDisabledloginButton = computed(() => {\r\n\t\treturn !isAgreeProtocol.value;\r\n\t});\r\n\r\n\t// 登录按钮是否禁用\r\n\tfunction ChangeIsAgreeProtocol() {\r\n\t\tisAgreeProtocol.value = !isAgreeProtocol.value;\r\n\t}\r\n\tfunction onSubmit(e : any) {\r\n\t\tconsole.log(\"onSubmit:\", e);\r\n\t}\r\n\r\n\t// Toast 相关数据\r\n\tconst toastRef = ref<ComponentPublicInstance | null>(null)\r\n\tconst toastVisible = ref(false)\r\n\tconst toastMessage = ref('')\r\n\tconst toastType = ref<ToastType>('info')\r\n\tconst toastIcon = ref('')\r\n\tconst toastIconColor = ref('')\r\n\r\n\t// 定义 Toast 选项接口\r\n\tinterface ShowToastOptions {\r\n\t\tmessage: string\r\n\t\ttype?: ToastType\r\n\t\ticon?: string\r\n\t\ticonColor?: string\r\n\t\tduration?: number\r\n\t}\r\n\r\n\t// Toast 方法\r\n\tfunction showToast(options: ShowToastOptions) {\r\n\t\ttoastMessage.value = options.message\r\n\t\tconst optionType = options.type\r\n\t\ttoastType.value = optionType != null ? optionType : 'info'\r\n\t\tconst optionIcon = options.icon\r\n\t\ttoastIcon.value = optionIcon != null ? optionIcon : ''\r\n\t\tconst optionIconColor = options.iconColor\r\n\t\ttoastIconColor.value = optionIconColor != null ? optionIconColor : ''\r\n\t\ttoastVisible.value = true\r\n\t}\r\n\r\n\tfunction onToastClose() {\r\n\t\ttoastVisible.value = false\r\n\t}\r\n\r\n\t// 测试验证功能\r\n\tfunction testValidation() {\r\n\t\tconst formInstance = loginForm.value\r\n\t\tif (formInstance != null) {\r\n\t\t\t// 调用表单验证\r\n\t\t\t(formInstance as any).valid({\r\n\t\t\t\tsuccess: () => {\r\n\t\t\t\t\tconsole.log(\"表单验证成功\");\r\n\t\t\t\t},\r\n\t\t\t\tfail: (failResults: FormItemVerifyResult[]) => {\r\n\t\t\t\t\tconsole.log(\"表单验证失败:\", failResults);\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t}\r\n\t}\r\n\r\n\t// 测试 SVG 图标 Toast\r\n\tfunction testSvgToast() {\r\n\t\tshowToast({\r\n\t\t\tmessage: '这是使用 SVG 图标的提示！',\r\n\t\t\ttype: 'success',\r\n\t\t\ticon: '/static/icons/assessment.svg',\r\n\t\t\ticonColor: '#52c41a'\r\n\t\t})\r\n\t}\r\n\r\n\t// 测试 Emoji 图标 Toast\r\n\tfunction testEmojiToast() {\r\n\t\tshowToast({\r\n\t\t\tmessage: '这是使用 Emoji 图标的提示！',\r\n\t\t\ttype: 'warning',\r\n\t\t\ticon: '🎉'\r\n\t\t})\r\n\t}\r\n\r\n\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.login-container {\r\n  height: 100%;\r\n  min-height: 100vh;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  position: relative;\r\n  // overflow: hidden;\r\n}\r\n\r\n.gradient-circle {\r\n  position: absolute;\r\n\r\n  border-radius: 200rpx;\r\n\r\n\r\n\r\n\r\n  background: radial-gradient(circle, #33a1fd 0%, #0062e6 70%);\r\n  background-color: #33a1fd;\r\n  /* 备用背景颜色 */\r\n  z-index: 0;\r\n}\r\n\r\n/* 添加调试样式 */\r\n.gradient-circle::after {\r\n  // content: '调试: 渐变球';\r\n  // color: red;\r\n  font-size: 12px;\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n}\r\n\r\n.top-right {\r\n  top: -80rpx;\r\n  right: -30rpx;\r\n  width: 300rpx;\r\n  /* 调整宽度 */\r\n  height: 300rpx;\r\n  /* 调整高度 */\r\n  opacity: 0.08;\r\n}\r\n\r\n.bottom-left {\r\n  bottom: -60rpx;\r\n  left: -60rpx;\r\n  width: 280rpx;\r\n  /* 调整宽度 */\r\n  height: 280rpx;\r\n  /* 调整高度 */\r\n  opacity: 0.1;\r\n}\r\n\r\n\r\n.logo-title {\r\n  text-align: center;\r\n  margin: 10rpx 0 30rpx;\r\n}\r\n\r\n.title-text {\r\n  font-size: 58rpx;\r\n  font-weight: bold;\r\n  color: $fui-color-primary;\r\n  //   font-family: ;\r\n}\r\n\r\n.form-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  width: 100%;\r\n  height: 70%;\r\n\r\n  .login-form-container {\r\n    // width: 100%;\r\n    padding: 0 16rpx;\r\n    display: flex;\r\n    min-width: 580rpx;\r\n  }\r\n\r\n  .form-login-tabs {}\r\n}\r\n\r\n.agreement {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  margin-top: 24rpx;\r\n  color: $fui-color-minor;\r\n\r\n  .agreement-row {\r\n    align-items: center;\r\n  }\r\n}\r\n\r\n.agreement-text {\r\n  font-size: 24rpx;\r\n}\r\n\r\n.agreement-link {\r\n  color: $fui-color-primary;\r\n  text-decoration: none;\r\n  font-weight: 500;\r\n}\r\n\r\n.footer {\r\n  text-align: center;\r\n  padding: 16rpx;\r\n  color: $fui-color-minor;\r\n  font-size: $fui-input-size;\r\n  margin-top: 20rpx;\r\n  // font-family: var(--font-content);\r\n}\r\n\r\n/* Toast 测试按钮样式 */\r\n.test-buttons {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 16rpx;\r\n  margin-top: 32rpx;\r\n  justify-content: center;\r\n}\r\n\r\n.test-btn {\r\n  padding: 16rpx 24rpx;\r\n  border-radius: 8rpx;\r\n  border: none;\r\n  font-size: 24rpx;\r\n  color: white;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  min-width: 120rpx;\r\n\r\n  &.validate {\r\n    background-color: #ff4d4f;\r\n\r\n    &:hover {\r\n      background-color: #ff7875;\r\n    }\r\n  }\r\n\r\n  &.svg {\r\n    background-color: #52c41a;\r\n\r\n    &:hover {\r\n      background-color: #73d13d;\r\n    }\r\n  }\r\n\r\n  &.emoji {\r\n    background-color: #faad14;\r\n\r\n    &:hover {\r\n      background-color: #ffc53d;\r\n    }\r\n  }\r\n}\r\n</style>", "import 'D:/HBuilderX/plugins/uniapp-cli-vite/node_modules/@dcloudio/uni-console/src/runtime/app/index.ts';import App from './App.uvue'\r\nimport { createSSRApp } from 'vue'\r\n\r\nexport function createApp() {\r\n  const app = createSSRApp(App)\r\n  return {\r\n    app\r\n  }\r\n}\nexport function main(app: IApp) {\n    definePageRoutes();\n    defineAppConfig();\n    (createApp()['app'] as VueApp).mount(app, GenUniApp());\n}\n\nexport class UniAppConfig extends io.dcloud.uniapp.appframe.AppConfig {\n    override name: string = \"demo\"\n    override appid: string = \"__UNI__4AABA03\"\n    override versionName: string = \"1.0.0\"\n    override versionCode: string = \"100\"\n    override uniCompilerVersion: string = \"4.66\"\n    \n    constructor() { super() }\n}\n\nimport GenPagesLoginLoginClass from './pages/login/login.uvue?type=page'\nimport GenPagesIndexIndexClass from './pages/index/index.uvue?type=page'\nfunction definePageRoutes() {\n__uniRoutes.push({ path: \"pages/login/login\", component: GenPagesLoginLoginClass, meta: { isQuit: true } as UniPageMeta, style: utsMapOf([[\"navigationBarTitleText\",\"登录\"],[\"navigationStyle\",\"custom\"]]) } as UniPageRoute)\n__uniRoutes.push({ path: \"pages/index/index\", component: GenPagesIndexIndexClass, meta: { isQuit: false } as UniPageMeta, style: utsMapOf([[\"navigationBarTitleText\",\"index\"]]) } as UniPageRoute)\n}\nconst __uniTabBar: Map<string, any | null> | null = null\nconst __uniLaunchPage: Map<string, any | null> = utsMapOf([[\"url\",\"pages/login/login\"],[\"style\",utsMapOf([[\"navigationBarTitleText\",\"登录\"],[\"navigationStyle\",\"custom\"]])]])\nfunction defineAppConfig(){\n  __uniConfig.entryPagePath = '/pages/login/login'\n  __uniConfig.globalStyle = utsMapOf([[\"pageOrientation\",\"portrait\"],[\"backgroundColor\",\"#F8F8F8\"],[\"backgroundColorTop\",\"#F4F5F6\"],[\"backgroundColorBottom\",\"#F4F5F6\"],[\"navigationStyle\",\"custom\"]])\n  __uniConfig.getTabBarConfig = ():Map<string, any> | null =>  null\n  __uniConfig.tabBar = __uniConfig.getTabBarConfig()\n  __uniConfig.conditionUrl = ''\n  __uniConfig.uniIdRouter = utsMapOf()\n  \n  __uniConfig.ready = true\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;+BAgCuB;+BC<PERSON>;AElCZ,qCAAS;+BCMqB;+BHkBlB;;;;;;ADvBL,IAAS,kBACd,OAAO,MAAM,EACb,MAAM,MAAM,EACZ,IAAI,MAAM,GACT,WAAQ,aAAmB;IAC5B,IAAI,SAAS,MAAM,QAAQ,MAAM,MAAM;QAAI,OAAO,WAAQ,OAAO,CAAC,IAAI;;IACtE,OAAO,MACJ,KAAK,CAAC,KACN,MAAM,CAAC,WAAQ,cACd,IACE,SAAS,WAAQ,cACjB,MAAM,MAAM,GACX,WAAQ,aAAsB;QAC/B,OAAO,QAAQ,IAAI,CAAC,IAAC,SAAS,WAAQ,aAAsB;YAC1D,IAAI,UAAU,IAAI;gBAAE,OAAO,WAAQ,OAAO,CAAC;;YAC3C,OAAO,iBAAiB,MAAM,MAAM;QACtC;;IACF;MACA,WAAQ,OAAO,CAAC,IAAI;AAE1B;AAEA,IAAM,yBAAiB,GAAG;AAC1B,IAAS,iBACP,MAAM,MAAM,EACZ,MAAM,MAAM,EACZ,IAAI,MAAM,GACT,WAAQ,aAAmB;IAC5B,OAAO,AAAI,WAAQ,IAAC,SAAS,OAAW;QACtC,IAAM,SAAS,uCACb,MAAK,AAAC,UAAO,OAAK,MAAG,OAAK,MAAG,IAC7B,OAAA,OAAO;YACL,QAAQ,IAAI;QACd;;QAEF,IAAM,QAAQ,WAAW,KAAM;YAE7B,OAAO,KAAK,oBACV,OAAM,IAAI,EACV,SAAQ;YAEV,QAAQ,IAAI;QACd;UAAG;QAEH,OAAO,MAAM,CAAC,IAAC,EAAM;YACnB,aAAa;YACb,QAAQ;QACV;;QACA,OAAO,OAAO,CAAC,IAAC,EAAM;YACpB,aAAa;YACb,QAAQ,IAAI;QACd;;QACA,OAAO,OAAO,CAAC,IAAC,EAAM;YACpB,aAAa;YACb,QAAQ,IAAI;QACd;;IACF;;AACF;AK1DO,IAAS,4BAA4B,WAAQ,OAAO,EAAE;IAC3D,IAAM,OAAO,MAAM;IACnB,IAAM,MAAM,MAAM;IAClB,IAAM,IAAI,MAAM;IAChB,IAAI,SAAS,MAAM,QAAQ,MAAM,MAAM;QAAI,OAAO,WAAQ,OAAO,CAAC,KAAK;;IACvE,IAAI,YAAY,cAAoB,IAAI;IACxC,4BACE,OAAI,MAAM,CAAI;QACZ;IACF;MACA,IAAC,MAAM,MAAM,CAAK;QAChB,YAAY,8BACV,OAAA;IAEJ;;IAEF,OAAO,WAAQ,OAAO,GACnB,IAAI,CAAC,OAAI,WAAQ,OAAO,EAAK;QAC5B,OAAO,kBAAkB,OAAO,MAAM,IAAI,IAAI,CAAC,IAAC,SAAS,OAAO,CAAI;YAClE,IAAI,UAAU,IAAI,EAAE;gBAClB,OAAO,KAAK;YACd;YACA,aAAa;YACb,OAAO,IAAI;QACb;;IACF;MACC,OAAK,CAAC,OAAI,OAAO,CAAI;QACpB,OAAO,KAAK;IACd;;AACJ;;IAEA;;AJhCE,IAAI,wBAAgB,CAAA;AACf;;iBAWO,IAAU,wBAAO,EAAA;YACzB,QAAQ,GAAG,CAAC,cAAY;QAC1B;;kBACQ,IAAU,sBAAO,EAAA;YACvB,QAAQ,GAAG,CAAC,YAAU;QACxB;;kBACQ,MAAA;YACN,QAAQ,GAAG,CAAC,YAAU;QACxB;;4BAEqB,MAAA;YACnB,QAAQ,GAAG,CAAC,yBAAuB;YACnC,IAAI,iBAAiB,CAAC,EAAE;gBACtB,+BACE,QAAO,YACP,WAAU;gBAEZ,gBAAgB,KAAK,GAAG;gBACxB,WAAW,KAAI;oBACb,gBAAgB,CAAA;gBAClB,GAAG,IAAI;mBACF,IAAI,KAAK,GAAG,KAAK,gBAAgB,IAAI,EAAE;gBAC5C,gBAAgB,KAAK,GAAG;gBACxB;;QAEJ;;eACA,MAAM;YACJ,QAAQ,GAAG,CAAC,YAAU;QACxB;;gBAES,IAAS,KAAK,GAAG,EAAA;YACxB,QAAQ,GAAG,CAAC,aAAa,KAAG;QAC9B;;;;;;;;AAcF;;;;;;;;AGnDD,IAAI,kBAAkB,KAAG,wBAAwB,eAAe,GAAA;;;;;;;;AEqC5B,WAAzB;IACX;mBAAO,MAAM,CAAC;IACd,cAAO,MAAM,SAAC;IACd,mBAAY,MAAM,SAAC;IACnB,gBAAS,MAAM,SAAC;IAChB,gBAAS,MAAM,SAAC;IAChB,eAAQ,MAAM,SAAC;;;;;;;;;kDANJ,qCAAA;;;;;6HACX,eAAA,MACA,cAAA,KACA,mBAAA,UACA,gBAAA,OACA,gBAAA,OACA,eAAA;;;;;;;;;iBALA,MAAO,MAAM;;6DAAb;;;;;;mCAAA;oBAAA;;;iBACA,KAAO,MAAM;;4DAAb;;;;;;mCAAA;oBAAA;;;iBACA,UAAY,MAAM;;iEAAlB;;;;;;mCAAA;oBAAA;;;iBACA,OAAS,MAAM;;8DAAf;;;;;;mCAAA;oBAAA;;;iBACA,OAAS,MAAM;;8DAAf;;;;;;mCAAA;oBAAA;;;iBACA,MAAQ,MAAM;;6DAAd;;;;;;mCAAA;oBAAA;;;;AAsBoC,WAAzB;IACX;sBAAU,OAAO,SAAC;IAClB;oBAAQ,MAAM,CAAC;;;;;;AAoIe,WAAnB;IACX;mBAAO,MAAM,CAAC;IACd,eAAQ,MAAM,SAAC;IACf,uBAAgB,MAAM,SAAC;IACvB,gBAAS,MAAM,SAAC;IAChB,gBAAS,OAAO,SAAC;IACjB,mBAAY,OAAO,SAAC;IACpB,gBAAS,MAAM,SAAC;;;;;;;;;4CAPL,+BAAA;;;;;uHACX,eAAA,MACA,eAAA,MACA,uBAAA,cACA,gBAAA,OACA,gBAAA,OACA,mBAAA,UACA,gBAAA;;;;;;;;;iBANA,MAAO,MAAM;;6DAAb;;;;;;mCAAA;oBAAA;;;iBACA,MAAQ,MAAM;;6DAAd;;;;;;mCAAA;oBAAA;;;iBACA,cAAgB,MAAM;;qEAAtB;;;;;;mCAAA;oBAAA;;;iBACA,OAAS,MAAM;;8DAAf;;;;;;mCAAA;oBAAA;;;iBACA,OAAS,OAAO;;8DAAhB;;;;;;mCAAA;oBAAA;;;iBACA,UAAY,OAAO;;iEAAnB;;;;;;mCAAA;oBAAA;;;iBACA,OAAS,MAAM;;8DAAf;;;;;;mCAAA;oBAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvL0B,WAAf;IACX;oBAAO,MAAM,CAAA;IACb;uBAAU,wBAAuB;;;;;;;;;wCAFtB,2BAAA;;;;;mHACX,gBAAA,OACA,mBAAA;;;;;;;;;iBADA,OAAO,MAAM;;8DAAb;;;;;;mCAAA;oBAAA;;;iBACA,UAAU;;iEAAV;;;;;;mCAAA;oBAAA;;;;AAE0B,WAAf;IAIX,eACG,MAAQ,SAYJ;IAIP,mBAAW,OAAO,SAAO;IAIzB,kBAAU,MAAM,SAAO;IAIvB,kBAAU,kBAAa;IAIvB,cAAM,MAAM,SAAO;IAInB,cAAM,MAAM,SAAO;IAInB,cAAM,MAAM,SAAO;IAInB,iBAAO,SAAM,GAAG,UAAQ;IAIxB,sBAAc,OAAO,GAAG,KAAK,GAAG,UAAQ;IAMxC,kBAAU,OAAO,GAAG,KAAK,MAAM,UAAQ;;;;;;;;;wCAvD5B,2BAAA;;;;;mHAIX,eAAA,MAiBA,mBAAA,UAIA,kBAAA,SAIA,kBAAA,SAIA,cAAA,KAIA,cAAA,KAIA,cAAA,KAIA,iBAAA,QAIA,oBAAA,WAMA,gBAAA;;;;;;;;;iBAnDA,MACG,MAAQ;;6DADX;;;;;;mCAAA;oBAAA;;;iBAiBA,UAAW,OAAO;;iEAAlB;;;;;;mCAAA;oBAAA;;;iBAIA,SAAU,MAAM;;gEAAhB;;;;;;mCAAA;oBAAA;;;iBAIA,SAAU;;gEAAV;;;;;;mCAAA;oBAAA;;;iBAIA,KAAM,MAAM;;4DAAZ;;;;;;mCAAA;oBAAA;;;iBAIA,KAAM,MAAM;;4DAAZ;;;;;;mCAAA;oBAAA;;;iBAIA,KAAM,MAAM;;4DAAZ;;;;;;mCAAA;oBAAA;;;iBAIA,QAAO,SAAM,GAAG;;6DAAhB;;;;;;mCAAA;oBAAA;;;;AAYkC,WAAvB;IAIX;oBAAO,OAAO,SAAA;IAId,kBAAU,MAAM,SAAO;IAIvB;oBAAO,MAAM,CAAA;;;;;;AAEgB,WAAlB;IAIX,yBAAiB,IAAI,UAAQ;IAI7B,iBAAS,sBAAa,0BAA2B,IAAI,UAAQ;;;;;;AAE5B,WAAtB;IAIX,yBAAiB,IAAI,UAAQ;IAI7B,iBAAS,aAAc,0BAAyB,IAAI,UAAQ;;;;;;UAMjD,YAAY,MAAS;UAKrB,gBAAgB,MAAK;UEvIhB;QAChB,KAAK,MAAM;QACX,OAAO,MAAM;QACb,KAAK,OAAO;QACZ,SAAS,OAAO;;ANOX,IAAU,cAAc,MAAO,MAAM,GAAI,WAAQ,MAAM,EAAC;IAE7D,OAAO,AAAI,WAAQ,IAAC,SAAS,OAAU;QAetC,IAAM,MAAM,cAAc;QAC1B,IAAG,OAAO,IAAI,EAAE;YACf,OAAO;;QAER,QAAQ,MAAK,OAAO,CAAC,wBAAO;IAwB7B;;AAED;AAOM,IAAU,aAAa,WAAY,MAAM,GAAI,MAAM,CAAA;IACxD,IAAM,aAAa,4BAAkB,CAAlB,mBAAmB,wEAAY,OAAO,CAAC,uBAAO;IACjE,OAAO,wBAAsB;AAC9B;;;kEOpEA,EAAE;;;;;;;;;;uDAAF,EAAE;;;;;;;;;;qDAAF,EAAE;;;;;;;;ACoDI,IAAU,SAAS,OAAO,GAAG,CAAO,GAAG,OAAO,CAAA;IAEnD,OAAO;QACN;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACA,CAAC,QAAQ,CAAC,oBAAO;AAyBnB;AACM,IAAU,MAAM,KAAK,MAAM,GAAG,OAAO,CAAA;IAC1C,IAAI,MACH;IACD,OAAO,IAAI,IAAI,CAAC;AACjB;AAEM,IAAU,QAAQ,OAAO,MAAM,GAAG,OAAO,CAAA;IAC9C,IAAM,MAAM;IACZ,OAAO,IAAI,IAAI,CAAC;AACjB;AAYM,IAAU,QAAQ,OAAO,MAAM,GAAG,OAAO,CAAA;IAC9C,IAAM,MAAM;IACZ,OAAO,IAAI,IAAI,CAAC;AACjB;AAEM,IAAU,SAAS,MAAM,MAAM,GAAG,OAAO,CAAA;IAC9C,IAAI,SAAS;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACA;IACD,IAAI,QAAQ,KAAK,MAAM,IAAI,EAAE;IAC7B,IAAI,QACH,KAAK,IAAI,MAAM,MACf,CAAC,+FAA0E,IAAI,CAC9E;IAEF,IAAI,QAAQ,CAAC,OAAO,QAAQ,CAAC,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;IAC5C,IAAI,SAAS,SAAS;QAAO,OAAO,KAAK;;IAGzC,IAAI,KAAK,MAAM,IAAI,EAAE,EAAE;QACtB,IAAI,QAAQ,KAAK,KAAK,CAAC;QAGvB,IAAI,2BAAS;AAAC,aAAC;AAAE,aAAC;AAAE,cAAE;AAAE,aAAC;AAAE,aAAC;AAAE,aAAC;AAAE,aAAC;AAAE,aAAC;AAAE,aAAC;AAAE,aAAC;AAAE,aAAC;AAAE,aAAC;AAAE,cAAE;AAAE,aAAC;AAAE,aAAC;AAAE,aAAC;AAAE,aAAC;SAAC;QAElE,IAAI,SAAS;YAAC;YAAK;YAAK;YAAK;YAAK;YAAM;YAAK;YAAK;YAAK;YAAK;YAAK;SAAI;QACrE,IAAI,cAAM,CAAC;QACX,IAAI,aAAK,CAAC;QACV,IAAI,aAAK,CAAC;YACV;YAAK,IAAI,YAAI,CAAC;YAAd,MAAgB,IAAI,EAAE;gBACrB,KAAK,SAAS,KAAK,CAAC,EAAE;gBACtB,KAAK,MAAM,CAAC,EAAE;gBACd,OAAO,KAAK;gBAHW;;;QAMxB,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE;YAClC,OAAO,KAAK;;;IAGd,OAAO,IAAI;AACZ;AAMM,IAAU,QAAQ,KAAK,MAAM,GAAG,OAAO,CAAA;IAC5C,OAAO,CAAC,MAAM,QAAQ,SAAS,QAAQ,KAAK,KAAK,CAAC,QAAQ;AAC3D;AACM,IAAU,UAAU,KAAK,MAAM,GAAG,OAAO,CAAA;IAC9C,OAAO,CAAC,MAAM,QAAQ,SAAS,QAAQ,KAAK,KAAK,CAAC,QAAQ;AAC3D;AACM,IAAU,QAAQ,KAAK,MAAM,GAAG,OAAO,CAAA;IAC5C,OAAO,IAAI,IAAI,MAAM;AACtB;AACM,IAAU,UACf,SAAS,IAAI,OAAO,CAAC,EACrB,aAAa,IAAI,MAAM,CAAC,EACxB,OAAO,MAAM,EACb,sBAAsB,EACtB,iBAAO,GAAG,EACV,WAAW,8BAA8B,IAAI,EAAA;;IAE7C,IAAI,OAAO;IACX,IAAI,OAAO,KAAK,IAAI;IACpB,IAAI,QAAQ,IAAI;IAChB,IAAI,UAAU,KAAK,OAAO;IAC1B,IAAI,QAAQ,KAAK;IAEjB,IAAI,oBAAO,UAAS,UAAU;QAC7B,IAAI,CAAC,MAAK,EAAA,CAAI,MAAM,KAAK;YAAI,QAAQ,IAAI;;WACnC,IAAI,SAAM,OAAO,CAAC,QAAQ;QAChC,IAAI,CAAC,MAAK,EAAA,UAAI,GAAG,CAAE,EAAE,MAAM,IAAI,CAAC;YAAE,QAAQ,IAAI;;;IAI/C,IAAI,KAAK,SAAS,IAAI,IAAI,EAAE;QAC3B,QAAQ,KAAK,SAAS,GAAE;;IAIzB,IAAI,KAAK,KAAK,IAAI,IAAI,EAAE;QACvB,IAAI,MAAM,KAAK,KAAK,GAAE;QACtB,QAAQ,IAAI,IAAI,MAAM;QACtB,UAAU,IAAA;YAAQ;;YAAU;;;IAI7B,IAAI,KAAK,OAAO,IAAI,IAAI,IAAI,oBAAO,UAAS,UAAU;QACrD,IAAI,CAAC,CAAC,KAAK,OAAO,CAAA,EAAA,CAAI,SAAM,EAAE,IAAI,CAAC,MAAK,EAAA,CAAI,MAAM;YAAG,QAAQ,KAAK;;;IAInE,IAAI,KAAK,QAAQ,IAAI,IAAI,EAAE;QAG1B,IAAI,oBAAO,UAAS,UAAU;YAC7B,IAAI,QAAQ,MAAK,EAAA,CAAI,MAAM;gBAAG,QAAQ,KAAK;;;;IAI7C,IAAI,QAAQ,IAAI,EAAE;QACjB,IAAS,MAAM,MAAM,MAAM,EAAA;YAC1B,IAAI,oBAAO,UAAS,MAAM;gBACzB,QAAQ,KAAK;;QAEf;QACA,MAAQ;YACF;gBACJ,MAAM;YAEF;gBACJ,IAAI,CAAC,SAAS,QAAQ;oBACrB,QAAQ,KAAK;;YAGV;gBACJ,MAAM;YAEF;gBACJ,MAAM;YAEF;gBACJ,MAAM;YAEF;gBACJ,IAAI,SAAS,QAAQ;oBACpB,IAAI,CAAC,UAAU,MAAK,EAAA,CAAI,MAAM,GAAG;wBAChC,QAAQ,KAAK;;uBAER;oBACN,QAAQ,KAAK;;YAGV;gBACJ,IAAI,SAAS,QAAQ;oBACpB,IAAI,CAAC,QAAQ,MAAK,EAAA,CAAI,MAAM,GAAG;wBAC9B,QAAQ,KAAK;;uBAER;oBACN,QAAQ,KAAK;;YAGV;gBACJ,IACC,KAAK,MAAI,IAAI,IAAI,IACjB,CAAC,CAAC,KAAK,MAAI,CAAA,EAAA,UAAI,GAAG,CAAE,EAAE,QAAQ,CAAC,QAC9B;oBACD,QAAQ,KAAK;;YAGV;gBACJ,IAAI,CAAC,SAAS,CAAC,MAAM,MAAK,EAAA,CAAI,MAAM;oBAAG,QAAQ,KAAK;;YAEhD;gBACJ,IAAI,CAAC,SAAS,CAAC,QAAQ,MAAK,EAAA,CAAI,MAAM;oBAAG,QAAQ,KAAK;;YAElD;gBACJ,IAAI,CAAC,SAAS,CAAC,SAAS,MAAK,EAAA,CAAI,MAAM;oBAAG,QAAQ,KAAK;;YAEnD;gBACJ,IAAI,CAAC,SAAS,CAAC,QAAQ,MAAK,EAAA,CAAI,MAAM;oBAAG,QAAQ,KAAK;;;;IAIzD,IAAI,oBAAO,UAAS,UAAU;QAC7B,IACC,KAAK,GAAG,IAAI,IAAI,IAChB,CAAC,MAAK,EAAA,CAAI,MAAM,EAAE,MAAM,GAAG,CAAC,KAAK,GAAG,CAAA,EAAA,CAAI,MAAM,GAC7C;YACD,QAAQ,KAAK;;QAEd,IACC,KAAK,GAAG,IAAI,IAAI,IAChB,CAAC,MAAK,EAAA,CAAI,MAAM,EAAE,MAAM,GAAG,CAAC,KAAK,GAAG,CAAA,EAAA,CAAI,MAAM,GAC7C;YACD,QAAQ,KAAK;;QAId,IAAI,KAAK,GAAG,IAAI,IAAI,EAAE;YACrB,IAAI,CAAC,MAAK,EAAA,CAAI,MAAM,EAAE,MAAM,IAAI,CAAC,KAAK,GAAG,CAAA,EAAA,CAAI,MAAM,GAAG;gBACrD,QAAQ,KAAK;mBACP;gBACN,QAAQ,IAAI;;;;IAIf,IAAI,SAAS,QAAQ;QACpB,IAAI,KAAK,GAAG,IAAI,IAAI,IAAI,CAAC,MAAK,EAAA,CAAI,MAAM,IAAI,CAAC,KAAK,GAAG,CAAA,EAAA,CAAI,MAAM,GAAG;YACjE,QAAQ,KAAK;;QAEd,IAAI,KAAK,GAAG,IAAI,IAAI,IAAI,CAAC,MAAK,EAAA,CAAI,MAAM,IAAI,CAAC,KAAK,GAAG,CAAA,EAAA,CAAI,MAAM,GAAG;YACjE,QAAQ,KAAK;;;IAIf,QAAQ,KAAK,GAAG;IAChB,YAAY,KAAK,GAAG,WAAW;IAE/B,8BACC,UAAA,SACA,QAAA,OACA,QAAA;AAEF;AACM,IAAU,WACf,UAAU,uBAAuB,EACjC,sBAAa,MAAM,CAAE,GACnB,yBAA8B;QAChC;QAAK,IAAI,YAAI,CAAC;QAAd,MAAgB,IAAI,YAAY,MAAM;YACrC,IAAI,SAAS,SAAS,SAAO;YAK7B,IAAI,OAAO,QAAQ,YAAU;YAE7B,MAAO,UAAU,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,WAAW,CAAC,EAAE,IAAI,IAAI,EAAG;gBAClE,SAAS,OAAO,SAAO;gBACvB,IAAI,UAAU,IAAI,EAAE;oBACnB,OAAO,OAAO,UAAQ,CAAC,IAAI;;;YAG7B,IAAI,UAAU,IAAI,EAAE;gBACnB,OAAO,OAAM,EAAA,CAAI;;YAfqB;;;IAmBxC,OAAO,IAAI;AACZ;;;6DDzXA,EAAE;;;;;;;;AEA2B,WAAjB;IACV;oBAAO,MAAM,CAAC;IACd;mBAAM,MAAM,CAAC;;;;;;;;;0CAFH,6BAAA;;;;;qHACV,gBAAA,OACA,eAAA;;;;;;;;;iBADA,OAAO,MAAM;;8DAAb;;;;;;mCAAA;oBAAA;;;iBACA,MAAM,MAAM;;6DAAZ;;;;;;mCAAA;oBAAA;;;;UEsLS;QACT,SAAS,MAAM;QACf;QACA,MAAO,MAAM;QACb,WAAY,MAAM;QAClB,UAAW,MAAM;;;;8CJ7LnB,EAAE;;;;;;;;;;8CAAF,EAAE;;;;;;;;AKGI,IAAU,aAAS,cAAA;IACvB,IAAM,MAAM;IACZ,qBAAO;;YACL;YAAA;;;AAEJ;AACM,IAAU,KAAK,KAAK,IAAI,EAAA;IAC1B;IACA;IACA,CAAC,WAAW,CAAC,MAAM,CAAA,EAAA,CAAI,MAAM,EAAE,KAAK,CAAC,KAAK;AAC9C;AAEM,WAAO,eAAqB,GAAG,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS;IACjE,aAAS,MAAM,MAAM,GAAG,MAAM;IAC9B,aAAS,OAAO,MAAM,GAAG,gBAAgB;IACzC,aAAS,aAAa,MAAM,GAAG,OAAO;IACtC,aAAS,aAAa,MAAM,GAAG,KAAK;IACpC,aAAS,oBAAoB,MAAM,GAAG,MAAM;IAE5C,gBAAgB,KAAK,GAArB,CAAwB;;AAK5B,IAAS,mBAAgB;IACzB,YAAY,IAAI,cAAG,OAAM,qBAAqB,qCAAoC,mBAAQ,SAAQ,IAAI,GAAmB,QAAO,SAAW,4BAAyB,MAAO,qBAAkB;IAC7L,YAAY,IAAI,cAAG,OAAM,qBAAqB,qCAAoC,mBAAQ,SAAQ,KAAK,GAAmB,QAAO,SAAW,4BAAyB;AACrK;AAEA,IAAM,iBAAiB,IAAI,MAAM,EAAE,GAAG,KAAW,SAAW,SAAM,qBAAsB,WAAQ,SAAW,4BAAyB,MAAO,qBAAkB;AAC7J,IAAS,kBAAe;IACtB,YAAY,aAAa,GAAG;IAC5B,YAAY,WAAW,GAAG,SAAW,qBAAkB,YAAa,qBAAkB,WAAY,wBAAqB,WAAY,2BAAwB,WAAY,qBAAkB;IACzL,YAAY,eAAe,GAAG,OAAG,IAAI,MAAM,EAAE,GAAG;eAAa,IAAI;;IACjE,YAAY,MAAM,GAAG,YAAY,eAAe;IAChD,YAAY,YAAY,GAAG;IAC3B,YAAY,WAAW,GAAG;IAE1B,YAAY,KAAK,GAAG,IAAI;AAC1B;;;;8BL1CA,EAAE;;;;8BAAF,EAAE;;;;uBAAF,EAAE"}