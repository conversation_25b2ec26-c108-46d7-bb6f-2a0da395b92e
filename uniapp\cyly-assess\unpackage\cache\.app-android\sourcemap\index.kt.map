{"version": 3, "sources": ["D:/HBuilderX/plugins/uniapp-cli-vite/node_modules/@dcloudio/uni-console/src/runtime/app/socket.ts", "App.uvue", "components/firstui/fui-status-bar/fui-status-bar.uvue", "D:/HBuilderX/plugins/uniapp-cli-vite/node_modules/@dcloudio/uni-console/src/runtime/app/index.ts", "components/firstui/fui-types/index.uts", "components/uc/types/index.uts", "main.uts", "components/uc/utils/index.uts", "pages/login/types.uts"], "sourcesContent": ["/// <reference types=\"@dcloudio/uni-app-x/types/uni/global\" />\n// 之所以又写了一份，是因为外层的socket，connectSocket的时候必须传入multiple:true\n// 但是android又不能传入，目前代码里又不能写条件编译之类的。\nexport function initRuntimeSocket(\n  hosts: string,\n  port: string,\n  id: string\n): Promise<SocketTask | null> {\n  if (hosts == '' || port == '' || id == '') return Promise.resolve(null)\n  return hosts\n    .split(',')\n    .reduce<Promise<SocketTask | null>>(\n      (\n        promise: Promise<SocketTask | null>,\n        host: string\n      ): Promise<SocketTask | null> => {\n        return promise.then((socket): Promise<SocketTask | null> => {\n          if (socket != null) return Promise.resolve(socket)\n          return tryConnectSocket(host, port, id)\n        })\n      },\n      Promise.resolve(null)\n    )\n}\n\nconst SOCKET_TIMEOUT = 500\nfunction tryConnectSocket(\n  host: string,\n  port: string,\n  id: string\n): Promise<SocketTask | null> {\n  return new Promise((resolve, reject) => {\n    const socket = uni.connectSocket({\n      url: `ws://${host}:${port}/${id}`,\n      fail() {\n        resolve(null)\n      },\n    })\n    const timer = setTimeout(() => {\n      // @ts-expect-error\n      socket.close({\n        code: 1006,\n        reason: 'connect timeout',\n      } as CloseSocketOptions)\n      resolve(null)\n    }, SOCKET_TIMEOUT)\n\n    socket.onOpen((e) => {\n      clearTimeout(timer)\n      resolve(socket)\n    })\n    socket.onClose((e) => {\n      clearTimeout(timer)\n      resolve(null)\n    })\n    socket.onError((e) => {\n      clearTimeout(timer)\n      resolve(null)\n    })\n  })\n}\n", "<script lang=\"uts\">\n  let firstBackTime = 0\n  export default {\n\n\n\n\n\n\n\n\n\n\n    onLaunch: function (options) {\n      console.log('App Launch')\n    },\n    onShow: function (options) {\n      console.log('App Show')\n    },\n    onHide: function () {\n      console.log('App Hide')\n    },\n\n    onLastPageBackPress: function () {\n      console.log('App LastPageBackPress')\n      if (firstBackTime == 0) {\n        uni.showToast({\n          title: '再按一次退出应用',\n          position: 'bottom',\n        })\n        firstBackTime = Date.now()\n        setTimeout(() => {\n          firstBackTime = 0\n        }, 2000)\n      } else if (Date.now() - firstBackTime < 2000) {\n        firstBackTime = Date.now()\n        uni.exit()\n      }\n    },\n    onExit() {\n      console.log('App Exit')\n    },\n\n    onError: function(err: any) {\n      console.log('App Error', err)\n    },\n    methods: {\n\n\n\n\n\n\n\n\n\n\n\n    }\n  }\n</script>\n\n<style>\n/* @import './styles/common.scss';\n@import './static/css/iconfont.css'; */\n</style>\n", "<template>\r\n\t<view :style=\"{ height: statusBarHeight,zIndex:isFixed?zIndex:1,background:background }\" class=\"fui-status__bar\"\r\n\t\t:class=\"{'fui-status__bar-fixed':isFixed}\">\r\n\t\t<slot />\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tvar statusBarHeight = `${uni.getSystemInfoSync().statusBarHeight}px`\r\n\t/**\r\n\t * StatusBar 状态栏\r\n\t * @description StatusBar 状态栏，主要用于自定义导航栏时状态栏占位，高度与状态栏相等。\r\n\t * @tutorial https://unix.firstui.cn/\r\n\t * @property {String} background {String} 状态栏背景色\r\n\t * @property {Boolean} isFixed {Boolean}是否固定在顶部\r\n\t * @property {Number} zIndex {Number} z-index值，isFixed为true时生效\r\n\t * @event {Function} init 初始化时触发,返回状态栏高度，(event: string) => void\r\n\t */\r\n\texport default {\r\n\t\tname: \"fui-status-bar\",\r\n\t\temits: ['init'],\r\n\t\tprops: {\r\n\t\t\tbackground: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'transparent'\r\n\t\t\t},\r\n\t\t\tisFixed: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\tzIndex: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 99\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tstatusBarHeight: statusBarHeight as string\r\n\t\t\t};\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tthis.$emit('init', statusBarHeight)\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style scoped>\r\n\t.fui-status__bar {\r\n\t\twidth: 100%;\r\n\t\theight: 20px;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.fui-status__bar-fixed {\r\n\t\tposition: fixed;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\ttop: 0;\r\n\t}\r\n</style>", "import { initRuntimeSocket } from './socket'\n\nexport function initRuntimeSocketService(): Promise<boolean> {\n  const hosts: string = process.env.UNI_SOCKET_HOSTS\n  const port: string = process.env.UNI_SOCKET_PORT\n  const id: string = process.env.UNI_SOCKET_ID\n  if (hosts == '' || port == '' || id == '') return Promise.resolve(false)\n  let socketTask: SocketTask | null = null\n  __registerWebViewUniConsole(\n    (): string => {\n      return process.env.UNI_CONSOLE_WEBVIEW_EVAL_JS_CODE\n    },\n    (data: string) => {\n      socketTask?.send({\n        data,\n      } as SendSocketMessageOptions)\n    }\n  )\n  return Promise.resolve()\n    .then((): Promise<boolean> => {\n      return initRuntimeSocket(hosts, port, id).then((socket): boolean => {\n        if (socket == null) {\n          return false\n        }\n        socketTask = socket\n        return true\n      })\n    })\n    .catch((): boolean => {\n      return false\n    })\n}\n\ninitRuntimeSocketService()\n", "/*!\r\n* type：组件内props属性参数、事件回调参数、方法参数类型\r\n* fui-types - v1.0.0 (2023/11/11, 11:07:14 AM)\r\n*\r\n* 注意：当页面使用时，Object、Array等类型使用any接收，暂时需要转化为UTSJSONObject使用，不可直接使用“.”访问属性\r\n* @example：const param  = JSON.parse(JSON.stringify(e)) as UTSJSONObject\r\n* 在 uts 中，只适合转 type，不适合使用 interface。[interface 中使用? 编译报错，需要使用 type]\r\n*\r\n* 官网地址：https://firstui.cn/\r\n* 文档地址：https://unix.firstui.cn/\r\n*/\r\n\r\n/**\r\n* fui-text 文本组件 @onclick 事件 回调参数类型\r\n* @description this.$emit('onclick',FuiTextClickParam)\r\n* @param {string} text {string} text文本\r\n* @param {string} param {string} 自定义参数 \r\n*/\r\nexport type FuiTextClickParam = {\r\n\ttext : string;\r\n\tparam : string;\r\n}\r\n\r\n/**\r\n* fui-number 数字组件 @onclick 事件 回调参数类型\r\n* @description this.$emit('onclick',FuiNumberClickParam)\r\n* @param {string} text {string} 数字文本内容\r\n* @param {string} param {string} 自定义参数 \r\n*/\r\nexport type FuiNumberClickParam = {\r\n\ttext : string;\r\n\tparam : string;\r\n};\r\n\r\n\r\n/**\r\n* fui-footer 页脚组件 navigate 属性参数类型\r\n* @description props navigate：Arrary：FuiFooterNavigateParam[]\r\n* @param {string} text {string} 链接文本\r\n* @param {string} url {string} 当前应用内的跳转链接，可选\r\n* @param {string} openType {string} 跳转方式，可选值：navigate、redirect、switchTab、reLaunch、navigateBack等，可选\r\n* @param {number} delta {number} 返回的页面数，如果 delta 大于现有页面数，则返回到首页【仅openType=navigateBack 有效】，可选\r\n* @param {string} color {string} 链接文本字体颜色，可选\r\n* @param {number} size {number} 链接文本字体大小，单位rpx，可选\r\n*/\r\nexport type FuiFooterNavigateParam = {\r\n\ttext : string;\r\n\turl ?: string;\r\n\topenType ?: string;\r\n\tdelta ?: number;\r\n\tcolor ?: string;\r\n\tsize ?: number;\r\n}\r\n\r\n/**\r\n* fui-input-number 数字输入框组件 @change 事件 回调参数类型\r\n* @description this.$emit('change',FuiInputNumberChangeParam)\r\n* @param {number} value {number} 数字文本内容\r\n* @param {number} index {number} 索引值\r\n* @param {string} param {string} 自定义参数 \r\n*/\r\nexport type FuiInputNumberChangeParam = {\r\n\tvalue : number;\r\n\tindex : number;\r\n\tparam : string;\r\n}\r\n\r\n/**\r\n* fui-checkbox 复选框组件 @change 事件 回调参数类型（单独使用fui-checkbox组件）\r\n* @description this.$emit('change',FuiCheckboxChangeParam)\r\n* @param {boolean} checked {boolean} 是否选中\r\n* @param {string} value {string} checkbox 标识，值\r\n*/\r\nexport type FuiCheckboxChangeParam = {\r\n\tchecked : boolean;\r\n\tvalue : string;\r\n}\r\n\r\n\r\n/**\r\n* fui-actionsheet 上拉菜单组件 itemList 属性 参数类型\r\n* @description props itemList：Arrary：FuiActionSheetItemParam[]\r\n* @param {string} text {string} 菜单按钮文本\r\n* @param {string} color {string} 菜单按钮文本颜色（主题（theme）为 light 下使用），可选\r\n* @param {string} darkColor {string} 菜单按钮文本颜色（主题（theme）为 dark 下使用），可选\r\n* @param {string} param {string} 自定义参数，可选\r\n* @param {number} index {number} 按钮索引值，点击按钮时内部返回，无需传值\r\n*/\r\nexport type FuiActionSheetItemParam = {\r\n\ttext : string;\r\n\tcolor ?: string;\r\n\tdarkColor ?: string;\r\n\tparam ?: string;\r\n\tindex ?: number;\r\n}\r\n\r\n\r\n/**\r\n* fui-toast 轻提示 组件show方法 参数类型\r\n* @description Toast 轻提示组件show方法参数类型\r\n* @param {number} duration {number} 显示持续时间，单位ms，可选\r\n* @param {string} src {string} 提示图标，可选\r\n* @param {string} text {string} 提示信息，使用插槽自定义内容时可不传\r\n*/\r\nexport type FuiToastShowParam = {\r\n\tduration ?: number;\r\n\tsrc ?: string;\r\n\ttext ?: string\r\n}\r\n\r\n\r\n/**\r\n* fui-dialog 对话框 组件 buttons属性 参数类型\r\n* @description Dialog 对话框组件 buttons属性 参数类型\r\n* @param {string} text {string} 按钮文本\r\n* @param {string} color {string} 按钮字体颜色，可选\r\n* @param {boolean} primary {boolean} 按钮字体颜色是否显示为primary主题色，color为空时有效，可选\r\n* @param {string} param {string} 自定义参数，可选\r\n* @param {number} index {number} 按钮索引值，点击按钮时内部返回，无需传值\r\n*/\r\nexport type FuiDialogButtonsParam = {\r\n\ttext : string;\r\n\tcolor ?: string;\r\n\tprimary ?: boolean;\r\n\tparam ?: string;\r\n\tindex ?: number;\r\n}\r\n\r\n/**\r\n* fui-dropdown-menu 下拉菜单 组件 menus属性 参数类型\r\n* @description DropdownMenu 下拉菜单组件 menus属性 参数类型\r\n* @param {string} text {string} 下拉菜单item项显示文本，必选\r\n* @param {string} value {string} 下拉菜单item项文本对应value值，可选\r\n* @param {string} src {string} 下拉菜单item项icon图片地址，可选\r\n* @param {boolean} checked {boolean} 是否选中，可选\r\n* @param {boolean} disabled {boolean} 是否禁用选择，可选\r\n* @param {string} param {string} 自定义参数，可选\r\n* @param {number} index {number} 索引值，点击菜单时内部返回，无需传值\r\n*/\r\nexport type FuiDropdownMenuOptionParam = {\r\n\ttext : string;\r\n\tvalue ?: string;\r\n\tsrc ?: string;\r\n\tchecked ?: boolean;\r\n\tdisabled ?: boolean;\r\n\tparam ?: string;\r\n\tindex ?: number;\r\n}\r\n\r\n/**\r\n* SwipeAction 滑动菜单 组件 buttons属性 参数类型\r\n* @description SwipeAction 滑动菜单 组件 buttons属性 参数类型\r\n* @param {string} text {string} 按钮文本，必选\r\n* @param {string} background {string} 按钮背景色，不传或为空则默认使用danger主题色，可选\r\n* @param {number} size {number} 按钮字体大小，单位rpx，可选\r\n* @param {string} color {string} 按钮字体颜色，可选\r\n* @param {number} param {number} 自定义参数，可选\r\n* @param {number} index {number} 当前点击的按钮索引，点击事件返回，无需传值\r\n*/\r\nexport type FuiSwipeActionButtonParam = {\r\n\ttext : string;\r\n\tbackground ?: string;\r\n\tsize ?: number;\r\n\tcolor ?: string;\r\n\tparam ?: number;\r\n\tindex ?: number;\r\n}\r\n\r\n/**\r\n* fui-pagination 分页器 组件 @change 事件 回调参数类型\r\n* @description Pagination 分页器组件 change 事件 回调参数类型\r\n* @param {string} type {string} 按钮类型（prev、next、pageNumber）\r\n* @param {number} current {number} 当前页码\r\n*/\r\nexport type FuiPaginationChangeParam = {\r\n\ttype : string;\r\n\tcurrent : number;\r\n}\r\n\r\n/**\r\n* fui-segmented-control 分段器 组件props 属性 values 参数类型\r\n* @description props 属性 values 参数类型\r\n* @param {string} name {string} 分段器显示文本，必选\r\n* @param {boolean} disabled {boolean} 是否禁用当前项，可选\r\n* @param {number} value {number} 对应的值，可选\r\n* @param {string} param {string} 自定义参数，可选\r\n* @param {number} index {number} 当前点击项索引值，点击事件返回，无需传值，可选\r\n*/\r\nexport type FuiSegmentedControlValueParam = {\r\n\tname : string;\r\n\tdisabled ?: boolean;\r\n\tvalue ?: number;\r\n\tparam ?: string;\r\n\tindex ?: number;\r\n}\r\n\r\n/**\r\n* fui-tabs 标签页 组件props 属性 tabs 参数类型\r\n* @description props 属性 tabs（item项） 参数类型\r\n* @param {string} name {string} 标签显示文本，必选\r\n* @param {string} src {string} 标签显示图标，图标尺寸默认为 40*40，单位rpx，可选\r\n* @param {string} selectedIcon {string} 当前选项图标src，可选，不传默认使用icon\r\n* @param {number} badge {number} 角标数值，可选\r\n* @param {boolean} isDot {boolean} 角标是否显示为圆点，可选\r\n* @param {boolean} disabled {boolean} 是否禁用当前项，可选\r\n* @param {number} index {number} 当前点击项索引值，change 事件返回，无需传值，可选\r\n*/\r\nexport type FuiTabsItemParam = {\r\n\tname : string;\r\n\ticon ?: string;\r\n\tselectedIcon ?: string;\r\n\tbadge ?: number;\r\n\tisDot ?: boolean;\r\n\tdisabled ?: boolean;\r\n\tindex ?: number;\r\n}\r\n\r\n/**\r\n*  Collapse 折叠面板组件 @change 事件 回调参数类型\r\n* @description Collapse 折叠面板组件 change 事件 回调参数类型\r\n* @param {number} index {number} item项索引\r\n* @param {boolean} isOpen {boolean} 是否展开\r\n*/\r\nexport type FuiCollapseChangeParam = {\r\n\tindex : number;\r\n\tisOpen : boolean;\r\n}\r\n\r\n\r\n/**\r\n * 【fui-validator 和 fui-form 组件】 FuiFormRulesValidatorParam 表单校验规则自定义方法参数类型\r\n * @description 表单校验自定义方法参数类型\r\n * @param {String} msg {String} 错误提示内容\r\n * @param {Function} method (value : any) => boolean 校验方法\r\n */\r\nexport type FuiFormRulesValidatorParam = {\r\n\tmsg : string;\r\n\tmethod : (value : any | null) => boolean;\r\n}\r\n\r\n/**\r\n * 【fui-validator 和 fui-form 组件】FuiFormRulesParam 表单校验规则参数类型\r\n * @description 表单校验规则参数类型\r\n * @param {String} name {String} 元素的名称，与formData中key一致\r\n * @param {Array} rule {string[]} 内置的校验方法名称集合，详细见下方说明\r\n * @param {Array} msg {string[]} 对应rule规则方法的错误提示信息\r\n * @param {Array} validator {FuiFormRulesValidatorParam[]} 表单校验规则自定义方法参数类型\r\n */\r\nexport type FuiFormRulesParam = {\r\n\tname : string;\r\n\trule ?: string[];\r\n\tmsg ?: string[];\r\n\tvalidator ?: FuiFormRulesValidatorParam[]\r\n}\r\n\r\n/**\r\n * 【fui-validator 和 fui-form 组件】FuiFormErrorMsgParam 表单校验错误消息参数类型\r\n * @description 表单校验错误消息参数类型\r\n * @param {String} name {String} 元素的名称，与formData中key一致\r\n * @param {String} msg {String} 错误提示信息\r\n */\r\nexport type FuiFormErrorMsgParam = {\r\n\tname : string;\r\n\tmsg : string;\r\n}\r\n\r\n/**\r\n * 【fui-validator 和 fui-form 组件】FuiFormValidatorResParam 表单校验结果参数类型\r\n * @description 表单校验结果参数类型\r\n * @param {Boolean} isPassed {Boolean} 是否校验通过\r\n * @param {Array} errorMsg {FuiFormErrorMsgParam[]} 错误提示信息\r\n */\r\nexport type FuiFormValidatorResParam = {\r\n\tisPassed : boolean;\r\n\terrorMsg : FuiFormErrorMsgParam[];\r\n}", "export type FormItemType =\n\t| 'input'\n\t| 'textarea'\n\t| 'select'\n\t| 'radio'\n\t| 'checkbox'\n\t| 'switch'\n\t| 'date'\n\t| 'time'\n\t| 'datetime'\n\t| 'upload'\n\t| 'custom'\nexport type FormItem = {\n\tlabel: string\n\tname: string\n\ttype: FormItemType\n\tvalue?: any\n\tplaceholder?: string\n\toptions?: Array<{ label: string; value: any }>\n\trules?: Array<{ required?: boolean; message?: string; trigger?: string }>\n\tdisabled?: boolean\n\treadonly?: boolean\n\tclearable?: boolean\n\tshowWordLimit?: boolean\n\tmaxLength?: number\n\tminLength?: number\n\tstep?: number\n\tmin?: number | string\n\tmax?: number | string\n\tformat?: string\n}\nexport type FormItemData = {\n\tfield: string\n\tinstance: ComponentPublicInstance\n}\nexport type FormItemRule = {\n\t/**\n\t * 字段类型\n\t */\n\ttype?:\n\t\t| 'string'\n\t\t| 'number'\n\t\t| 'boolean'\n\t\t| 'integer'\n\t\t| 'float'\n\t\t| 'array'\n\t\t| 'object'\n\t\t| 'enum'\n\t\t| 'url'\n\t\t| 'email'\n\t\t| 'phone'\n\t\t| 'idcard'\n\t\t| null\n\t/**\n\t * 是否必填\n\t */\n\trequired?: boolean | null\n\t/**\n\t * 校验失败提示信息\n\t */\n\tmessage?: string | null\n\t/**\n\t * 正则校验规则\n\t */\n\tpattern?: RegExp | null\n\t/**\n\t * 最小长度\n\t */\n\tmin?: number | null\n\t/**\n\t * 最大长度\n\t */\n\tmax?: number | null\n\t/**\n\t * 值的长度（同时设置 min、max 和 len，以len的值为准）\n\t */\n\tlen?: number | null\n\t/**\n\t * 值的枚举值，限制值只能为此枚举数组的子项\n\t */\n\tenum?: Array<any> | null\n\t/**\n\t * 数据转换函数，校验前先执行此函数对原始数据进行处理\n\t */\n\ttransform?: ((value: any) => any) | null\n\t/**\n\t * 自定义校验函数，在默认的校验前先执行此函数。\n\t *\n\t * 返回空文本串表示校验通过；返回其他字符串表示校验失败，且返回的字符串将作为校验失败的提示信息\n\t */\n\tvalid?: ((value: any) => string) | null\n}\nexport type FormItemVerifyResult = {\n\t/**\n\t * 子项校验是否通过\n\t */\n\tvalid: boolean\n\t/**\n\t * 子项校验失败的提示信息\n\t */\n\tmessage?: string | null\n\t/**\n\t * 子项的名称\n\t */\n\tfield: string\n}\nexport type FormValidResult = {\n\t/**\n\t * 表单校验成功回调\n\t */\n\tsuccess?: (() => void) | null\n\t/**\n\t * 表单校验失败回调，会将所有校验失败的子项的错误信息作为 failResults 数组参数\n\t */\n\tfail?: ((failResults: FormItemVerifyResult[]) => void) | null\n}\nexport type FormValidResultItem = {\n\t/**\n\t * 表单校验成功回调\n\t */\n\tsuccess?: (() => void) | null\n\t/**\n\t * 表单校验失败回调，会将所有校验失败的子项的错误信息作为 failResults 数组参数\n\t */\n\tfail?: ((failResults?: FormItemVerifyResult) => void) | null\n}\n\n/**\n * Toast 提示类型\n */\nexport type ToastType = 'success' | 'error' | 'warning' | 'info' | 'default'\n\n/**\n * Toast 位置\n */\nexport type ToastPosition = 'top' | 'bottom'\n\n/**\n * Toast 配置选项\n */\nexport type ToastOptions = {\n\t/**\n\t * 消息内容\n\t */\n\tmessage: string\n\t/**\n\t * 提示类型\n\t */\n\ttype?: ToastType\n\t/**\n\t * 自定义背景色\n\t */\n\tbackgroundColor?: string\n\t/**\n\t * 自定义文字颜色\n\t */\n\ttextColor?: string\n\t/**\n\t * 图标\n\t */\n\ticon?: string\n\t/**\n\t * 是否显示图标\n\t */\n\tshowIcon?: boolean\n\t/**\n\t * 自动关闭时间（毫秒）\n\t */\n\tduration?: number\n\t/**\n\t * 位置\n\t */\n\tposition?: ToastPosition\n\t/**\n\t * 距离顶部的距离\n\t */\n\ttop?: number\n\t/**\n\t * 自定义样式\n\t */\n\tcustomStyle?: UTSJSONObject\n}\n", "import 'D:/HBuilderX/plugins/uniapp-cli-vite/node_modules/@dcloudio/uni-console/src/runtime/app/index.ts';import App from './App.uvue'\r\nimport { createSSRApp } from 'vue'\r\n\r\nexport function createApp() {\r\n  const app = createSSRApp(App)\r\n  return {\r\n    app\r\n  }\r\n}\nexport function main(app: IApp) {\n    definePageRoutes();\n    defineAppConfig();\n    (createApp()['app'] as VueApp).mount(app, GenUniApp());\n}\n\nexport class UniAppConfig extends io.dcloud.uniapp.appframe.AppConfig {\n    override name: string = \"demo\"\n    override appid: string = \"__UNI__4AABA03\"\n    override versionName: string = \"1.0.0\"\n    override versionCode: string = \"100\"\n    override uniCompilerVersion: string = \"4.66\"\n    \n    constructor() { super() }\n}\n\nimport GenPagesLoginLoginClass from './pages/login/login.uvue?type=page'\nimport GenPagesIndexIndexClass from './pages/index/index.uvue?type=page'\nfunction definePageRoutes() {\n__uniRoutes.push({ path: \"pages/login/login\", component: GenPagesLoginLoginClass, meta: { isQuit: true } as UniPageMeta, style: utsMapOf([[\"navigationBarTitleText\",\"登录\"],[\"navigationStyle\",\"custom\"]]) } as UniPageRoute)\n__uniRoutes.push({ path: \"pages/index/index\", component: GenPagesIndexIndexClass, meta: { isQuit: false } as UniPageMeta, style: utsMapOf([[\"navigationBarTitleText\",\"index\"]]) } as UniPageRoute)\n}\nconst __uniTabBar: Map<string, any | null> | null = null\nconst __uniLaunchPage: Map<string, any | null> = utsMapOf([[\"url\",\"pages/login/login\"],[\"style\",utsMapOf([[\"navigationBarTitleText\",\"登录\"],[\"navigationStyle\",\"custom\"]])]])\nfunction defineAppConfig(){\n  __uniConfig.entryPagePath = '/pages/login/login'\n  __uniConfig.globalStyle = utsMapOf([[\"pageOrientation\",\"portrait\"],[\"backgroundColor\",\"#F8F8F8\"],[\"backgroundColorTop\",\"#F4F5F6\"],[\"backgroundColorBottom\",\"#F4F5F6\"],[\"navigationStyle\",\"custom\"]])\n  __uniConfig.getTabBarConfig = ():Map<string, any> | null =>  null\n  __uniConfig.tabBar = __uniConfig.getTabBarConfig()\n  __uniConfig.conditionUrl = ''\n  __uniConfig.uniIdRouter = utsMapOf()\n  \n  __uniConfig.ready = true\n}\n", "import { FormItemVerifyResult, FormItemRule } from '../types'\n\nexport function throttle(\n\tfun: () => void,\n\twait: number,\n\tid: string = 'default'\n): void {\n\tif (wait <= 0) {\n\t\tfun()\n\t} else if (!this.flags.has(id) || !this.flags.get(id)!) {\n\t\tthis.flags.set(id, true)\n\t\tthis.timers.set(\n\t\t\tid,\n\t\t\tsetTimeout(() => {\n\t\t\t\tthis.flags.set(id, false)\n\t\t\t\tfun()\n\t\t\t}, wait)\n\t\t)\n\t}\n}\n\n/**\n * @name 防抖\n * @description 创建一个防抖函数，指定的时间运行多次，将重新计时，并只执行最后一次调用。\n * @param {Function} fun - 需要防抖的函数。\n * @param {number} wait - 需要等待的时间（毫秒）。\n * @param {string} [id='default'] - 防抖函数的唯一标识符，用于多组件区分不同的防抖实例。\n * @returns {void}\n * @example\n * // 示例用法\n * const myDebouncedFunction = () => {\n *   __f__('log','at components/uc/utils/index.uts:32','只会在停止触发后执行一次');\n * };\n * debounce(myDebouncedFunction, 300, 'uniqueId');\n */\nexport function debounce(\n\tfun: () => void,\n\twait: number,\n\tid: string = 'default'\n): void {\n\tclearTimeout(this.timers.get(id) ?? -1)\n\tif (wait <= 0) {\n\t\tfun()\n\t} else {\n\t\tthis.timers.set(\n\t\t\tid,\n\t\t\tsetTimeout(() => {\n\t\t\t\tfun()\n\t\t\t}, wait)\n\t\t)\n\t}\n}\nexport function isNumber(value: any | null): boolean {\n\n\treturn [\n\t\t'Byte',\n\t\t'UByte',\n\t\t'Short',\n\t\t'UShort',\n\t\t'Int',\n\t\t'UInt',\n\t\t'Long',\n\t\t'ULong',\n\t\t'Float',\n\t\t'Double',\n\t\t'number',\n\t].includes(typeof value)\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n}\nexport function isURL(url: string): boolean {\n\tlet reg =\n\t\t/^((https|http|ftp|rtsp|mms|ws):\\/\\/)(([0-9a-zA-Z_!~*'().&=+$%-]+: )?[0-9a-zA-Z_!~*'().&=+$%-]+@)?(([0-9]{1,3}.){3}[0-9]{1,3}|([0-9a-zA-Z_!~*'()-]+.)*([0-9a-zA-Z][0-9a-zA-Z-]{0,61})?[0-9a-zA-Z].[a-zA-Z]{2,6})(:[0-9]{1,4})?((\\/?)|(\\/[0-9a-zA-Z_!~*'().;?:@&=+$,%#-]+)+\\/?)$/\n\treturn reg.test(url)\n}\n\nexport function isEmail(email: string): boolean {\n\tconst reg = /^\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*$/g\n\treturn reg.test(email)\n}\n\n/**\n * @name 是否为手机号\n * @description 验证输入的字符串是否为有效的手机号。\n * @param {string} phone - 输入的字符串。\n * @returns {boolean} 如果输入的字符串是一个有效的手机号，返回 true，否则返回 false。\n * @example\n * // 示例用法\n * const isValidPhone = isPhone('13800138000'); // 返回 true\n * const isNotValidPhone = isPhone('not a valid phone'); // 返回 false\n */\nexport function isPhone(phone: string): boolean {\n\tconst reg = /^[1][3,4,5,6,7,8,9][0-9]{9}$/g\n\treturn reg.test(phone)\n}\n\nexport function isIDCard(code: string): boolean {\n\tlet cities = [\n\t\t'11',\n\t\t'12',\n\t\t'13',\n\t\t'14',\n\t\t'15',\n\t\t'21',\n\t\t'22',\n\t\t'23',\n\t\t'31',\n\t\t'32',\n\t\t'33',\n\t\t'34',\n\t\t'35',\n\t\t'36',\n\t\t'37',\n\t\t'41',\n\t\t'42',\n\t\t'43',\n\t\t'44',\n\t\t'45',\n\t\t'46',\n\t\t'50',\n\t\t'51',\n\t\t'52',\n\t\t'53',\n\t\t'54',\n\t\t'61',\n\t\t'62',\n\t\t'63',\n\t\t'64',\n\t\t'65',\n\t\t'71',\n\t\t'81',\n\t\t'82',\n\t\t'91',\n\t]\n\tlet case1 = code.length != 18\n\tlet case2 =\n\t\tcode.trim() == '' ||\n\t\t!/^\\d{6}(18|19|20)?\\d{2}(0[1-9]|1[012])(0[1-9]|[12]\\d|3[01])\\d{3}(\\d|X)$/i.test(\n\t\t\tcode\n\t\t)\n\tlet case3 = !cities.includes(code.slice(0, 2))\n\tif (case1 || case2 || case3) return false\n\n\t//验证最后一位校验位\n\tif (code.length == 18) {\n\t\tlet _code = code.split('')\n\t\t//∑(ai×Wi)(mod 11)\n\t\t//加权因子\n\t\tlet factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]\n\t\t//校验位\n\t\tlet parity = ['1', '0', 'X', '9', '8,', '7', '6', '5', '4', '3', '2']\n\t\tlet sum = 0\n\t\tlet ai = 0\n\t\tlet wi = 0\n\t\tfor (let i = 0; i < 17; i++) {\n\t\t\tai = parseInt(_code[i])\n\t\t\twi = factor[i]\n\t\t\tsum += ai * wi\n\t\t}\n\n\t\tif (parity[sum % 11] != _code[17]) {\n\t\t\treturn false\n\t\t}\n\t}\n\treturn true\n}\nexport function isImage(filename: string): boolean {\n\tconst reg =\n\t\t/^(?:[^\\s]+\\/)*[^\\s]+\\.(bmp|jpg|jpeg|ico|jfif|dpg|png|tif|gif|pcx|tga|exif|fpx|svg|psd|cdr|pcd|dxf|ufo|eps|ai|raw|wmf|webp|avif|apng)$/i\n\treturn reg.test(filename)\n}\nexport function isFloat(num: number): boolean {\n\treturn !isNaN(num) && isFinite(num) && Math.trunc(num) != num\n}\nexport function isInteger(num: number): boolean {\n\treturn !isNaN(num) && isFinite(num) && Math.trunc(num) == num\n}\nexport function isEmpty(str: string): boolean {\n\treturn str.trim() == ''\n}\nexport function useVerify(\n\tisValid: Ref<boolean>,\n\thintMessage: Ref<string>,\n\tfield: string,\n\titemRule: FormItemRule,\n\tvalue: any,\n\tcallback: (res: FormItemVerifyResult) => void\n) {\n\tlet rule = itemRule\n\tlet type = rule.type\n\tlet valid = true\n\tlet message = rule.message\n\tlet empty = false\n\n\tif (typeof value == 'string') {\n\t\tif ((value as string) == '') empty = true\n\t} else if (Array.isArray(value)) {\n\t\tif ((value as any[]).length == 0) empty = true\n\t}\n\n\t// 验证前进行数据转换处理\n\tif (rule.transform != null) {\n\t\tvalue = rule.transform!(value)\n\t}\n\n\t// 执行自定义验证方法\n\tif (rule.valid != null) {\n\t\tlet msg = rule.valid!(value)\n\t\tvalid = msg.trim() == ''\n\t\tmessage = valid ? message : msg\n\t}\n\n\t// 验证正则表达式\n\tif (rule.pattern != null && typeof value == 'string') {\n\t\tif (!(rule.pattern as RegExp).test(value as string)) valid = false\n\t}\n\n\t// 验证必填项\n\tif (rule.required == true) {\n\t\t// 对字符串进行额外判空\n\n\t\tif (typeof value == 'string') {\n\t\t\tif (isEmpty(value as string)) valid = false\n\t\t}\n\t}\n\n\tif (type != null) {\n\t\tfunction check(type: string) {\n\t\t\tif (typeof value != type) {\n\t\t\t\tvalid = false\n\t\t\t}\n\t\t}\n\t\tswitch (type) {\n\t\t\tcase 'string':\n\t\t\t\tcheck('string')\n\t\t\t\tbreak\n\t\t\tcase 'number':\n\t\t\t\tif (!isNumber(value)) {\n\t\t\t\t\tvalid = false\n\t\t\t\t}\n\t\t\t\tbreak\n\t\t\tcase 'boolean':\n\t\t\t\tcheck('boolean')\n\t\t\t\tbreak\n\t\t\tcase 'object':\n\t\t\t\tcheck('object')\n\t\t\t\tbreak\n\t\t\tcase 'array':\n\t\t\t\tcheck('array')\n\t\t\t\tbreak\n\t\t\tcase 'integer':\n\t\t\t\tif (isNumber(value)) {\n\t\t\t\t\tif (!isInteger(value as number)) {\n\t\t\t\t\t\tvalid = false\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tvalid = false\n\t\t\t\t}\n\t\t\t\tbreak\n\t\t\tcase 'float':\n\t\t\t\tif (isNumber(value)) {\n\t\t\t\t\tif (!isFloat(value as number)) {\n\t\t\t\t\t\tvalid = false\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tvalid = false\n\t\t\t\t}\n\t\t\t\tbreak\n\t\t\tcase 'enum':\n\t\t\t\tif (\n\t\t\t\t\trule.enum != null &&\n\t\t\t\t\t!(rule.enum as any[]).includes(value)\n\t\t\t\t) {\n\t\t\t\t\tvalid = false\n\t\t\t\t}\n\t\t\t\tbreak\n\t\t\tcase 'url':\n\t\t\t\tif (!empty && !isURL(value as string)) valid = false\n\t\t\t\tbreak\n\t\t\tcase 'email':\n\t\t\t\tif (!empty && !isEmail(value as string)) valid = false\n\t\t\t\tbreak\n\t\t\tcase 'idcard':\n\t\t\t\tif (!empty && !isIDCard(value as string)) valid = false\n\t\t\t\tbreak\n\t\t\tcase 'phone':\n\t\t\t\tif (!empty && !isPhone(value as string)) valid = false\n\t\t\t\tbreak\n\t\t}\n\t}\n\tif (typeof value == 'string') {\n\t\tif (\n\t\t\trule.min != null &&\n\t\t\t(value as string).length < (rule.min as number)\n\t\t) {\n\t\t\tvalid = false\n\t\t}\n\t\tif (\n\t\t\trule.max != null &&\n\t\t\t(value as string).length > (rule.max as number)\n\t\t) {\n\t\t\tvalid = false\n\t\t}\n\n\t\t// 如果同时设置 len, max, min，以len的结果为准\n\t\tif (rule.len != null) {\n\t\t\tif ((value as string).length != (rule.len as number)) {\n\t\t\t\tvalid = false\n\t\t\t} else {\n\t\t\t\tvalid = true\n\t\t\t}\n\t\t}\n\t}\n\tif (isNumber(value)) {\n\t\tif (rule.min != null && (value as number) < (rule.min as number)) {\n\t\t\tvalid = false\n\t\t}\n\t\tif (rule.max != null && (value as number) > (rule.max as number)) {\n\t\t\tvalid = false\n\t\t}\n\t}\n\n\tisValid.value = valid\n\thintMessage.value = message ?? ''\n\n\tcallback({\n\t\tmessage,\n\t\tvalid,\n\t\tfield,\n\t})\n}\nexport function findParent(\n\tinstance: ComponentPublicInstance,\n\tparentNames: string[]\n): ComponentPublicInstance | null {\n\tfor (let i = 0; i < parentNames.length; i++) {\n\t\tlet parent = instance.$parent\n\n\n\n\n\t\tlet name = parent?.$options?.name\n\n\t\twhile (parent != null && (name == null || parentNames[i] != name)) {\n\t\t\tparent = parent.$parent\n\t\t\tif (parent != null) {\n\t\t\t\tname = parent.$options.name\n\t\t\t}\n\t\t}\n\t\tif (parent != null) {\n\t\t\treturn parent as ComponentPublicInstance\n\t\t}\n\t}\n\n\treturn null\n}\n", "export type phoneModelType = {\n  phone: string,\n  code: string,\n};\r\n\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;+BAgCuB;+BCIX;+BC5BkB;+BDkBlB;;;;;;ADvBL,IAAS,kBACd,OAAO,MAAM,EACb,MAAM,MAAM,EACZ,IAAI,MAAM,GACT,WAAQ,aAAmB;IAC5B,IAAI,SAAS,MAAM,QAAQ,MAAM,MAAM;QAAI,OAAO,WAAQ,OAAO,CAAC,IAAI;;IACtE,OAAO,MACJ,KAAK,CAAC,KACN,MAAM,CAAC,WAAQ,cACd,IACE,SAAS,WAAQ,cACjB,MAAM,MAAM,GACX,WAAQ,aAAsB;QAC/B,OAAO,QAAQ,IAAI,CAAC,IAAC,SAAS,WAAQ,aAAsB;YAC1D,IAAI,UAAU,IAAI;gBAAE,OAAO,WAAQ,OAAO,CAAC;;YAC3C,OAAO,iBAAiB,MAAM,MAAM;QACtC;;IACF;MACA,WAAQ,OAAO,CAAC,IAAI;AAE1B;AAEA,IAAM,yBAAiB,GAAG;AAC1B,IAAS,iBACP,MAAM,MAAM,EACZ,MAAM,MAAM,EACZ,IAAI,MAAM,GACT,WAAQ,aAAmB;IAC5B,OAAO,AAAI,WAAQ,IAAC,SAAS,OAAW;QACtC,IAAM,SAAS,uCACb,MAAK,AAAC,UAAO,OAAK,MAAG,OAAK,MAAG,IAC7B,OAAA,OAAO;YACL,QAAQ,IAAI;QACd;;QAEF,IAAM,QAAQ,WAAW,KAAM;YAE7B,OAAO,KAAK,oBACV,OAAM,IAAI,EACV,SAAQ;YAEV,QAAQ,IAAI;QACd;UAAG;QAEH,OAAO,MAAM,CAAC,IAAC,EAAM;YACnB,aAAa;YACb,QAAQ;QACV;;QACA,OAAO,OAAO,CAAC,IAAC,EAAM;YACpB,aAAa;YACb,QAAQ,IAAI;QACd;;QACA,OAAO,OAAO,CAAC,IAAC,EAAM;YACpB,aAAa;YACb,QAAQ,IAAI;QACd;;IACF;;AACF;AG1DO,IAAS,4BAA4B,WAAQ,OAAO,EAAE;IAC3D,IAAM,OAAO,MAAM;IACnB,IAAM,MAAM,MAAM;IAClB,IAAM,IAAI,MAAM;IAChB,IAAI,SAAS,MAAM,QAAQ,MAAM,MAAM;QAAI,OAAO,WAAQ,OAAO,CAAC,KAAK;;IACvE,IAAI,YAAY,cAAoB,IAAI;IACxC,4BACE,OAAI,MAAM,CAAI;QACZ;IACF;MACA,IAAC,MAAM,MAAM,CAAK;QAChB,YAAY,8BACV,OAAA;IAEJ;;IAEF,OAAO,WAAQ,OAAO,GACnB,IAAI,CAAC,OAAI,WAAQ,OAAO,EAAK;QAC5B,OAAO,kBAAkB,OAAO,MAAM,IAAI,IAAI,CAAC,IAAC,SAAS,OAAO,CAAI;YAClE,IAAI,UAAU,IAAI,EAAE;gBAClB,OAAO,KAAK;YACd;YACA,aAAa;YACb,OAAO,IAAI;QACb;;IACF;MACC,OAAK,CAAC,OAAI,OAAO,CAAI;QACpB,OAAO,KAAK;IACd;;AACJ;;IAEA;;AFhCE,IAAI,wBAAgB,CAAA;AACf;;iBAWO,IAAU,wBAAO,EAAA;YACzB,QAAQ,GAAG,CAAC,cAAY;QAC1B;;kBACQ,IAAU,sBAAO,EAAA;YACvB,QAAQ,GAAG,CAAC,YAAU;QACxB;;kBACQ,MAAA;YACN,QAAQ,GAAG,CAAC,YAAU;QACxB;;4BAEqB,MAAA;YACnB,QAAQ,GAAG,CAAC,yBAAuB;YACnC,IAAI,iBAAiB,CAAC,EAAE;gBACtB,+BACE,QAAO,YACP,WAAU;gBAEZ,gBAAgB,KAAK,GAAG;gBACxB,WAAW,KAAI;oBACb,gBAAgB,CAAA;gBAClB,GAAG,IAAI;mBACF,IAAI,KAAK,GAAG,KAAK,gBAAgB,IAAI,EAAE;gBAC5C,gBAAgB,KAAK,GAAG;gBACxB;;QAEJ;;eACA,MAAM;YACJ,QAAQ,GAAG,CAAC,YAAU;QACxB;;gBAES,IAAS,KAAK,GAAG,EAAA;YACxB,QAAQ,GAAG,CAAC,aAAa,KAAG;QAC9B;;;;;;;;AAcF;;;;;;;;ACnDD,IAAI,kBAAkB,KAAG,wBAAwB,eAAe,GAAA;;;;;;;;AEqC5B,WAAzB;IACX;mBAAO,MAAM,CAAC;IACd,cAAO,MAAM,SAAC;IACd,mBAAY,MAAM,SAAC;IACnB,gBAAS,MAAM,SAAC;IAChB,gBAAS,MAAM,SAAC;IAChB,eAAQ,MAAM,SAAC;;;;;;;;;kDANJ,qCAAA;;;;;6HACX,eAAA,MACA,cAAA,KACA,mBAAA,UACA,gBAAA,OACA,gBAAA,OACA,eAAA;;;;;;;;;iBALA,MAAO,MAAM;;6DAAb;;;;;;mCAAA;oBAAA;;;iBACA,KAAO,MAAM;;4DAAb;;;;;;mCAAA;oBAAA;;;iBACA,UAAY,MAAM;;iEAAlB;;;;;;mCAAA;oBAAA;;;iBACA,OAAS,MAAM;;8DAAf;;;;;;mCAAA;oBAAA;;;iBACA,OAAS,MAAM;;8DAAf;;;;;;mCAAA;oBAAA;;;iBACA,MAAQ,MAAM;;6DAAd;;;;;;mCAAA;oBAAA;;;;AAsBoC,WAAzB;IACX;sBAAU,OAAO,SAAC;IAClB;oBAAQ,MAAM,CAAC;;;;;;AAoIe,WAAnB;IACX;mBAAO,MAAM,CAAC;IACd,eAAQ,MAAM,SAAC;IACf,uBAAgB,MAAM,SAAC;IACvB,gBAAS,MAAM,SAAC;IAChB,gBAAS,OAAO,SAAC;IACjB,mBAAY,OAAO,SAAC;IACpB,gBAAS,MAAM,SAAC;;;;;;;;;4CAPL,+BAAA;;;;;uHACX,eAAA,MACA,eAAA,MACA,uBAAA,cACA,gBAAA,OACA,gBAAA,OACA,mBAAA,UACA,gBAAA;;;;;;;;;iBANA,MAAO,MAAM;;6DAAb;;;;;;mCAAA;oBAAA;;;iBACA,MAAQ,MAAM;;6DAAd;;;;;;mCAAA;oBAAA;;;iBACA,cAAgB,MAAM;;qEAAtB;;;;;;mCAAA;oBAAA;;;iBACA,OAAS,MAAM;;8DAAf;;;;;;mCAAA;oBAAA;;;iBACA,OAAS,OAAO;;8DAAhB;;;;;;mCAAA;oBAAA;;;iBACA,UAAY,OAAO;;iEAAnB;;;;;;mCAAA;oBAAA;;;iBACA,OAAS,MAAM;;8DAAf;;;;;;mCAAA;oBAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvL0B,WAAf;IACX;oBAAO,MAAM,CAAA;IACb;uBAAU,wBAAuB;;;;;;;;;wCAFtB,2BAAA;;;;;mHACX,gBAAA,OACA,mBAAA;;;;;;;;;iBADA,OAAO,MAAM;;8DAAb;;;;;;mCAAA;oBAAA;;;iBACA,UAAU;;iEAAV;;;;;;mCAAA;oBAAA;;;;AAE0B,WAAf;IAIX,eACG,MAAQ,SAYJ;IAIP,mBAAW,OAAO,SAAO;IAIzB,kBAAU,MAAM,SAAO;IAIvB,kBAAU,kBAAa;IAIvB,cAAM,MAAM,SAAO;IAInB,cAAM,MAAM,SAAO;IAInB,cAAM,MAAM,SAAO;IAInB,iBAAO,SAAM,GAAG,UAAQ;IAIxB,sBAAc,OAAO,GAAG,KAAK,GAAG,UAAQ;IAMxC,kBAAU,OAAO,GAAG,KAAK,MAAM,UAAQ;;;;;;;;;wCAvD5B,2BAAA;;;;;mHAIX,eAAA,MAiBA,mBAAA,UAIA,kBAAA,SAIA,kBAAA,SAIA,cAAA,KAIA,cAAA,KAIA,cAAA,KAIA,iBAAA,QAIA,oBAAA,WAMA,gBAAA;;;;;;;;;iBAnDA,MACG,MAAQ;;6DADX;;;;;;mCAAA;oBAAA;;;iBAiBA,UAAW,OAAO;;iEAAlB;;;;;;mCAAA;oBAAA;;;iBAIA,SAAU,MAAM;;gEAAhB;;;;;;mCAAA;oBAAA;;;iBAIA,SAAU;;gEAAV;;;;;;mCAAA;oBAAA;;;iBAIA,KAAM,MAAM;;4DAAZ;;;;;;mCAAA;oBAAA;;;iBAIA,KAAM,MAAM;;4DAAZ;;;;;;mCAAA;oBAAA;;;iBAIA,KAAM,MAAM;;4DAAZ;;;;;;mCAAA;oBAAA;;;iBAIA,QAAO,SAAM,GAAG;;6DAAhB;;;;;;mCAAA;oBAAA;;;;AAYkC,WAAvB;IAIX;oBAAO,OAAO,SAAA;IAId,kBAAU,MAAM,SAAO;IAIvB;oBAAO,MAAM,CAAA;;;;;;AAEgB,WAAlB;IAIX,yBAAiB,IAAI,UAAQ;IAI7B,iBAAS,sBAAa,0BAA2B,IAAI,UAAQ;;;;;;AAE5B,WAAtB;IAIX,yBAAiB,IAAI,UAAQ;IAI7B,iBAAS,aAAc,0BAAyB,IAAI,UAAQ;;;;;;UAMjD,YAAY,MAAS;UAKrB,gBAAgB,MAAK;;;uDCvIjC,EAAA;;;;;;;;;;qDAAA,EAAA;;;;;;;;ACoDM,IAAU,SAAS,OAAO,GAAG,CAAO,GAAG,OAAO,CAAA;IAEnD,OAAO;QACN;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACA,CAAC,QAAQ,CAAC,oBAAO;AAyBnB;AACM,IAAU,MAAM,KAAK,MAAM,GAAG,OAAO,CAAA;IAC1C,IAAI,MACH;IACD,OAAO,IAAI,IAAI,CAAC;AACjB;AAEM,IAAU,QAAQ,OAAO,MAAM,GAAG,OAAO,CAAA;IAC9C,IAAM,MAAM;IACZ,OAAO,IAAI,IAAI,CAAC;AACjB;AAYM,IAAU,QAAQ,OAAO,MAAM,GAAG,OAAO,CAAA;IAC9C,IAAM,MAAM;IACZ,OAAO,IAAI,IAAI,CAAC;AACjB;AAEM,IAAU,SAAS,MAAM,MAAM,GAAG,OAAO,CAAA;IAC9C,IAAI,SAAS;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACA;IACD,IAAI,QAAQ,KAAK,MAAM,IAAI,EAAE;IAC7B,IAAI,QACH,KAAK,IAAI,MAAM,MACf,CAAC,+FAA0E,IAAI,CAC9E;IAEF,IAAI,QAAQ,CAAC,OAAO,QAAQ,CAAC,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;IAC5C,IAAI,SAAS,SAAS;QAAO,OAAO,KAAK;;IAGzC,IAAI,KAAK,MAAM,IAAI,EAAE,EAAE;QACtB,IAAI,QAAQ,KAAK,KAAK,CAAC;QAGvB,IAAI,2BAAS;AAAC,aAAC;AAAE,aAAC;AAAE,cAAE;AAAE,aAAC;AAAE,aAAC;AAAE,aAAC;AAAE,aAAC;AAAE,aAAC;AAAE,aAAC;AAAE,aAAC;AAAE,aAAC;AAAE,aAAC;AAAE,cAAE;AAAE,aAAC;AAAE,aAAC;AAAE,aAAC;AAAE,aAAC;SAAC;QAElE,IAAI,SAAS;YAAC;YAAK;YAAK;YAAK;YAAK;YAAM;YAAK;YAAK;YAAK;YAAK;YAAK;SAAI;QACrE,IAAI,cAAM,CAAC;QACX,IAAI,aAAK,CAAC;QACV,IAAI,aAAK,CAAC;YACV;YAAK,IAAI,YAAI,CAAC;YAAd,MAAgB,IAAI,EAAE;gBACrB,KAAK,SAAS,KAAK,CAAC,EAAE;gBACtB,KAAK,MAAM,CAAC,EAAE;gBACd,OAAO,KAAK;gBAHW;;;QAMxB,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE;YAClC,OAAO,KAAK;;;IAGd,OAAO,IAAI;AACZ;AAMM,IAAU,QAAQ,KAAK,MAAM,GAAG,OAAO,CAAA;IAC5C,OAAO,CAAC,MAAM,QAAQ,SAAS,QAAQ,KAAK,KAAK,CAAC,QAAQ;AAC3D;AACM,IAAU,UAAU,KAAK,MAAM,GAAG,OAAO,CAAA;IAC9C,OAAO,CAAC,MAAM,QAAQ,SAAS,QAAQ,KAAK,KAAK,CAAC,QAAQ;AAC3D;AACM,IAAU,QAAQ,KAAK,MAAM,GAAG,OAAO,CAAA;IAC5C,OAAO,IAAI,IAAI,MAAM;AACtB;AACM,IAAU,UACf,SAAS,IAAI,OAAO,CAAC,EACrB,aAAa,IAAI,MAAM,CAAC,EACxB,OAAO,MAAM,EACb,sBAAsB,EACtB,iBAAO,GAAG,EACV,WAAW,8BAA8B,IAAI,EAAA;;IAE7C,IAAI,OAAO;IACX,IAAI,OAAO,KAAK,IAAI;IACpB,IAAI,QAAQ,IAAI;IAChB,IAAI,UAAU,KAAK,OAAO;IAC1B,IAAI,QAAQ,KAAK;IAEjB,IAAI,oBAAO,UAAS,UAAU;QAC7B,IAAI,CAAC,MAAK,EAAA,CAAI,MAAM,KAAK;YAAI,QAAQ,IAAI;;WACnC,IAAI,SAAM,OAAO,CAAC,QAAQ;QAChC,IAAI,CAAC,MAAK,EAAA,UAAI,GAAG,CAAE,EAAE,MAAM,IAAI,CAAC;YAAE,QAAQ,IAAI;;;IAI/C,IAAI,KAAK,SAAS,IAAI,IAAI,EAAE;QAC3B,QAAQ,KAAK,SAAS,GAAE;;IAIzB,IAAI,KAAK,KAAK,IAAI,IAAI,EAAE;QACvB,IAAI,MAAM,KAAK,KAAK,GAAE;QACtB,QAAQ,IAAI,IAAI,MAAM;QACtB,UAAU,IAAA;YAAQ;;YAAU;;;IAI7B,IAAI,KAAK,OAAO,IAAI,IAAI,IAAI,oBAAO,UAAS,UAAU;QACrD,IAAI,CAAC,CAAC,KAAK,OAAO,CAAA,EAAA,CAAI,SAAM,EAAE,IAAI,CAAC,MAAK,EAAA,CAAI,MAAM;YAAG,QAAQ,KAAK;;;IAInE,IAAI,KAAK,QAAQ,IAAI,IAAI,EAAE;QAG1B,IAAI,oBAAO,UAAS,UAAU;YAC7B,IAAI,QAAQ,MAAK,EAAA,CAAI,MAAM;gBAAG,QAAQ,KAAK;;;;IAI7C,IAAI,QAAQ,IAAI,EAAE;QACjB,IAAS,MAAM,MAAM,MAAM,EAAA;YAC1B,IAAI,oBAAO,UAAS,MAAM;gBACzB,QAAQ,KAAK;;QAEf;QACA,MAAQ;YACF;gBACJ,MAAM;YAEF;gBACJ,IAAI,CAAC,SAAS,QAAQ;oBACrB,QAAQ,KAAK;;YAGV;gBACJ,MAAM;YAEF;gBACJ,MAAM;YAEF;gBACJ,MAAM;YAEF;gBACJ,IAAI,SAAS,QAAQ;oBACpB,IAAI,CAAC,UAAU,MAAK,EAAA,CAAI,MAAM,GAAG;wBAChC,QAAQ,KAAK;;uBAER;oBACN,QAAQ,KAAK;;YAGV;gBACJ,IAAI,SAAS,QAAQ;oBACpB,IAAI,CAAC,QAAQ,MAAK,EAAA,CAAI,MAAM,GAAG;wBAC9B,QAAQ,KAAK;;uBAER;oBACN,QAAQ,KAAK;;YAGV;gBACJ,IACC,KAAK,MAAI,IAAI,IAAI,IACjB,CAAC,CAAC,KAAK,MAAI,CAAA,EAAA,UAAI,GAAG,CAAE,EAAE,QAAQ,CAAC,QAC9B;oBACD,QAAQ,KAAK;;YAGV;gBACJ,IAAI,CAAC,SAAS,CAAC,MAAM,MAAK,EAAA,CAAI,MAAM;oBAAG,QAAQ,KAAK;;YAEhD;gBACJ,IAAI,CAAC,SAAS,CAAC,QAAQ,MAAK,EAAA,CAAI,MAAM;oBAAG,QAAQ,KAAK;;YAElD;gBACJ,IAAI,CAAC,SAAS,CAAC,SAAS,MAAK,EAAA,CAAI,MAAM;oBAAG,QAAQ,KAAK;;YAEnD;gBACJ,IAAI,CAAC,SAAS,CAAC,QAAQ,MAAK,EAAA,CAAI,MAAM;oBAAG,QAAQ,KAAK;;;;IAIzD,IAAI,oBAAO,UAAS,UAAU;QAC7B,IACC,KAAK,GAAG,IAAI,IAAI,IAChB,CAAC,MAAK,EAAA,CAAI,MAAM,EAAE,MAAM,GAAG,CAAC,KAAK,GAAG,CAAA,EAAA,CAAI,MAAM,GAC7C;YACD,QAAQ,KAAK;;QAEd,IACC,KAAK,GAAG,IAAI,IAAI,IAChB,CAAC,MAAK,EAAA,CAAI,MAAM,EAAE,MAAM,GAAG,CAAC,KAAK,GAAG,CAAA,EAAA,CAAI,MAAM,GAC7C;YACD,QAAQ,KAAK;;QAId,IAAI,KAAK,GAAG,IAAI,IAAI,EAAE;YACrB,IAAI,CAAC,MAAK,EAAA,CAAI,MAAM,EAAE,MAAM,IAAI,CAAC,KAAK,GAAG,CAAA,EAAA,CAAI,MAAM,GAAG;gBACrD,QAAQ,KAAK;mBACP;gBACN,QAAQ,IAAI;;;;IAIf,IAAI,SAAS,QAAQ;QACpB,IAAI,KAAK,GAAG,IAAI,IAAI,IAAI,CAAC,MAAK,EAAA,CAAI,MAAM,IAAI,CAAC,KAAK,GAAG,CAAA,EAAA,CAAI,MAAM,GAAG;YACjE,QAAQ,KAAK;;QAEd,IAAI,KAAK,GAAG,IAAI,IAAI,IAAI,CAAC,MAAK,EAAA,CAAI,MAAM,IAAI,CAAC,KAAK,GAAG,CAAA,EAAA,CAAI,MAAM,GAAG;YACjE,QAAQ,KAAK;;;IAIf,QAAQ,KAAK,GAAG;IAChB,YAAY,KAAK,GAAG,WAAW;IAE/B,8BACC,UAAA,SACA,QAAA,OACA,QAAA;AAEF;AACM,IAAU,WACf,UAAU,uBAAuB,EACjC,sBAAa,MAAM,CAAE,GACnB,yBAA8B;QAChC;QAAK,IAAI,YAAI,CAAC;QAAd,MAAgB,IAAI,YAAY,MAAM;YACrC,IAAI,SAAS,SAAS,SAAO;YAK7B,IAAI,OAAO,QAAQ,YAAU;YAE7B,MAAO,UAAU,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,WAAW,CAAC,EAAE,IAAI,IAAI,EAAG;gBAClE,SAAS,OAAO,SAAO;gBACvB,IAAI,UAAU,IAAI,EAAE;oBACnB,OAAO,OAAO,UAAQ,CAAC,IAAI;;;YAG7B,IAAI,UAAU,IAAI,EAAE;gBACnB,OAAO,OAAM,EAAA,CAAI;;YAfqB;;;IAmBxC,OAAO,IAAI;AACZ;;;6DDzXA,EAAA;;;;;;;;AEA6B,WAAjB;IACV;oBAAO,MAAM,CAAC;IACd;mBAAM,MAAM,CAAC;;;;;;;;;0CAFH,6BAAA;;;;;qHACV,gBAAA,OACA,eAAA;;;;;;;;;iBADA,OAAO,MAAM;;8DAAb;;;;;;mCAAA;oBAAA;;;iBACA,MAAM,MAAM;;6DAAZ;;;;;;mCAAA;oBAAA;;;;;;8CFFF,EAAA;;;;;;;;;;8CAAA,EAAA;;;;;;;;AAGM,IAAU,aAAS,cAAA;IACvB,IAAM,MAAM;IACZ,qBAAO;;YACL;YAAA;;;AAEJ;AACM,IAAU,KAAK,KAAK,IAAI,EAAA;IAC1B;IACA;IACA,CAAC,WAAW,CAAC,MAAM,CAAA,EAAA,CAAI,MAAM,EAAE,KAAK,CAAC,KAAK;AAC9C;AAEM,WAAO,eAAqB,GAAG,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS;IACjE,aAAS,MAAM,MAAM,GAAG,MAAM;IAC9B,aAAS,OAAO,MAAM,GAAG,gBAAgB;IACzC,aAAS,aAAa,MAAM,GAAG,OAAO;IACtC,aAAS,aAAa,MAAM,GAAG,KAAK;IACpC,aAAS,oBAAoB,MAAM,GAAG,MAAM;IAE5C,gBAAgB,KAAK,GAArB,CAAwB;;AAK5B,IAAS,mBAAgB;IACzB,YAAY,IAAI,cAAG,OAAM,qBAAqB,qCAAoC,mBAAQ,SAAQ,IAAI,GAAmB,QAAO,SAAW,4BAAyB,MAAO,qBAAkB;IAC7L,YAAY,IAAI,cAAG,OAAM,qBAAqB,qCAAoC,mBAAQ,SAAQ,KAAK,GAAmB,QAAO,SAAW,4BAAyB;AACrK;AAEA,IAAM,iBAAiB,IAAI,MAAM,EAAE,GAAG,KAAW,SAAW,SAAM,qBAAsB,WAAQ,SAAW,4BAAyB,MAAO,qBAAkB;AAC7J,IAAS,kBAAe;IACtB,YAAY,aAAa,GAAG;IAC5B,YAAY,WAAW,GAAG,SAAW,qBAAkB,YAAa,qBAAkB,WAAY,wBAAqB,WAAY,2BAAwB,WAAY,qBAAkB;IACzL,YAAY,eAAe,GAAG,OAAG,IAAI,MAAM,EAAE,GAAG;eAAa,IAAI;;IACjE,YAAY,MAAM,GAAG,YAAY,eAAe;IAChD,YAAY,YAAY,GAAG;IAC3B,YAAY,WAAW,GAAG;IAE1B,YAAY,KAAK,GAAG,IAAI;AAC1B;;;;8BA1CA,EAAA;;;;8BAAA,EAAA;;;;uBAAA,EAAA"}