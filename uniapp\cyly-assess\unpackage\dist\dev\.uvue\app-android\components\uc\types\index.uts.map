{"version": 3, "file": "index.uts", "sourceRoot": "", "sources": ["components/uc/types/index.uts"], "names": [], "mappings": "AAAA,MAAM,MAAM,YAAY,GACrB,OAAO,GACP,UAAU,GACV,QAAQ,GACR,OAAO,GACP,UAAU,GACV,QAAQ,GACR,MAAM,GACN,MAAM,GACN,UAAU,GACV,QAAQ,GACR,QAAQ,CAAA;AACX,MAAM,MAAM,QAAQ,GAAG;IACtB,KAAK,EAAE,MAAM,CAAA;IACb,IAAI,EAAE,MAAM,CAAA;IACZ,IAAI,EAAE,YAAY,CAAA;IAClB,KAAK,CAAC,EAAE,GAAG,CAAA;IACX,WAAW,CAAC,EAAE,MAAM,CAAA;IACpB,OAAO,CAAC,EAAE,KAAK,CAAC;QAAE,KAAK,EAAE,MAAM,CAAC;QAAC,KAAK,EAAE,GAAG,CAAA;KAAE,CAAC,CAAA;IAC9C,KAAK,CAAC,EAAE,KAAK,CAAC;QAAE,QAAQ,CAAC,EAAE,OAAO,CAAC;QAAC,OAAO,CAAC,EAAE,MAAM,CAAC;QAAC,OAAO,CAAC,EAAE,MAAM,CAAA;KAAE,CAAC,CAAA;IACzE,QAAQ,CAAC,EAAE,OAAO,CAAA;IAClB,QAAQ,CAAC,EAAE,OAAO,CAAA;IAClB,SAAS,CAAC,EAAE,OAAO,CAAA;IACnB,aAAa,CAAC,EAAE,OAAO,CAAA;IACvB,SAAS,CAAC,EAAE,MAAM,CAAA;IAClB,SAAS,CAAC,EAAE,MAAM,CAAA;IAClB,IAAI,CAAC,EAAE,MAAM,CAAA;IACb,GAAG,CAAC,EAAE,MAAM,GAAG,MAAM,CAAA;IACrB,GAAG,CAAC,EAAE,MAAM,GAAG,MAAM,CAAA;IACrB,MAAM,CAAC,EAAE,MAAM,CAAA;CACf,CAAA;AACD,MAAM,MAAM,YAAY,GAAG;IAC1B,KAAK,EAAE,MAAM,CAAA;IACb,QAAQ,EAAE,uBAAuB,CAAA;CACjC,CAAA;AACD,MAAM,MAAM,YAAY,GAAG;IAC1B;;OAEG;IACH,IAAI,CAAC,EACF,QAAQ,GACR,QAAQ,GACR,SAAS,GACT,SAAS,GACT,OAAO,GACP,OAAO,GACP,QAAQ,GACR,MAAM,GACN,KAAK,GACL,OAAO,GACP,OAAO,GACP,QAAQ,GACR,IAAI,CAAA;IACP;;OAEG;IACH,QAAQ,CAAC,EAAE,OAAO,GAAG,IAAI,CAAA;IACzB;;OAEG;IACH,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,CAAA;IACvB;;OAEG;IACH,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,CAAA;IACvB;;OAEG;IACH,GAAG,CAAC,EAAE,MAAM,GAAG,IAAI,CAAA;IACnB;;OAEG;IACH,GAAG,CAAC,EAAE,MAAM,GAAG,IAAI,CAAA;IACnB;;OAEG;IACH,GAAG,CAAC,EAAE,MAAM,GAAG,IAAI,CAAA;IACnB;;OAEG;IACH,IAAI,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAA;IACxB;;OAEG;IACH,SAAS,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK,GAAG,CAAC,GAAG,IAAI,CAAA;IACxC;;;;OAIG;IACH,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK,MAAM,CAAC,GAAG,IAAI,CAAA;CACvC,CAAA;AACD,MAAM,MAAM,oBAAoB,GAAG;IAClC;;OAEG;IACH,KAAK,EAAE,OAAO,CAAA;IACd;;OAEG;IACH,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,CAAA;IACvB;;OAEG;IACH,KAAK,EAAE,MAAM,CAAA;CACb,CAAA;AACD,MAAM,MAAM,eAAe,GAAG;IAC7B;;OAEG;IACH,OAAO,CAAC,EAAE,CAAC,MAAM,IAAI,CAAC,GAAG,IAAI,CAAA;IAC7B;;OAEG;IACH,IAAI,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,oBAAoB,EAAE,KAAK,IAAI,CAAC,GAAG,IAAI,CAAA;CAC7D,CAAA;AACD,MAAM,MAAM,mBAAmB,GAAG;IACjC;;OAEG;IACH,OAAO,CAAC,EAAE,CAAC,MAAM,IAAI,CAAC,GAAG,IAAI,CAAA;IAC7B;;OAEG;IACH,IAAI,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC,EAAE,oBAAoB,KAAK,IAAI,CAAC,GAAG,IAAI,CAAA;CAC5D,CAAA;AAED;;GAEG;AACH,MAAM,MAAM,SAAS,GAAG,SAAS,GAAG,OAAO,GAAG,SAAS,GAAG,MAAM,GAAG,SAAS,CAAA;AAE5E;;GAEG;AACH,MAAM,MAAM,aAAa,GAAG,KAAK,GAAG,QAAQ,CAAA;AAE5C;;GAEG;AACH,MAAM,MAAM,YAAY,GAAG;IAC1B;;OAEG;IACH,OAAO,EAAE,MAAM,CAAA;IACf;;OAEG;IACH,IAAI,CAAC,EAAE,SAAS,CAAA;IAChB;;OAEG;IACH,eAAe,CAAC,EAAE,MAAM,CAAA;IACxB;;OAEG;IACH,SAAS,CAAC,EAAE,MAAM,CAAA;IAClB;;OAEG;IACH,IAAI,CAAC,EAAE,MAAM,CAAA;IACb;;OAEG;IACH,SAAS,CAAC,EAAE,MAAM,CAAA;IAClB;;OAEG;IACH,QAAQ,CAAC,EAAE,OAAO,CAAA;IAClB;;OAEG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAA;IACjB;;OAEG;IACH,QAAQ,CAAC,EAAE,aAAa,CAAA;IACxB;;OAEG;IACH,GAAG,CAAC,EAAE,MAAM,CAAA;IACZ;;OAEG;IACH,WAAW,CAAC,EAAE,aAAa,CAAA;CAC3B,CAAA", "sourcesContent": ["export type FormItemType =\n\t| 'input'\n\t| 'textarea'\n\t| 'select'\n\t| 'radio'\n\t| 'checkbox'\n\t| 'switch'\n\t| 'date'\n\t| 'time'\n\t| 'datetime'\n\t| 'upload'\n\t| 'custom'\nexport type FormItem = {\n\tlabel: string\n\tname: string\n\ttype: FormItemType\n\tvalue?: any\n\tplaceholder?: string\n\toptions?: Array<{ label: string; value: any }>\n\trules?: Array<{ required?: boolean; message?: string; trigger?: string }>\n\tdisabled?: boolean\n\treadonly?: boolean\n\tclearable?: boolean\n\tshowWordLimit?: boolean\n\tmaxLength?: number\n\tminLength?: number\n\tstep?: number\n\tmin?: number | string\n\tmax?: number | string\n\tformat?: string\n}\nexport type FormItemData = {\n\tfield: string\n\tinstance: ComponentPublicInstance\n}\nexport type FormItemRule = {\n\t/**\n\t * 字段类型\n\t */\n\ttype?:\n\t\t| 'string'\n\t\t| 'number'\n\t\t| 'boolean'\n\t\t| 'integer'\n\t\t| 'float'\n\t\t| 'array'\n\t\t| 'object'\n\t\t| 'enum'\n\t\t| 'url'\n\t\t| 'email'\n\t\t| 'phone'\n\t\t| 'idcard'\n\t\t| null\n\t/**\n\t * 是否必填\n\t */\n\trequired?: boolean | null\n\t/**\n\t * 校验失败提示信息\n\t */\n\tmessage?: string | null\n\t/**\n\t * 正则校验规则\n\t */\n\tpattern?: RegExp | null\n\t/**\n\t * 最小长度\n\t */\n\tmin?: number | null\n\t/**\n\t * 最大长度\n\t */\n\tmax?: number | null\n\t/**\n\t * 值的长度（同时设置 min、max 和 len，以len的值为准）\n\t */\n\tlen?: number | null\n\t/**\n\t * 值的枚举值，限制值只能为此枚举数组的子项\n\t */\n\tenum?: Array<any> | null\n\t/**\n\t * 数据转换函数，校验前先执行此函数对原始数据进行处理\n\t */\n\ttransform?: ((value: any) => any) | null\n\t/**\n\t * 自定义校验函数，在默认的校验前先执行此函数。\n\t *\n\t * 返回空文本串表示校验通过；返回其他字符串表示校验失败，且返回的字符串将作为校验失败的提示信息\n\t */\n\tvalid?: ((value: any) => string) | null\n}\nexport type FormItemVerifyResult = {\n\t/**\n\t * 子项校验是否通过\n\t */\n\tvalid: boolean\n\t/**\n\t * 子项校验失败的提示信息\n\t */\n\tmessage?: string | null\n\t/**\n\t * 子项的名称\n\t */\n\tfield: string\n}\nexport type FormValidResult = {\n\t/**\n\t * 表单校验成功回调\n\t */\n\tsuccess?: (() => void) | null\n\t/**\n\t * 表单校验失败回调，会将所有校验失败的子项的错误信息作为 failResults 数组参数\n\t */\n\tfail?: ((failResults: FormItemVerifyResult[]) => void) | null\n}\nexport type FormValidResultItem = {\n\t/**\n\t * 表单校验成功回调\n\t */\n\tsuccess?: (() => void) | null\n\t/**\n\t * 表单校验失败回调，会将所有校验失败的子项的错误信息作为 failResults 数组参数\n\t */\n\tfail?: ((failResults?: FormItemVerifyResult) => void) | null\n}\n\n/**\n * Toast 提示类型\n */\nexport type ToastType = 'success' | 'error' | 'warning' | 'info' | 'default'\n\n/**\n * Toast 位置\n */\nexport type ToastPosition = 'top' | 'bottom'\n\n/**\n * Toast 配置选项\n */\nexport type ToastOptions = {\n\t/**\n\t * 消息内容\n\t */\n\tmessage: string\n\t/**\n\t * 提示类型\n\t */\n\ttype?: ToastType\n\t/**\n\t * 自定义背景色\n\t */\n\tbackgroundColor?: string\n\t/**\n\t * 自定义文字颜色\n\t */\n\ttextColor?: string\n\t/**\n\t * 图标\n\t */\n\ticon?: string\n\t/**\n\t * 图标颜色（用于 SVG 图标）\n\t */\n\ticonColor?: string\n\t/**\n\t * 是否显示图标\n\t */\n\tshowIcon?: boolean\n\t/**\n\t * 自动关闭时间（毫秒）\n\t */\n\tduration?: number\n\t/**\n\t * 位置\n\t */\n\tposition?: ToastPosition\n\t/**\n\t * 距离顶部的距离\n\t */\n\ttop?: number\n\t/**\n\t * 自定义样式\n\t */\n\tcustomStyle?: UTSJSONObject\n}\n"]}