{"version": 3, "file": "utils.uts", "sourceRoot": "", "sources": ["uni_modules/lime-svg/components/l-svg/utils.ts"], "names": [], "mappings": "AAAA,cAAc;AAEd,OAAO,EAAE,aAAa,EAAE,MAAM,+BAA+B,CAAA;AAI7D;;;;GAIG;AACH,MAAM,UAAU,aAAa,CAAC,IAAI,EAAG,MAAM,GAAI,OAAO,CAAC,MAAM,CAAC;IAE7D,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QAetC,MAAM,GAAG,GAAG,aAAa,CAAC,IAAI,CAAC,CAAA;QAC/B,IAAG,GAAG,IAAI,IAAI,EAAE;YACf,MAAM,CAAC,MAAM,CAAC,CAAA;SACd;QACD,OAAO,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,MAAM,EAAC,EAAE,CAAC,CAAC,CAAA;IAwBjC,CAAC,CAAC,CAAA;AAEH,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,YAAY,CAAC,SAAS,EAAG,MAAM,GAAI,MAAM;IACxD,MAAM,UAAU,GAAG,kBAAkB,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IACxE,OAAO,sBAAsB,UAAU,EAAE,CAAA;AAC1C,CAAC", "sourcesContent": ["// @ts-nocheck\r\n\r\nimport { fileToDataURL } from '@/uni_modules/lime-file-utils'\r\n\r\n\r\n\r\n/**\r\n * 小程序把路径转成base64\r\n * @param {string} path \r\n * @return 表示 SVG 的 Data URL。\r\n */\r\nexport function pathToDataUrl(path : string) : Promise<string> {\r\n\t\r\n\treturn new Promise((resolve, reject) => {\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\t\tconst url = fileToDataURL(path)\r\n\t\tif(url == null) {\r\n\t\t\treject('路径错误')\r\n\t\t}\r\n\t\tresolve(url!.replace(/\\s+/g,''))\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\t})\r\n\r\n}\r\n\r\n/**\r\n * 将 SVG 字符串转换为 Data URL。\r\n * @param {string} svg - 要转换的 SVG 字符串。\r\n * @returns {string} 表示 SVG 的 Data URL。\r\n */\r\nexport function svgToDataUrl(svgString : string) : string {\r\n\tconst encodedSvg = encodeURIComponent(svgString)!.replace(/\\+/g, '%20');\r\n\treturn `data:image/svg+xml,${encodedSvg}`\r\n}"]}