{"version": 3, "sources": ["uni_modules/lime-svg/components/l-svg/l-svg.uvue"], "sourcesContent": ["<template>\r\n\r\n\t<web-view class=\"l-svg\" ref=\"webRef\" v-if=\"web\" @error=\"error\" @load=\"loaded\" @message=\"message\"\r\n\t\tsrc=\"/uni_modules/lime-svg/hybrid/html/index.html?v=21\"></web-view>\r\n\t<!-- <l-svg-x class=\"l-svg\" v-else :src=\"path\" :color=\"color\" @error=\"onError\" @load=\"onLoad\"\r\n\t\t@click=\"$emit('click')\"></l-svg-x> -->\r\n\t<native-view class=\"l-svg\" v-else v-bind=\"$attrs\" @init=\"onviewinit\" @error=\"onError\" @load=\"onLoad\"></native-view>\t\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n</template>\r\n\r\n<script setup lang=\"uts\">\r\n\t/**\r\n\t * Svg SVG组件\r\n\t * @description 用于渲染SVG路径元素，支持动态颜色和继承属性\r\n\t * <br>插件类型：LSvpComponentPublicInstance \r\n\t * @tutorial https://ext.dcloud.net.cn/plugin?name=lime-svg\r\n\t * \r\n\t * @property {string} src SVG路径\r\n\t * @property {string} color 路径颜色（默认：\"currentColor\"）\r\n\t * @property {boolean} web 是否启用Web优化模式（默认：false）\r\n\t * @property {boolean} inherit 是否继承父级SVG属性（默认：true）\r\n\t * @event {Function} load SVG路径加载完成时触发\r\n\t * @event {Function} error SVG路径加载失败时触发\r\n\t */\r\n\t\r\n\timport { LSvpProps } from './type'\r\n\r\n\r\n\r\n\r\n\timport { pathToDataUrl, svgToDataUrl } from './utils'\r\n\r\n\r\n\timport { NativeImage } from \"@/uni_modules/lime-svg\";\r\n\tlet nativeImage : NativeImage | null = null\r\n\r\n\r\n\r\n\tconst props = withDefaults(defineProps<LSvpProps>(), {\r\n\t\tsrc: '',\r\n\t\tcolor: '',\r\n\t\tweb: false,\r\n\t\tinherit: false\r\n\t})\r\n\t\r\n\tconst emit = defineEmits(['load', 'error'])\r\n\tconst path = ref(props.src)\r\n\tconst svgRef = ref<UniElement | null>(null)\r\n\r\n\r\n\r\n\r\n\r\n\t\r\n\t\r\n\tconst imageURL = ref('')\r\n\tconst formatUrl = (url : string, action : string) : string => {\r\n\t\tif (url.indexOf(`'`) > 0) return `${action}(\"${url}\")`\r\n\t\treturn `${action}('${url}')`\r\n\t}\r\n\tconst styles = computed(() : Map<string, string> => {\r\n\t\tconst style = new Map<string, string>()\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\t\tif (props.color != '') {\r\n\t\t\tstyle.set('color', props.color)\r\n\t\t}\r\n\t\treturn style\r\n\t})\r\n\r\n\t\r\n\r\n\r\n\r\n\r\n\tconst errorDetaill = new UniImageErrorEventDetail('加载失败')\r\n\tconst errorEvent = new UniImageErrorEvent('error', errorDetaill)\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\tconst onError = () => {\r\n\t\temit('error', errorEvent)\r\n\t}\r\n\tconst onLoad = (e : UniNativeViewEvent) => {\r\n\r\n\r\n\r\n\r\n\r\n\t\tconst detail = new ImageLoadEventDetail(512, 512)\r\n\t\tconst loadEvent = new UniImageLoadEvent('load', detail)\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\t\temit('load', loadEvent)\r\n\t}\r\n\t// app\r\n\r\n\tconst webRef = ref<UniWebViewElement | null>(null)\r\n\tconst setSvgSrc = () => {\r\n\t\tif (path.value != '') {\r\n\t\t\twebRef.value?.evalJS(formatUrl(path.value, 'setSrc'));\r\n\t\t}\r\n\t}\r\n\tconst setSvgColor = () => {\r\n\t\tif (props.color != '' && path.value != '') {\r\n\t\t\twebRef.value?.evalJS(`setStyle({\"--color\": \"${props.color}\"})`);\r\n\t\t}\r\n\t}\r\n\tconst error = (_ : UniWebViewErrorEvent) => {\r\n\t\temit('error', errorEvent)\r\n\t}\r\n\tconst loaded = (_ : UniWebViewLoadEvent) => {\r\n\t\twatchEffect(() => {\r\n\t\t\tif (props.src == '' || !props.web) return\r\n\t\t\tif (props.src.startsWith('<svg')) {\r\n\t\t\t\tpath.value = svgToDataUrl(props.src)\r\n\t\t\t\tsetSvgSrc()\r\n\t\t\t\tsetSvgColor()\r\n\t\t\t} else if (props.src.startsWith('/static')) {\r\n\t\t\t\tpathToDataUrl(props.src).then(res => {\r\n\t\t\t\t\tpath.value = res;\r\n\t\t\t\t\tsetSvgSrc()\r\n\t\t\t\t\tsetSvgColor()\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\temit('error', errorEvent)\r\n\t\t\t\t\tconsole.warn(\"[lime-svg]\" + props.src + JSON.stringify(err))\r\n\t\t\t\t})\r\n\t\t\t} else {\r\n\t\t\t\tpath.value = props.src\r\n\t\t\t\tsetSvgSrc()\r\n\t\t\t\tsetSvgColor()\r\n\t\t\t}\r\n\t\t})\r\n\t}\r\n\tconst message = (event : UniWebViewMessageEvent) => {\r\n\t\tconst data = UTSJSONObject.assign({}, event.detail.data[0] as UTSJSONObject);  //event.detail.data[0] as UTSJSONObject\r\n\t\tconst type = data.getString('event')\r\n\r\n\t\tconst detail = data.getJSON('data')?.getJSON('detail')\r\n\r\n\r\n\r\n\r\n\t\tif (type == 'click') {\r\n\t\t\temit('click')\r\n\t\t} else if (type == 'load') {\r\n\t\t\tconst width = detail?.getNumber('width') ?? 512\r\n\t\t\tconst height = detail?.getNumber('height') ?? 512\r\n\r\n\t\t\tconst loadDetail = new ImageLoadEventDetail(width, height)\r\n\t\t\tconst loadEvent = new UniImageLoadEvent('load', loadDetail)\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\t\t\temit(type, loadEvent)\r\n\t\t} else if (type == 'error') {\r\n\t\t\temit(type, errorEvent)\r\n\t\t}\r\n\t}\r\n\r\n\r\n\r\n\r\n\tfunction onviewinit(e : UniNativeViewInitEvent) {\r\n\t\tnativeImage = new NativeImage(e.detail.element);\r\n\t\tnativeImage?.updateSrc(path.value)\r\n\t\tnativeImage?.updateColor(props.color)\r\n\t}\r\n\tconst map = new Map<string, string>()\r\n\twatchEffect(() => {\r\n\r\n\t\t// ios uts组件使用uni.request会报错，故在这里使用\r\n\t\tif (!props.web && props.src.startsWith('http')) {\r\n\t\t\tif(map.has(props.src)) {\r\n\t\t\t\tnativeImage?.updateSrc(map.get(props.src)!)\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t\tuni.downloadFile({\r\n\t\t\t\turl: props.src,\r\n\t\t\t\tsuccess(res) {\r\n\t\t\t\t\t// path.value = res.tempFilePath\r\n\t\t\t\t\tmap.set(props.src, res.tempFilePath)\r\n\t\t\t\t\tnativeImage?.updateSrc(res.tempFilePath)\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t} else {\r\n\t\t\tpath.value = props.src;\r\n\t\t\tnativeImage?.updateSrc(props.src)\r\n\t\t}\r\n\r\n\r\n\r\n\r\n\r\n\t})\r\n\twatchEffect(() => {\r\n\t\tnativeImage?.updateColor(props.color)\r\n\t})\r\n\r\n\r\n\t\r\n\r\n\t// 小程序\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.l-svg {\r\n\t\t// align-self: flex-start;\r\n\r\n\t\twidth: 24px;\r\n\t\theight: 24px;\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\t}\r\n</style>"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AA+CC,+BAAS;+BAmLH;;;;;;;;;;;;;YAlLN,IAAI,aAAc,eAAqB,IAAI;YAI3C,IAAM,QAAQ;YAOd,IAAA,KAAA,OAAA,MAAA,EAAA,OAAA,yBAAA,GAAA,CAAA,EAAA;gBAAA,MAAA,IAAA,CAAA,QAAA;YAAA;YACA,IAAM,OAAO,IAAI,MAAM,GAAG;YAC1B,IAAM,SAAS,IAAI,aAAmB,IAAI;YAQ1C,IAAM,WAAW,IAAI;YACrB,IAAM,YAAY,IAAC,KAAM,MAAM,EAAE,QAAS,MAAM,GAAI,MAAM,CAAG;gBAC5D,IAAI,IAAI,OAAO,CAAC,OAAO,CAAC;oBAAE,OAAO,KAAG,SAAM,QAAK,MAAG;;gBAClD,OAAO,KAAG,SAAM,OAAK,MAAG;YACzB;YACA,IAAM,SAAS,SAAS,OAAK,IAAI,MAAM,EAAE,MAAM,EAAI;gBAClD,IAAM,QAAQ,AAAI,IAAI,MAAM,EAAE,MAAM;gBAapC,IAAI,MAAM,KAAK,IAAI,IAAI;oBACtB,MAAM,GAAG,CAAC,SAAS,MAAM,KAAK;;gBAE/B,OAAO;YACR;;YAOA,IAAM,eAAe,AAAI,yBAAyB;YAClD,IAAM,aAAa,AAAI,mBAAmB,SAAS;YAYnD,IAAM,UAAU,KAAK;gBACpB,KAAK,SAAS;YACf;YACA,IAAM,SAAS,IAAC,GAAI,mBAAsB;gBAMzC,IAAM,SAAS,AAAI,qBAAqB,GAAG,EAAE,GAAG;gBAChD,IAAM,YAAY,AAAI,kBAAkB,QAAQ;gBAYhD,KAAK,QAAQ;YACd;YAGA,IAAM,SAAS,IAAI,oBAA0B,IAAI;YACjD,IAAM,YAAY,KAAK;gBACtB,IAAI,KAAK,KAAK,IAAI,IAAI;oBACrB,OAAO,KAAK,EAAE,OAAO,UAAU,KAAK,KAAK,EAAE;;YAE7C;YACA,IAAM,cAAc,KAAK;gBACxB,IAAI,MAAM,KAAK,IAAI,MAAM,KAAK,KAAK,IAAI,IAAI;oBAC1C,OAAO,KAAK,EAAE,OAAO,8BAAyB,MAAM,KAAK,GAAA;;YAE3D;YACA,IAAM,QAAQ,IAAC,GAAI,qBAAwB;gBAC1C,KAAK,SAAS;YACf;YACA,IAAM,SAAS,IAAC,GAAI,oBAAuB;gBAC1C,YAAY,KAAK;oBAChB,IAAI,MAAM,GAAG,IAAI,MAAM,CAAC,MAAM,GAAG;wBAAE;;oBACnC,IAAI,MAAM,GAAG,CAAC,UAAU,CAAC,SAAS;wBACjC,KAAK,KAAK,GAAG,aAAa,MAAM,GAAG;wBACnC;wBACA;2BACM,IAAI,MAAM,GAAG,CAAC,UAAU,CAAC,YAAY;wBAC3C,cAAc,MAAM,GAAG,EAAE,IAAI,CAAC,IAAA,IAAM;4BACnC,KAAK,KAAK,GAAG;4BACb;4BACA;wBACD,GAAG,OAAK,CAAC,IAAA,IAAM;4BACd,KAAK,SAAS;4BACd,QAAQ,IAAI,CAAC,eAAe,MAAM,GAAG,GAAG,KAAK,SAAS,CAAC,MAAG;wBAC3D;2BACM;wBACN,KAAK,KAAK,GAAG,MAAM,GAAG;wBACtB;wBACA;;gBAEF;;YACD;YACA,IAAM,UAAU,IAAC,OAAQ,uBAA0B;gBAClD,IAAM,OAAO,cAAc,MAAM,CAAC,eAAE,EAAE,MAAM,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA,EAAA,CAAI;gBAC9D,IAAM,OAAO,KAAK,SAAS,CAAC;gBAE5B,IAAM,SAAS,KAAK,OAAO,CAAC,SAAS,QAAQ;gBAK7C,IAAI,QAAQ,SAAS;oBACpB,KAAK;uBACC,IAAI,QAAQ,QAAQ;oBAC1B,IAAM,QAAQ,QAAQ,UAAU,YAAY,GAAG;oBAC/C,IAAM,SAAS,QAAQ,UAAU,aAAa,GAAG;oBAEjD,IAAM,aAAa,AAAI,qBAAqB,OAAO;oBACnD,IAAM,YAAY,AAAI,kBAAkB,QAAQ;oBAYhD,KAAK,MAAM;uBACL,IAAI,QAAQ,SAAS;oBAC3B,KAAK,MAAM;;YAEb;YAKA,IAAS,kBAAW,GAAI,sBAAsB,EAAA;gBAC7C,cAAc,AAAI,YAAY,EAAE,MAAM,CAAC,OAAO;gBAC9C,aAAa,UAAU,KAAK,KAAK;gBACjC,aAAa,YAAY,MAAM,KAAK;YACrC;gBAJS;YAKT,IAAM,MAAM,AAAI,IAAI,MAAM,EAAE,MAAM;YAClC,YAAY,KAAK;gBAGhB,IAAI,CAAC,MAAM,GAAG,IAAI,MAAM,GAAG,CAAC,UAAU,CAAC,SAAS;oBAC/C,IAAG,IAAI,GAAG,CAAC,MAAM,GAAG,GAAG;wBACtB,aAAa,UAAU,IAAI,GAAG,CAAC,MAAM,GAAG;wBACxC;;oBAED,qCACC,MAAK,MAAM,GAAG,EACd,UAAA,IAAQ,GAAG,EAAA;wBAEV,IAAI,GAAG,CAAC,MAAM,GAAG,EAAE,IAAI,YAAY;wBACnC,aAAa,UAAU,IAAI,YAAY;oBACxC;uBAEK;oBACN,KAAK,KAAK,GAAG,MAAM,GAAG;oBACtB,aAAa,UAAU,MAAM,GAAG;;YAOlC;;YACA,YAAY,KAAK;gBAChB,aAAa,YAAY,MAAM,KAAK;YACrC;;;uBApPA,IAAA,OAAA,KAAA,GAAA;4DAAU,SAAK,CAAA,sBAAS,aAAI,UAAqB,SAAA,QAAe,aAAM,OAAS,YAAA,QAC9E,eAAI,wEAGL,IAAA,EAAA,GAAA;;0EAAa,SAAK,CAAA,aAAwB,UAAS,KAAI,QAAE,EAAA,SAAa,YAAK,YAAY,aAAM"}