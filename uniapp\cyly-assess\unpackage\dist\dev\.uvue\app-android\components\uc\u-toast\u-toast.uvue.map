{"version": 3, "sources": ["components/uc/u-toast/u-toast.uvue"], "names": [], "mappings": "AAsBE,OAAO,EAAE,SAAS,EAAE,aAAa,EAAE,MAAM,UAAU,CAAA;AACnD,mEAAmE;AAEnE,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QACX,MAAM,KAAK,GAAG,OA6DZ,CAAA;QAEF,WAAW;QACX,SAAA,IAAA,CAAA,KAAA,EAAA,MAAA,EAAA,GAAA,uBAAA,EAAA,KAAA,CAAA,GAAA,GAAA,IAAA,CAAA;YAAA,KAAA,CAAA,IAAA,CAAA,KAAA,EAAA,GAAA,uBAAA,CAAA,CAAA;QAAA,CAA4C;QAE5C,QAAQ;QACR,MAAM,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;QAClC,MAAM,KAAK,GAAG,GAAG,CAAC,MAAM,GAAG,IAAI,EAAE,IAAI,CAAC,CAAA;QAEtC,OAAO;QACP,MAAM,QAAQ,GAAG,QAAQ,CAAC,WAAG,EAAE;YAC7B,IAAI,KAAK,CAAC,IAAI,KAAK,EAAE;gBAAE,OAAO,MAAM,CAAA;YACpC,yBAAyB;YACzB,IAAI,KAAK,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,mIAAmI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;gBAAE,OAAO,MAAM,CAAA;YAClM,OAAO,QAAQ,CAAA;QACjB,CAAC,CAAC,CAAA;QAEF,MAAM,UAAU,GAAG,QAAQ,CAAC,WAAG,EAAE;YAC/B,MAAM,OAAO,GAAG,CAAC,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,CAAA;YACzC,IAAI,KAAK,CAAC,QAAQ,KAAK,KAAK;gBAAE,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,CAAA;YACzD,IAAI,KAAK,CAAC,QAAQ,KAAK,QAAQ;gBAAE,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAA;YAC/D,OAAO,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QAC1B,CAAC,CAAC,CAAA;QAEF,MAAM,cAAc,GAAG,QAAQ,CAAC,kBAAG,EAAE;YACnC,MAAM,KAAK,EAAE,aAAa,GAAG,EAAA,mBAAA,EAAA,IAAA,oBAAA,CAAA,OAAA,EAAA,oCAAA,EAAA,GAAA,EAAA,EAAA,CAAA,GAAE,CAAA;YAC/B,MAAM,QAAQ,GAAG,KAAK,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;YAClD,IAAI,KAAK,EAAE,QAAQ,KAAK,KAAK,EAAE;gBAC7B,KAAK,CAAC,KAAK,CAAC,GAAG,QAAQ,GAAG,KAAK,CAAA;aAChC;iBAAM,IAAI,KAAK,EAAE,QAAQ,KAAK,QAAQ,EAAE;gBACvC,KAAK,CAAC,QAAQ,CAAC,GAAG,QAAQ,GAAG,KAAK,CAAA;aACnC;YACD,OAAO,KAAK,CAAA;QACd,CAAC,CAAC,CAAA;QAEF,MAAM,UAAU,GAAG,QAAQ,CAAC,kBAAG,EAAE;YAC/B,MAAM,KAAK,EAAE,aAAa,GAAG,EAAA,mBAAA,EAAA,IAAA,oBAAA,CAAA,OAAA,EAAA,oCAAA,EAAA,GAAA,EAAA,EAAA,CAAA,GAAE,CAAA;YAC/B,IAAI,KAAK,CAAC,eAAe,IAAI,IAAI,IAAI,KAAK,CAAC,eAAe,KAAK,EAAE,EAAE;gBACjE,KAAK,CAAC,kBAAkB,CAAC,GAAG,KAAK,CAAC,eAAe,CAAA;aAClD;YACD,UAAU;YACV,KAAK,MAAM,GAAG,IAAI,KAAK,CAAC,WAAW,EAAE;gBACnC,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,CAAA;aACpC;YACD,OAAO,KAAK,CAAA;QACd,CAAC,CAAC,CAAA;QAEF,MAAM,YAAY,GAAG,QAAQ,CAAC,kBAAG,EAAE;YACjC,MAAM,KAAK,EAAE,aAAa,GAAG,EAAA,mBAAA,EAAA,IAAA,oBAAA,CAAA,OAAA,EAAA,oCAAA,EAAA,GAAA,EAAA,EAAA,CAAA,GAAE,CAAA;YAC/B,IAAI,KAAK,CAAC,SAAS,IAAI,IAAI,IAAI,KAAK,CAAC,SAAS,KAAK,EAAE,EAAE;gBACrD,KAAK,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC,SAAS,CAAA;aACjC;YACD,OAAO,KAAK,CAAA;QACd,CAAC,CAAC,CAAA;QAEF,sBAAsB;QACtB,SAAS,UAAU;YACjB,MAAM,OAAO,GAAG,KAAK,CAAC,KAAK,CAAA;YAC3B,IAAI,OAAO,IAAI,IAAI,EAAE;gBACnB,YAAY,CAAC,OAAO,CAAC,CAAA;gBACrB,KAAK,CAAC,KAAK,GAAG,IAAI,CAAA;aACnB;QACH,CAAC;QAED,SAAS,IAAI;YACX,OAAO,CAAC,KAAK,GAAG,KAAK,CAAA;YACrB,UAAU,EAAE,CAAA;YACZ,IAAI,CAAC,OAAO,CAAC,CAAA;QACf,CAAC;QAED,SAAS,IAAI;YACX,OAAO,CAAC,KAAK,GAAG,IAAI,CAAA;YACpB,IAAI,KAAK,CAAC,QAAQ,GAAG,CAAC,EAAE;gBACtB,UAAU,EAAE,CAAA;gBACZ,KAAK,CAAC,KAAK,GAAG,UAAU,CAAC,GAAG,EAAE;oBAC5B,IAAI,EAAE,CAAA;gBACR,CAAC,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAA;aACnB;QACH,CAAC;QAED,sBAAsB;QACtB,KAAK,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,MAAM,EAAE,OAAO,GAAG,IAAI,CAAC,EAAE;YACnD,IAAI,MAAM,EAAE;gBACV,IAAI,EAAE,CAAA;aACP;iBAAM;gBACL,IAAI,EAAE,CAAA;aACP;QACH,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAA;QAEvB,aAAa;QACb,WAAW,CAAC,GAAG,EAAE;YACf,UAAU,EAAE,CAAA;QACd,CAAC,CAAC,CAAA;QAEF,OAAO;QACP,QAAY,CAAC;YACX,IAAI;YACJ,IAAI;SACL,CAAC,CAAA;;mBAxLF,MAAA,CAAA,KAAA,CAAA,OAiBO,CAAA,CAAA;;oBAjBD,GAAA,EAAK,CAAA;oBAAqC,KAAK,EAAA,mBAAE;;kBACrD,EAAA;sCAAW,CAAA,MAAC,EAAA,QAAS,CAAS;wBAAa,KAAK,EAAA,cAAA,CAAE,CAAA,SAAA,EAAA,KAAU,CAAA,UAAA,CAAA,CAAA,CAAA;;;8BAE1D,CAAA,IAAA,CAAA,QAAA,CAAA;;gCAAM,GAAA,EAAK,CAAA;;8BAE6B,EAAA;qCAAtC,CAAA,QAAA,CAAA,KAAA,MAA4E;;wCAAtE,GAAA,EAAK,CAAA;kEAAmD;qCAEjB,CAAA,EAAA,eAAQ,CAAA,IAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA,UAAA,CAAA;2CAArD,CAAA,QAAA,CAAA,KAAA,QAEO;;4CAFD,GAAA,EAAK,CAAA;;0CACT,EAAA;;;;;4BAKJ,CAAA,CAAA,kBAEO,CAAA,MAAA,EAAA,IAAA,CAAA;0CADL,CAAA,MAAwE,EAAA,QAAA,CAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,CAAA,EAAA;8CAA5D,CAAA,MAAA,EAAiB,QAAA,CAAA;gCAAE,KAAK,EAAA,iBAAE;qDAAiB,CAAA,KAAA,CAAA,YAAO,CAAA,CAAA", "sourcesContent": ["<template>\n  <view class=\"u-toast-container\" v-if=\"visible\" :style=\"containerStyle\">\n    <view class=\"u-toast\" :class=\"toastClass\" :style=\"toastStyle\">\n      <!-- 图标 -->\n      <view class=\"u-toast-icon\" v-if=\"showIcon\">\n        <!-- 文字图标 -->\n        <text class=\"u-toast-icon-text\" v-if=\"iconType === 'text'\">{{ icon }}</text>\n        <!-- 自定义图标插槽 -->\n        <view class=\"u-toast-icon-custom\" v-else-if=\"iconType === 'custom'\">\n          <slot name=\"icon\"></slot>\n        </view>\n      </view>\n\n      <!-- 消息内容 -->\n      <view class=\"u-toast-content\">\n        <text class=\"u-toast-message\" :style=\"messageStyle\">{{ message }}</text>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script lang=\"uts\" setup>\n  import { ToastType, ToastPosition } from \"../types\"\n  // import lSvg from \"@/uni_modules/lime-svg/components/l-svg/l-svg\"\n\n  // Props 定义\n  const props = defineProps({\n    // 是否显示\n    visible: {\n      type: Boolean,\n      default: false\n    },\n    // 消息内容\n    message: {\n      type: String,\n      default: ''\n    },\n    // 提示类型\n    type: {\n      type: String as PropType<ToastType>,\n      default: 'info' as ToastType\n    },\n    // 自定义背景色\n    backgroundColor: {\n      type: String,\n      default: ''\n    },\n    // 自定义文字颜色\n    textColor: {\n      type: String,\n      default: ''\n    },\n    // 图标\n    icon: {\n      type: String,\n      default: ''\n    },\n    // 图标颜色（用于 SVG 图标）\n    iconColor: {\n      type: String,\n      default: ''\n    },\n    // 是否显示图标\n    showIcon: {\n      type: Boolean,\n      default: true\n    },\n    // 自动关闭时间（毫秒）\n    duration: {\n      type: Number,\n      default: 3000\n    },\n    // 位置\n    position: {\n      type: String as PropType<ToastPosition>,\n      default: 'top' as ToastPosition\n    },\n    // 距离顶部的距离\n    top: {\n      type: Number,\n      default: 0\n    },\n    // 自定义样式\n    customStyle: {\n      type: Object as PropType<UTSJSONObject>,\n      default: {} as UTSJSONObject\n    }\n  })\n\n  // Emits 定义\n  const emit = defineEmits(['close', 'click'])\n\n  // 响应式数据\n  const visible = ref(props.visible)\n  const timer = ref<number | null>(null)\n\n  // 计算属性\n  const iconType = computed(() => {\n    if (props.icon === '') return 'none'\n    // 检查是否是单个字符（emoji 或文字图标）\n    if (props.icon.length === 1 || /^[\\u{1F600}-\\u{1F64F}]|[\\u{1F300}-\\u{1F5FF}]|[\\u{1F680}-\\u{1F6FF}]|[\\u{1F1E0}-\\u{1F1FF}]|[\\u{2600}-\\u{26FF}]|[\\u{2700}-\\u{27BF}]/u.test(props.icon)) return 'text'\n    return 'custom'\n  })\n\n  const toastClass = computed(() => {\n    const classes = ['u-toast-' + props.type]\n    if (props.position === 'top') classes.push('u-toast-top')\n    if (props.position === 'bottom') classes.push('u-toast-bottom')\n    return classes.join(' ')\n  })\n\n  const containerStyle = computed(() => {\n    const style: UTSJSONObject = {}\n    const topValue = props.top != null ? props.top : 0\n    if (props?.position === 'top') {\n      style['top'] = topValue + 'rpx'\n    } else if (props?.position === 'bottom') {\n      style['bottom'] = topValue + 'rpx'\n    }\n    return style\n  })\n\n  const toastStyle = computed(() => {\n    const style: UTSJSONObject = {}\n    if (props.backgroundColor != null && props.backgroundColor !== '') {\n      style['background-color'] = props.backgroundColor\n    }\n    // 合并自定义样式\n    for (const key in props.customStyle) {\n      style[key] = props.customStyle[key]\n    }\n    return style\n  })\n\n  const messageStyle = computed(() => {\n    const style: UTSJSONObject = {}\n    if (props.textColor != null && props.textColor !== '') {\n      style['color'] = props.textColor\n    }\n    return style\n  })\n\n  // 方法 - 先声明 clearTimer\n  function clearTimer() {\n    const timerId = timer.value\n    if (timerId != null) {\n      clearTimeout(timerId)\n      timer.value = null\n    }\n  }\n\n  function hide() {\n    visible.value = false\n    clearTimer()\n    emit('close')\n  }\n\n  function show() {\n    visible.value = true\n    if (props.duration > 0) {\n      clearTimer()\n      timer.value = setTimeout(() => {\n        hide()\n      }, props.duration)\n    }\n  }\n\n  // 监听 props.visible 变化\n  watch(() => props.visible, (newVal: boolean): void => {\n    if (newVal) {\n      show()\n    } else {\n      hide()\n    }\n  }, { immediate: true })\n\n  // 组件卸载时清理定时器\n  onUnmounted(() => {\n    clearTimer()\n  })\n\n  // 暴露方法\n  defineExpose({\n    show,\n    hide\n  })\n</script>\n\n<style lang=\"scss\" scoped>\n.u-toast-container {\n  position: fixed;\n  left: 0;\n  right: 0;\n  z-index: 9999;\n  pointer-events: none;\n  display: flex;\n  justify-content: center;\n  padding: 0 32rpx;\n}\n\n.u-toast {\n  display: flex;\n  align-items: center;\n  min-height: 88rpx;\n  padding: 16rpx 24rpx;\n  border-radius: 12rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\n  backdrop-filter: blur(10rpx);\n  pointer-events: auto;\n  max-width: 100%;\n  word-wrap: break-word;\n\n  /* 动画效果 */\n  animation: toastSlideIn 0.3s ease-out;\n}\n\n.u-toast-top {\n  transform-origin: top center;\n}\n\n.u-toast-bottom {\n  transform-origin: bottom center;\n}\n\n/* 不同类型的默认样式 */\n.u-toast-success {\n  background-color: rgba(82, 196, 26, 0.9);\n  color: white;\n}\n\n.u-toast-error {\n  background-color: rgba(255, 77, 79, 0.9);\n  color: white;\n}\n\n.u-toast-warning {\n  background-color: rgba(250, 173, 20, 0.9);\n  color: white;\n}\n\n.u-toast-info {\n  background-color: rgba(24, 144, 255, 0.9);\n  color: white;\n}\n\n.u-toast-default {\n  background-color: rgba(0, 0, 0, 0.8);\n  color: white;\n}\n\n.u-toast-icon {\n  margin-right: 12rpx;\n  flex-shrink: 0;\n}\n\n.u-toast-icon-text {\n  font-size: 32rpx;\n  line-height: 1;\n}\n\n.u-toast-content {\n  flex: 1;\n  min-width: 0;\n}\n\n.u-toast-message {\n  font-size: 28rpx;\n  line-height: 1.4;\n  word-wrap: break-word;\n}\n\n/* 动画 */\n@keyframes toastSlideIn {\n  0% {\n    opacity: 0;\n    transform: translateY(-20rpx) scale(0.95);\n  }\n\n  100% {\n    opacity: 1;\n    transform: translateY(0) scale(1);\n  }\n}\n\n/* 响应式设计 */\n@media (max-width: 575px) {\n  .u-toast {\n    min-height: 80rpx;\n    padding: 14rpx 20rpx;\n  }\n\n  .u-toast-message {\n    font-size: 26rpx;\n  }\n\n  .u-toast-icon-text {\n    font-size: 28rpx;\n  }\n}\n</style>\n"]}