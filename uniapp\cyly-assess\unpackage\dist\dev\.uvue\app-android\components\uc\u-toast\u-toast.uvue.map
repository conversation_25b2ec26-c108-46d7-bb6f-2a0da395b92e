{"version": 3, "sources": ["components/uc/u-toast/u-toast.uvue"], "names": [], "mappings": "AAsBE,OAAO,EAAE,SAAS,EAAE,aAAa,EAAE,MAAM,UAAU,CAAA;AACpD,OAAO,MAAM,MAAM,sCAAsC,CAAC;AAEzD,mEAAmE;AAEnE,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QACX,MAAM,KAAK,GAAG,OA6DZ,CAAA;QAEF,WAAW;QACX,SAAA,IAAA,CAAA,KAAA,EAAA,MAAA,EAAA,GAAA,uBAAA,EAAA,KAAA,CAAA,GAAA,GAAA,IAAA,CAAA;YAAA,KAAA,CAAA,IAAA,CAAA,KAAA,EAAA,GAAA,uBAAA,CAAA,CAAA;QAAA,CAA4C;QAE5C,QAAQ;QACR,MAAM,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;QAClC,MAAM,KAAK,GAAG,GAAG,CAAC,MAAM,GAAG,IAAI,EAAE,IAAI,CAAC,CAAA;QACtC,MAAM,cAAc,GAAG,GAAG,CAAC,EAAE,CAAC,CAAA;QAC9B,MAAM,WAAW,GAAG,GAAG,CAAC,KAAK,CAAC,CAAA;QAE9B,eAAe;QACf,MAAM,SAAS,GAAG,QAAQ,CAAC,WAAG,EAAE;YAC9B,IAAI,KAAK,CAAC,IAAI,KAAK,EAAE;gBAAE,OAAO,KAAK,CAAC,IAAI,CAAA;YAExC,aAAa;YACb,QAAQ,KAAK,CAAC,IAAI,EAAE;gBAClB,KAAK,SAAS;oBACZ,OAAO,GAAG,CAAA;gBACZ,KAAK,OAAO;oBACV,OAAO,GAAG,CAAA;gBACZ,KAAK,SAAS;oBACZ,OAAO,GAAG,CAAA;gBACZ,KAAK,MAAM;oBACT,OAAO,GAAG,CAAA;gBACZ;oBACE,OAAO,GAAG,CAAA;aACb;QACH,CAAC,CAAC,CAAA;QAEF,cAAc;QACd,MAAM,QAAQ,GAAG,QAAQ,CAAC,WAAG,EAAE;YAC7B,MAAM,KAAK,GAAG,SAAS,CAAC,KAAK,CAAA;YAC7B,IAAI,KAAK,KAAK,EAAE;gBAAE,OAAO,MAAM,CAAA;YAC/B,yBAAyB;YACzB,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,IAAI,mIAAmI,CAAC,IAAI,CAAC,KAAK,CAAC;gBAAE,OAAO,MAAM,CAAA;YACvL,OAAO,QAAQ,CAAA;QACjB,CAAC,CAAC,CAAA;QAEF,MAAM,UAAU,GAAG,QAAQ,CAAC,WAAG,EAAE;YAC/B,MAAM,OAAO,GAAG,CAAC,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,CAAA;YACzC,IAAI,KAAK,CAAC,QAAQ,KAAK,KAAK;gBAAE,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,CAAA;YACzD,IAAI,KAAK,CAAC,QAAQ,KAAK,QAAQ;gBAAE,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAA;YAC/D,OAAO,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QAC1B,CAAC,CAAC,CAAA;QAEF,MAAM,cAAc,GAAG,QAAQ,CAAC,kBAAG,EAAE;YACnC,MAAM,KAAK,EAAE,aAAa,GAAG,EAAA,mBAAA,EAAA,IAAA,oBAAA,CAAA,OAAA,EAAA,oCAAA,EAAA,GAAA,EAAA,EAAA,CAAA,GAAE,CAAA;YAC/B,MAAM,QAAQ,GAAG,KAAK,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;YAClD,IAAI,KAAK,EAAE,QAAQ,KAAK,KAAK,EAAE;gBAC7B,KAAK,CAAC,KAAK,CAAC,GAAG,QAAQ,GAAG,KAAK,CAAA;aAChC;iBAAM,IAAI,KAAK,EAAE,QAAQ,KAAK,QAAQ,EAAE;gBACvC,KAAK,CAAC,QAAQ,CAAC,GAAG,QAAQ,GAAG,KAAK,CAAA;aACnC;YACD,OAAO,KAAK,CAAA;QACd,CAAC,CAAC,CAAA;QAEF,MAAM,UAAU,GAAG,QAAQ,CAAC,kBAAG,EAAE;YAC/B,MAAM,KAAK,EAAE,aAAa,GAAG,EAAA,mBAAA,EAAA,IAAA,oBAAA,CAAA,OAAA,EAAA,oCAAA,EAAA,GAAA,EAAA,EAAA,CAAA,GAAE,CAAA;YAC/B,IAAI,KAAK,CAAC,eAAe,IAAI,IAAI,IAAI,KAAK,CAAC,eAAe,KAAK,EAAE,EAAE;gBACjE,KAAK,CAAC,kBAAkB,CAAC,GAAG,KAAK,CAAC,eAAe,CAAA;aAClD;YACD,UAAU;YACV,KAAK,MAAM,GAAG,IAAI,KAAK,CAAC,WAAW,EAAE;gBACnC,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,CAAA;aACpC;YAED,OAAO,KAAK,CAAA;QACd,CAAC,CAAC,CAAA;QAEF,MAAM,YAAY,GAAG,QAAQ,CAAC,kBAAG,EAAE;YACjC,MAAM,KAAK,EAAE,aAAa,GAAG,EAAA,mBAAA,EAAA,IAAA,oBAAA,CAAA,OAAA,EAAA,oCAAA,EAAA,GAAA,EAAA,EAAA,CAAA,GAAE,CAAA;YAC/B,IAAI,KAAK,CAAC,SAAS,IAAI,IAAI,IAAI,KAAK,CAAC,SAAS,KAAK,EAAE,EAAE;gBACrD,KAAK,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC,SAAS,CAAA;aACjC;iBAAM;gBACL,gBAAgB;gBAChB,KAAK,CAAC,OAAO,CAAC,GAAG,OAAO,CAAA;aACzB;YACD,OAAO,KAAK,CAAA;QACd,CAAC,CAAC,CAAA;QAEF,gBAAgB;QAChB,SAAS,kBAAkB;YACzB,WAAW,CAAC,KAAK,GAAG,IAAI,CAAA;YAExB,SAAS;YACT,cAAc,CAAC,KAAK,GAAG,oBAAoB,CAAA;YAE3C,YAAY;YACZ,UAAU,CAAC,GAAG,EAAE;gBACd,cAAc,CAAC,KAAK,GAAG,uCAAuC,CAAA;gBAE9D,UAAU;gBACV,UAAU,CAAC,GAAG,EAAE;oBACd,cAAc,CAAC,KAAK,GAAG,EAAE,CAAA;oBACzB,WAAW,CAAC,KAAK,GAAG,KAAK,CAAA;gBAC3B,CAAC,EAAE,GAAG,CAAC,CAAA;YACT,CAAC,EAAE,EAAE,CAAC,CAAA;QACR,CAAC;QAED,SAAS,kBAAkB;YACzB,WAAW,CAAC,KAAK,GAAG,IAAI,CAAA;YAExB,SAAS;YACT,cAAc,CAAC,KAAK,GAAG,uCAAuC,CAAA;YAE9D,UAAU,CAAC,GAAG,EAAE;gBACd,cAAc,CAAC,KAAK,GAAG,EAAE,CAAA;gBACzB,WAAW,CAAC,KAAK,GAAG,KAAK,CAAA;YAC3B,CAAC,EAAE,GAAG,CAAC,CAAA;QACT,CAAC;QAED,sBAAsB;QACtB,SAAS,UAAU;YACjB,MAAM,OAAO,GAAG,KAAK,CAAC,KAAK,CAAA;YAC3B,IAAI,OAAO,IAAI,IAAI,EAAE;gBACnB,YAAY,CAAC,OAAO,CAAC,CAAA;gBACrB,KAAK,CAAC,KAAK,GAAG,IAAI,CAAA;aACnB;QACH,CAAC;QAED,SAAS,IAAI;YACX,IAAI,WAAW,CAAC,KAAK;gBAAE,OAAM;YAE7B,SAAS;YACT,kBAAkB,EAAE,CAAA;YAEpB,YAAY;YACZ,UAAU,CAAC,GAAG,EAAE;gBACd,OAAO,CAAC,KAAK,GAAG,KAAK,CAAA;gBACrB,UAAU,EAAE,CAAA;gBACZ,IAAI,CAAC,OAAO,CAAC,CAAA;YACf,CAAC,EAAE,GAAG,CAAC,CAAA;QACT,CAAC;QAED,SAAS,IAAI;YACX,IAAI,WAAW,CAAC,KAAK;gBAAE,OAAM;YAE7B,OAAO,CAAC,KAAK,GAAG,IAAI,CAAA;YAEpB,SAAS;YACT,kBAAkB,EAAE,CAAA;YAEpB,WAAW;YACX,IAAI,KAAK,CAAC,QAAQ,GAAG,CAAC,EAAE;gBACtB,UAAU,EAAE,CAAA;gBACZ,KAAK,CAAC,KAAK,GAAG,UAAU,CAAC,GAAG,EAAE;oBAC5B,IAAI,EAAE,CAAA;gBACR,CAAC,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAA;aACnB;QACH,CAAC;QAED,sBAAsB;QACtB,WAAW,CAAC,GAAG,EAAE;YACf,IAAI,KAAK,CAAC,OAAO,EAAE;gBACjB,IAAI,EAAE,CAAA;aACP;iBAAM;gBACL,IAAI,EAAE,CAAA;aACP;QACH,CAAC,CAAC,CAAA;QAEF,aAAa;QACb,WAAW,CAAC,GAAG,EAAE;YACf,UAAU,EAAE,CAAA;QACd,CAAC,CAAC,CAAA;QAEF,OAAO;QACP,QAAY,CAAC;YACX,IAAI;YACJ,IAAI;SACL,CAAC,CAAA;;mBAlQF,MAAA,CAAA,KAAA,CAAA,OAiBO,CAAA,CAAA;;oBAjBD,GAAA,EAAK,CAAA;oBAAsB,KAAK,EAAA,mBAAE;;kBACtC,EAAA;sCAAW,CAAA,MAAC,EAAA,QAAS,CAAA;wBAAwC,KAAK,EAAA,cAAA,CAAE,CAAA,SAAA,EAAA,CAAA,KAAU,CAAA,UAAA,CAAA,EAAA,KAAA,CAAA,cAAA,CAAA,CAAA,CAAA,CAAA;;;8BAE5E,CAAA,IAAA,CAAA,QAAA,CAAA;;gCAAM,GAAA,EAAK,CAAA;;8BAE6B,EAAA;qCAAtC,CAAA,QAAA,CAAA,KAAA,MAAuG;;wCAAjG,GAAA,EAAK,CAAA;wCAAgD,KAAqB,EAAA,mBAArB;6DAAyB,CAAA,QAAA,CAAA,EAAA,OAAS,EAAA,OAAA,EAAA,CAAA,CAAA;qCAEhD,CAAA,EAAA,eAAQ,CAAA,KAAA,CAAA,SAAA,CAAA,CAAA,EAAA,CAAA,CAAA,iBAAA,CAAA;2CAArD,CAAA,QAAA,CAAA,KAAA,QAEO;;4CAFD,GAAA,EAAK,CAAA;;0CACT,EAAA;;;;;4BAKJ,CAAA,CAAA,kBAEO,CAAA,MAAA,EAAA,IAAA,CAAA;0CADL,CAAA,MAAwE,EAAA,QAAA,CAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,CAAA,EAAA;8CAA5D,CAAA,MAAA,EAAiB,QAAA,CAAA;gCAAE,KAAK,EAAA,iBAAE;qDAAiB,CAAA,KAAA,CAAA,YAAO,CAAA,CAAA", "sourcesContent": ["<template>\n  <view class=\"u-toast-container\" :style=\"containerStyle\" v-if=\"visible\">\n    <view class=\"u-toast\" :class=\"[toastClass, animationClass]\" :style=\"toastStyle\">\n      <!-- 图标 -->\n      <view class=\"u-toast-icon\" v-if=\"showIcon\">\n        <!-- 文字图标 -->\n        <text class=\"u-toast-icon-text\" v-if=\"iconType === 'text'\" style=\"color: white;\">{{ iconValue }}</text>\n        <!-- 自定义图标插槽 -->\n        <view class=\"u-toast-icon-custom\" v-else-if=\"iconType === 'custom'\">\n          <slot name=\"icon\"></slot>\n        </view>\n      </view>\n\n      <!-- 消息内容 -->\n      <view class=\"u-toast-content\">\n        <text class=\"u-toast-message\" :style=\"messageStyle\">{{ message }}</text>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script lang=\"uts\" setup>\n  import { ToastType, ToastPosition } from \"../types\"\n\timport fuiRow from \"@/components/firstui/fui-row/fui-row\";\n\n  // import lSvg from \"@/uni_modules/lime-svg/components/l-svg/l-svg\"\n\n  // Props 定义\n  const props = defineProps({\n    // 是否显示\n    visible: {\n      type: Boolean,\n      default: false\n    },\n    // 消息内容\n    message: {\n      type: String,\n      default: ''\n    },\n    // 提示类型\n    type: {\n      type: String as PropType<ToastType>,\n      default: 'info' as ToastType\n    },\n    // 自定义背景色\n    backgroundColor: {\n      type: String,\n      default: ''\n    },\n    // 自定义文字颜色\n    textColor: {\n      type: String,\n      default: ''\n    },\n    // 图标\n    icon: {\n      type: String,\n      default: ''\n    },\n    // 图标颜色（用于 SVG 图标）\n    iconColor: {\n      type: String,\n      default: ''\n    },\n    // 是否显示图标\n    showIcon: {\n      type: Boolean,\n      default: true\n    },\n    // 自动关闭时间（毫秒）\n    duration: {\n      type: Number,\n      default: 3000\n    },\n    // 位置\n    position: {\n      type: String as PropType<ToastPosition>,\n      default: 'top' as ToastPosition\n    },\n    // 距离顶部的距离\n    top: {\n      type: Number,\n      default: 0\n    },\n    // 自定义样式\n    customStyle: {\n      type: Object as PropType<UTSJSONObject>,\n      default: {} as UTSJSONObject\n    }\n  })\n\n  // Emits 定义\n  const emit = defineEmits(['close', 'click'])\n\n  // 响应式数据\n  const visible = ref(props.visible)\n  const timer = ref<number | null>(null)\n  const animationClass = ref('')\n  const isAnimating = ref(false)\n\n  // 计算属性 - 获取图标值\n  const iconValue = computed(() => {\n    if (props.icon !== '') return props.icon\n\n    // 根据类型返回默认图标\n    switch (props.type) {\n      case 'success':\n        return '✓'\n      case 'error':\n        return '✕'\n      case 'warning':\n        return '⚠'\n      case 'info':\n        return 'ℹ'\n      default:\n        return '●'\n    }\n  })\n\n  // 计算属性 - 图标类型\n  const iconType = computed(() => {\n    const value = iconValue.value\n    if (value === '') return 'none'\n    // 检查是否是单个字符（emoji 或文字图标）\n    if (value.length <= 2 || /^[\\u{1F600}-\\u{1F64F}]|[\\u{1F300}-\\u{1F5FF}]|[\\u{1F680}-\\u{1F6FF}]|[\\u{1F1E0}-\\u{1F1FF}]|[\\u{2600}-\\u{26FF}]|[\\u{2700}-\\u{27BF}]/u.test(value)) return 'text'\n    return 'custom'\n  })\n\n  const toastClass = computed(() => {\n    const classes = ['u-toast-' + props.type]\n    if (props.position === 'top') classes.push('u-toast-top')\n    if (props.position === 'bottom') classes.push('u-toast-bottom')\n    return classes.join(' ')\n  })\n\n  const containerStyle = computed(() => {\n    const style: UTSJSONObject = {}\n    const topValue = props.top != null ? props.top : 0\n    if (props?.position === 'top') {\n      style['top'] = topValue + 'rpx'\n    } else if (props?.position === 'bottom') {\n      style['bottom'] = topValue + 'rpx'\n    }\n    return style\n  })\n\n  const toastStyle = computed(() => {\n    const style: UTSJSONObject = {}\n    if (props.backgroundColor != null && props.backgroundColor !== '') {\n      style['background-color'] = props.backgroundColor\n    }\n    // 合并自定义样式\n    for (const key in props.customStyle) {\n      style[key] = props.customStyle[key]\n    }\n\n    return style\n  })\n\n  const messageStyle = computed(() => {\n    const style: UTSJSONObject = {}\n    if (props.textColor != null && props.textColor !== '') {\n      style['color'] = props.textColor\n    } else {\n      // 安卓端兼容：确保文字是白色\n      style['color'] = 'white'\n    }\n    return style\n  })\n\n  // 动画方法 - 使用CSS类\n  function startShowAnimation() {\n    isAnimating.value = true\n\n    // 初始隐藏状态\n    animationClass.value = 'u-toast-enter-from'\n\n    // 下一帧开始显示动画\n    setTimeout(() => {\n      animationClass.value = 'u-toast-enter-active u-toast-enter-to'\n\n      // 动画完成后清理\n      setTimeout(() => {\n        animationClass.value = ''\n        isAnimating.value = false\n      }, 300)\n    }, 50)\n  }\n\n  function startHideAnimation() {\n    isAnimating.value = true\n\n    // 开始隐藏动画\n    animationClass.value = 'u-toast-leave-active u-toast-leave-to'\n\n    setTimeout(() => {\n      animationClass.value = ''\n      isAnimating.value = false\n    }, 200)\n  }\n\n  // 方法 - 先声明 clearTimer\n  function clearTimer() {\n    const timerId = timer.value\n    if (timerId != null) {\n      clearTimeout(timerId)\n      timer.value = null\n    }\n  }\n\n  function hide() {\n    if (isAnimating.value) return\n\n    // 播放隐藏动画\n    startHideAnimation()\n\n    // 动画完成后隐藏元素\n    setTimeout(() => {\n      visible.value = false\n      clearTimer()\n      emit('close')\n    }, 200)\n  }\n\n  function show() {\n    if (isAnimating.value) return\n\n    visible.value = true\n\n    // 启动显示动画\n    startShowAnimation()\n\n    // 添加延迟自动隐藏\n    if (props.duration > 0) {\n      clearTimer()\n      timer.value = setTimeout(() => {\n        hide()\n      }, props.duration)\n    }\n  }\n\n  // 监听 props.visible 变化\n  watchEffect(() => {\n    if (props.visible) {\n      show()\n    } else {\n      hide()\n    }\n  })\n\n  // 组件卸载时清理定时器\n  onUnmounted(() => {\n    clearTimer()\n  })\n\n  // 暴露方法\n  defineExpose({\n    show,\n    hide\n  })\n</script>\n\n<style lang=\"scss\" scoped>\n.u-toast-container {\n  position: fixed;\n  left: 0;\n  right: 0;\n  z-index: 9999;\n  pointer-events: none;\n  display: flex;\n  justify-content: center;\n  display: flex;\n  padding: 0 32rpx;\n}\n\n.u-toast {\n  display: flex;\n  align-items: center;\n  min-height: 80rpx;\n  padding: 16rpx 24rpx;\n  border-radius: 20px;\n  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);\n\n  pointer-events: auto;\n  max-width: 85%;\n  word-wrap: break-word;\n  border: 1rpx solid rgba(255, 255, 255, 0.2);\n\n\n\n  /* 添加渐变光泽效果 */\n  position: relative;\n  overflow: hidden;\n\n}\n\n\n\n.u-toast-top {\n  transform-origin: top center;\n}\n\n.u-toast-bottom {\n  transform-origin: bottom center;\n}\n\n/* 不同类型的默认样式 */\n.u-toast-success {\n  background: linear-gradient(135deg, rgba(82, 196, 26, 0.95) 0%, rgba(115, 209, 61, 0.95) 100%);\n  color: white;\n  border-color: rgba(82, 196, 26, 0.3);\n}\n\n.u-toast-error {\n  background: linear-gradient(135deg, rgba(255, 77, 79, 0.95) 0%, rgba(255, 120, 117, 0.95) 100%);\n  color: white;\n  border-color: rgba(255, 77, 79, 0.3);\n}\n\n.u-toast-warning {\n  background: linear-gradient(135deg, rgba(250, 173, 20, 0.95) 0%, rgba(255, 197, 61, 0.95) 100%);\n  color: white;\n  border-color: rgba(250, 173, 20, 0.3);\n}\n\n.u-toast-info {\n  background: linear-gradient(135deg, rgba(24, 144, 255, 0.95) 0%, rgba(64, 169, 255, 0.95) 100%);\n  color: white;\n  border-color: rgba(24, 144, 255, 0.3);\n}\n\n.u-toast-default {\n  background: linear-gradient(135deg, rgba(0, 0, 0, 0.85) 0%, rgba(64, 64, 64, 0.85) 100%);\n  color: white;\n  border-color: rgba(255, 255, 255, 0.1);\n}\n\n.u-toast-icon {\n  margin-right: 12rpx;\n  flex-shrink: 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 36rpx;\n  height: 36rpx;\n  border-radius: 18px;\n  background-color: rgba(255, 255, 255, 0.3);\n}\n\n.u-toast-icon-text {\n  font-size: 24rpx;\n  line-height: 1;\n  font-weight: bold;\n  color: white !important;\n  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);\n}\n\n.u-toast-content {\n  flex: 1;\n  min-width: 0;\n}\n\n.u-toast-message {\n  font-size: 28rpx;\n  line-height: 1.4;\n  word-wrap: break-word;\n  font-weight: 500;\n  color: white !important;\n  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);\n  letter-spacing: 0.3rpx;\n}\n\n\n\n\n\n/* 响应式设计 */\n@media (max-width: 575px) {\n  .u-toast {\n    min-height: 72rpx;\n    padding: 12rpx 20rpx;\n    border-radius: 18px;\n  }\n\n  .u-toast-icon {\n    width: 32rpx;\n    height: 32rpx;\n    margin-right: 10rpx;\n  }\n\n  .u-toast-icon-text {\n    font-size: 20rpx;\n  }\n\n  .u-toast-message {\n    font-size: 26rpx;\n  }\n}\n\n/* 安卓端兼容的动画类 */\n.u-toast-enter-from {\n  opacity: 0;\n  transform: translateY(-20px);\n}\n\n.u-toast-enter-active {\n  transition: opacity 0.3s ease-out, transform 0.3s ease-out;\n}\n\n.u-toast-enter-to {\n  opacity: 1;\n  transform: translateY(0);\n}\n\n.u-toast-leave-active {\n  transition: opacity 0.2s ease-in, transform 0.2s ease-in;\n}\n\n.u-toast-leave-to {\n  opacity: 0;\n  transform: translateY(-10px);\n}\n</style>\n"]}