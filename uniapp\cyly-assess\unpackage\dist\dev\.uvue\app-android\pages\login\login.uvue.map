{"version": 3, "sources": ["pages/login/login.uvue"], "names": [], "mappings": "AAqFC,OAAO,YAAY,MAAM,oDAAoD,CAAC;AAC9E,OAAO,SAAS,MAAM,4CAA4C,CAAC;AACnE,OAAO,MAAM,MAAM,sCAAsC,CAAC;AAC1D,OAAO,OAAO,MAAM,wCAAwC,CAAC;AAC7D,OAAO,QAAQ,MAAM,0CAA0C,CAAC;AAChE,OAAO,SAAS,MAAM,4CAA4C,CAAC;AACnE,OAAO,OAAO,MAAM,wCAAwC,CAAC;AAC7D,OAAO,WAAW,MAAM,gDAAgD,CAAC;AACzE,OAAO,EAAE,gBAAgB,EAAE,MAAM,0CAA0C,CAAC;AAC5E,OAAO,KAAK,MAAM,+BAA+B,CAAC;AAClD,OAAO,SAAS,MAAM,yCAAyC,CAAC;AAChE,OAAO,MAAM,MAAM,iCAAiC,CAAC;AACrD,OAAO,EAAE,cAAc,EAAE,MAAM,SAAS,CAAC;AACzC,OAAO,EAAE,oBAAoB,EAAE,eAAe,EAAE,mBAAmB,EAAE,YAAY,EAAE,YAAY,EAAE,SAAS,EAAE,aAAa,EAAE,MAAM,iCAAiC,CAAA;AAClK,OAAO,EAAE,uBAAuB,EAAE,MAAM,KAAK,CAAA;AAqF7C,KAAK,gBAAgB,GAAG;IAAA,mBAAA,CAAA,EAAA,oBAAA,CAAA,kBAAA,EAAA,wBAAA,EAAA,GAAA,EAAA,CAAA,CAAA,CAAC;IACxB,OAAO,EAAE,MAAM,CAAA;IACf,IAAI,CAAC,EAAE,SAAS,CAAA;IAChB,IAAI,CAAC,EAAE,MAAM,CAAA;IACb,SAAS,CAAC,EAAE,MAAM,CAAA;IAClB,QAAQ,CAAC,EAAE,MAAM,CAAA;CACjB,CAAA;AAED,WAAW;;;;;;;QAnHX,MAAM,UAAU,GAAG,EAAA,mBAAA,EAAA,IAAA,oBAAA,CAAA,YAAA,EAAA,wBAAA,EAAA,EAAA,EAAA,CAAA,CAAA;YAClB,WAAW,EAAE,IAAI;YACjB,IAAI,EAAE,EAAE;YACR,MAAM,EAAE,EAAE;YACV,SAAS,EAAE,EAAE;YACb,IAAI,EAAE,QAAQ;SACd,iBAAA,CAAA;QACD,OAAO;QAgBP,MAAM,QAAQ,GAAG,kBAAkB,EAAE,CAAC;QAEtC,MAAM,SAAS,GAAG,GAAG,CAAC,uBAAuB,GAAG,IAAI,EAAE,IAAI,CAAC,CAAA;QAC3D,SAAS,MAAM,CAAC,KAAK,EAAG,OAAO,GAAG,MAAM;YACvC,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAI,EAAA,gCAAC,CAAC,CAAA;YAC5B,MAAM,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC;YAC1B,IAAI,CAAC,IAAI,IAAI,EAAE;gBACd,wBAAwB;gBACxB,CAAC,CAAC,WAAW,CAAC,WAAW,EAAE,KAAK,EAAE;oBACjC,OAAO;wBACN,OAAO,CAAC,GAAG,CAAC,SAAQ,EAAA,gCAAC,CAAC,CAAC;oBACxB,CAAC;oBACD,IAAI,CAAC,GAAG;wBACP,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,GAAE,EAAA,gCAAC,CAAC,CAAC;oBAC3B,CAAC;iBACD,IAAI,mBAAmB,CAAC,CAAA;aACzB;QACF,CAAC;QACD,QAAQ;QACR,MAAM,QAAQ,GAAG,GAAG,CAAC;YACpB,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC,EAAE;YACxB,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,EAAE;SACvB,CAAC,CAAC;QAEH,WAAW;QACX,MAAM,OAAO,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;QACvB,SAAS,eAAe,CAAC,CAAC,EAAG,gBAAgB;YAC5C,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAAE,CAAA,EAAA,gCAAC,CAAC,CAAC;YACnC,IAAI,CAAC,CAAC,KAAK,IAAK,IAAI,EAAE;gBACrB,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,IAAI,MAAM,CAAC;aAClC;QACF,CAAC;QACD,UAAU;QACV,MAAM,UAAU,GAAG,QAAQ,CAAC,cAAc,EAAE;YAC3C,KAAK,EAAE,EAAE;YACT,IAAI,EAAE,EAAE;SACR,mBAAC,CAAA;QACF,OAAO;QACP,MAAM,UAAU,GAAG,GAAG,CAAC,YAAY,EAAE;YACpC,IAAI,EAAE,OAAO;YACb,QAAQ,EAAE,IAAI;YACd,OAAO,EAAE,WAAW;SACpB,iBAAC,CAAA;QACF,MAAM,SAAS,GAAG,GAAG,CAAC,YAAY,EAAE;YACnC,IAAI,EAAE,QAAQ;YACd,QAAQ,EAAE,IAAI;YACd,OAAO,EAAE,QAAQ;SACjB,iBAAC,CAAA;QACF,OAAO;QACP,MAAM,WAAW,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;QAE9B,QAAQ;QACR,MAAM,QAAQ,GAAG,GAAG,CAAC,OAAO,CAAC,CAAC;QAC9B,MAAM,UAAU,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;QAC7B,SAAS;QACT,SAAS;QACT,MAAM,WAAW,GAAG,QAAQ,CAAC;YAC5B,QAAQ,EAAE,EAAE;YACZ,QAAQ,EAAE,EAAE;SACZ,CAAC,CAAC;QAEH,OAAO;QACP,MAAM,eAAe,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;QACnC,MAAM,qBAAqB,GAAG,QAAQ,CAAC,YAAG,EAAE;YAC3C,OAAO,CAAC,eAAe,CAAC,KAAK,CAAC;QAC/B,CAAC,CAAC,CAAC;QAEH,WAAW;QACX,SAAS,qBAAqB;YAC7B,eAAe,CAAC,KAAK,GAAG,CAAC,eAAe,CAAC,KAAK,CAAC;QAChD,CAAC;QACD,SAAS,QAAQ,CAAC,CAAC,EAAG,GAAG;YACxB,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE,CAAA,EAAA,gCAAC,CAAC,CAAC;QAC7B,CAAC;QAED,aAAa;QACb,MAAM,QAAQ,GAAG,GAAG,CAAC,uBAAuB,GAAG,IAAI,EAAE,IAAI,CAAC,CAAA;QAC1D,MAAM,YAAY,GAAG,GAAG,CAAC,KAAK,CAAC,CAAA;QAC/B,MAAM,YAAY,GAAG,GAAG,CAAC,EAAE,CAAC,CAAA;QAC5B,MAAM,SAAS,GAAG,GAAG,CAAC,SAAS,EAAE,MAAM,CAAC,CAAA;QACxC,MAAM,SAAS,GAAG,GAAG,CAAC,EAAE,CAAC,CAAA;QACzB,MAAM,cAAc,GAAG,GAAG,CAAC,EAAE,CAAC,CAAA;QAE9B,gBAAgB;QAUhB,SAAS,SAAS,CAAC,OAAO,EAAE,gBAAgB;YAC3C,YAAY,CAAC,KAAK,GAAG,OAAO,CAAC,OAAO,CAAA;YACpC,MAAM,UAAU,GAAG,OAAO,CAAC,IAAI,CAAA;YAC/B,SAAS,CAAC,KAAK,GAAG,UAAU,IAAI,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAA;YAC1D,MAAM,UAAU,GAAG,OAAO,CAAC,IAAI,CAAA;YAC/B,SAAS,CAAC,KAAK,GAAG,UAAU,IAAI,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAA;YACtD,MAAM,eAAe,GAAG,OAAO,CAAC,SAAS,CAAA;YACzC,cAAc,CAAC,KAAK,GAAG,eAAe,IAAI,IAAI,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE,CAAA;YACrE,YAAY,CAAC,KAAK,GAAG,IAAI,CAAA;QAC1B,CAAC;QAED,SAAS,YAAY;YACpB,YAAY,CAAC,KAAK,GAAG,KAAK,CAAA;QAC3B,CAAC;QAED,SAAS;QACT,SAAS,cAAc;YACtB,MAAM,YAAY,GAAG,SAAS,CAAC,KAAK,CAAA;YACpC,IAAI,YAAY,IAAI,IAAI,EAAE;gBACzB,wBAAwB;gBACxB,YAAY,CAAC,WAAW,CAAC,OAAO,EAAE;oBACjC,OAAO,EAAE,GAAG,EAAE;wBACb,OAAO,CAAC,GAAG,CAAC,QAAO,EAAA,gCAAC,CAAC,CAAC;oBACvB,CAAC;oBACD,IAAI,EAAE,CAAC,WAAW,EAAE,oBAAoB,EAAE,EAAE,EAAE;wBAC7C,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,WAAU,EAAA,gCAAC,CAAC,CAAC;oBACrC,CAAC;iBACD,CAAC,CAAA;aACF;QACF,CAAC;QAED,eAAe;QACf,SAAS,YAAY;YACpB,MAAM,OAAO,EAAE,gBAAgB,GAAG;gBACjC,OAAO,EAAE,SAAS;gBAClB,IAAI,EAAE,SAAS;gBACf,IAAI,EAAE,GAAG;aACT,CAAA;YACD,SAAS,CAAC,OAAO,CAAC,CAAA;QACnB,CAAC;QAED,oBAAoB;QACpB,SAAS,cAAc;YACtB,MAAM,OAAO,EAAE,gBAAgB,GAAG;gBACjC,OAAO,EAAE,mBAAmB;gBAC5B,IAAI,EAAE,SAAS;gBACf,IAAI,EAAE,IAAI;aACV,CAAA;YACD,SAAS,CAAC,OAAO,CAAC,CAAA;QACnB,CAAC;;;mBA/OE,kBACoG,CAAA,MAAA,EAAA,QAAA,CAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,CAAA,EAAA;2BADxF,CAAA,KAAA,CAAA,MAAU,CAAA,EAAA,QAAA,CAAA;oBAAd,OAAI,EAAA,UAAU;oBAAE,GAAA,EAAA,QAAS;oBAAe,OAAO,EAAE,KAAA,CAAA,YAAA,CAAY;oBAAG,OAAM,EAAA,KAAA,CAAA,YAAS,CAAA;oBAAG,IAAI,EAAE,KAAA,CAAA,SAAA,CAAS;oBACtG,IAAA,EAAA,KAAW,CAAA,SAAA,CAAA;oBAAiB,SAAQ,EAAE,KAAI,CAAA,cAAA,CAAA;oBAAG,QAAQ,EAAE,IAAA;oBAAQ,QAAQ,EAAA,KAAA;oBAAG,GAAA,EAAA,GAAK;;iBAElF,CAAA,EAAA,IAAA,EAAA,CAAA,CAAiC,WAAA,EAAA,CAAA,SAAA,EAAA,SAAA,EAAA,MAAA,EAAA,MAAA,EAAA,WAAA,CAAA,CAAA;gBAEjC,WAAA,CAAA,KAAA,CAAA,YAA+C,CAAA,CAAA;gBAE/C,kBAAA,CAAiD,MAAA,EAAA,QAAA,CAAA,EAA3C,KAAK,EAAC,2BAAA,EAA6B,CAAA,CAAA;gBAGzC,kBAAA,CAuDO,MAAA,EAAA,QAAA,CAAA,EAvDD,KAAK,EAAC,6BAAgB,EAAA,CAAA,CAAA;kCAE1B,CAAA,MAEO,EAAA,QAAA,CAAA,EAAA,KAFD,EAAK,gBAAa,EAAA,CAAA,EAAA;sCACtB,CAAA,MAAkC,EAAA,QAAA,CAAA,EAAA,KAA5B,EAAK,YAAC,EAAY,CAAA,EAAA;;qBAG1B,CAAA;+BAAgB,CAAA,KAAA,CAAA,OAAA,CAAiB,EAAA,QAAA,CAAA;wBAAE,KAAI,EAAE,iBAAQ;wBAAG,IAAA,EAAK,KAAE,CAAK,QAAA,CAAA;wBAAE,KAAA,EAAM,KAAA;wBAAE,MAAA,EAAO,EAAE;wBAChF,OAAA,EAAM,KAAE,CAAA,OAAA,CAAA;;qBACX,CAAA,EAAA,IAAA,EAAA,CAAA,CAAA,WAwBO,EAAA,CAAA,MAAA,EAAA,SAxBI,CAAA,CAAC;sCAEU,CAAA,MAAA,EAAA,QAAA,CAAA,EAAA,KAAA,EAAA,sBAAA,EAAA,CAAA,EAAA;6BAApB,CAAA,OAAA,CAAA,KAgBQ,CAAA;0DADE,EAAA,QAAA,CAAA,EAAA,GAAA,EAAA,CAAA,EAAA,CAAA,EAAA,QAAA,CAAA;uCAbR,EAAA,WAaQ,CAAA,IAAA,GAAA,EAAA,CAAA,EAAA,CAAA;+CAbG,CAAA,KAAA,CAAA,KAAW,CAAA,EAAA,QAAA,CAAA;wCAAf,OAAI,EAAA,WAAW;wCAAE,GAAA,EAAA,SAAU;oDAAmB,QAAA;;wCAAa,qBAAe,EAAA,MAAA,CAAA,EAAA,GAAA,cAAA,CAAA,UAAA,EAAA,MAAA,CAAA,CAAA,CAAA,CAAA;wCAAG,SAAQ,EAAE,IAAG;wCAC9F,QAAA,EAAA,GAAa;;;+CACd,EAAA,WAIY,CAAA,IAAA,GAAA,EAAA,CAAA,EAAA,CAAA;uDAJK,CAAA,KAAO,CAAA,SAAA,CAAA,EAAA,QAAA,CAAA;gDAAE,KAAI,EAAE,OAAA;gDAAa,IAAA,EAAA,KAAW,CAAA,UAAE,CAAA;;;uDACxD,EAAA,WAEY,CAAA,IAAA,GAAA,EAAA,CAAA,EAAA,CAAA;+DAFG,CAAA,KAAO,CAAA,QAAA,CAAA,EAAA,QAAA,CAAA;wDAAE,GAAA,EAAA,OAAI;wDAAoB,MAAA,EAAA,GAAA,EAAY,GAAE,MAAK,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA;wDAAG,YAAQ,EAAA,KAAA;wDAAG,IAAA,EAAM,EAAE;wDAAI,MAAK,EAAA,EAAA;wDAChG,IAAA,EAAA,QAAY;;;;;;;6CAGhB,CAAA,EAAA,CAAA,CAAA,WAKY,EAAA,CAAA,MAAA,CAAA,CAAA;uDALK,CAAA,KAAM,CAAA,SAAA,CAAA,EAAA,QAAA,CAAA;gDAAE,KAAI,EAAE,MAAA;gDAAY,IAAA,EAAA,KAAW,CAAA,SAAE,CAAA;;;uDACtD,EAAA,WAGY,CAAA,IAAA,GAAA,EAAA,CAAA,EAAA,CAAA;+DAHG,CAAA,KAAM,CAAA,QAAA,CAAA,EAAA,QAAA,CAAA;wDAAE,GAAA,EAAA,MAAI;wDAAmB,MAAA,EAAA,GAAA,EAAY,GAAE,MAAK,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA;wDAAG,YAAQ,EAAA,KAAA;wDAAG,IAAA,EAAM,EAAE;wDAAI,MAAK,EAAA,EAAA;wDAC9F,IAAA,EAAA,QAAY;;;;;;;;;;;;;6BAQpB,CAAA,CAAA;0DAEA,EAAA,QAFc,CAAA,EAAA,GAEd,EAAA,CAAA,EAAA,CAAA,EAAA,QAAA,CAAA;;;;qBAIF,CAAA;sCASY,CAAA,MAAA,EAAA,QAAA,CAAA,EAAA,KAAA,EAAA,WAAA,EAAA,CAAA,EAAA;mCARO,CAAA,KAAA,CAAA,MAAQ,CAAA,EAAA,QAAA,CAAA;4BAAC,OAAM,EAAA,QAAA;;;mCAE9B,EAAA,WACiD,CAAA,IAAA,GAAA,EAAA,CAAA,EAAA,CAAA;2CAD7B,CAAA,KAAA,CAAA,WAAoB,CAAA,EAAA,QAAA,CAAA;oCAAE,KAAA,EAAO,oBAAE;oCAAiB,OAAA,EAAA,KAAa,CAAA,eAAM,CAAA;oCAAE,YAAY,EAAA,MAAI;oCACtG,UAAQ,EAAA,IAAA;;iCACX,CAAA,EAAA,IAAA,EAAA,CAAA,CAAA,WAA2C,EAAA,CAAA,SAAA,CAAA,CAAA;gCAC3C,kBAAA,CAA2D,MAAA,EAAA,QAAA,CAAA,EAArD,KAAK,EAAC,gBAAA,EAAA,CAAA,EAAA,SAAA,CAA+B;gCAC3C,kBAAA,CAAqC,MAAA,EAAA,QAAA,CAAA,EAA/B,KAAK,EAAC,+BAAkB,EAAA,CAAA,EAAA,UAAA,CAAA;gCAC9B,kBAAA,CAAyD,MAAA,EAAA,QAAA,CAAA,EAAnD,KAAK,EAAC,gBAAA,EAAA,CAAA,EAAA,GAAA,CAAA;;;;;qBAKhB,CAAA;sCACE,CAAA,MAAuE,EAAA,QAAA,CAAA,EAAA,KAAA,EAAA,cAAA,EAAA,CAAA,EAAA;0CAAzD,CAAA,QAAA,EAAmB,QAAA,CAAA;4BAAE,KAAA,EAAK,mBAAgB;mCAAE,EAAI,cAAA;yBAC9D,CAAA,EAAA,MAAA,CAAA;0CAAc,CAAA,QAAc,EAAA,QAAA,CAAA;4BAAE,KAAA,EAAK,cAAc;mCAAE,EAAI,YAAA;yBACvD,CAAA,EAAA,MAAA,CAAA;0CAAc,CAAA,QAAgB,EAAA,QAAA,CAAA;4BAAE,KAAA,EAAK,gBAAgB;mCAAE,EAAA,cAAO;;;iBAMlE,CAAA;kCACqD,CAAA,MAAA,EAAA,QAAA,CAAA,EAAA,KAAvC,EAAA,QAAK,EAAA,CAAA,EAAA", "sourcesContent": ["<template>\r\n  <view class=\"login-container\">\r\n    <!-- 页面级 Toast 提示框 -->\r\n    <uToast ref=\"toastRef\" :visible=\"toastVisible\" :message=\"toastMessage\" :type=\"toastType\" :icon=\"toastIcon\"\r\n      :iconColor=\"toastIconColor\" :duration=\"4000\" :position=\"'top'\" :top=\"120\" @close=\"onToastClose\" />\r\n\r\n    <fui-status-bar></fui-status-bar>\r\n    <!-- 右上角渐变球 -->\r\n    <view class=\"gradient-circle top-right\"></view>\r\n    <!-- 左下角渐变球 -->\r\n    <view class=\"gradient-circle bottom-left\"></view>\r\n    <!-- 切换登录 -->\r\n    <!-- 登录表单 -->\r\n    <view class=\"form-container\">\r\n      <!-- Logo/标题 -->\r\n      <view class=\"logo-title\">\r\n        <text class=\"title-text\">登录</text>\r\n      </view>\r\n      <!-- 手机号登录 -->\r\n      <fui-tabs class=\"form-login-tabs\" :tabs=\"tabsList\" :short=\"false\" center :current=\"current\"\r\n        @change=\"handleChangeTab\"></fui-tabs>\r\n      <view class=\"login-form-container\">\r\n\r\n        <block v-if=\"current === 0\">\r\n          <!-- TODO: 要加前缀 +86 fui-dropdown-menu -->\r\n          <uForm ref=\"loginForm\" @submited=\"onSubmit\" v-model=\"phoneModel\" :showToast=\"true\" :toastTop=\"120\"\r\n            :toastDuration=\"4000\">\r\n            <uFromItem field=\"phone\" :rule=\"phoneRules\" :marginTop=\"16\">\r\n              <fui-input ref=\"phone\" @blur=\"onBlur('phone')\" :borderBottom=\"false\" :size=\"28\" :radius=\"12\" type=\"number\"\r\n                placeholder=\"请输入手机号\" v-model=\"phoneModel.phone as string\">\r\n              </fui-input>\r\n            </uFromItem>\r\n            <uFromItem field=\"code\" :rule=\"codeRules\" :marginTop=\"16\">\r\n              <fui-input ref=\"code\" @blur=\"onBlur('code')\" :borderBottom=\"false\" :size=\"28\" :radius=\"12\" type=\"number\"\r\n                placeholder=\"请输入验证码\" v-model=\"phoneModel.code as string\">\r\n\r\n              </fui-input>\r\n            </uFromItem>\r\n          </uForm>\r\n        </block>\r\n\r\n        <!-- 账号登录 -->\r\n        <block v-else>\r\n          2\r\n        </block>\r\n      </view>\r\n\r\n      <!-- 协议同意 -->\r\n      <view class=\"agreement\">\r\n        <fui-row justify=\"center\" class=\"agreement-row\">\r\n          <!-- 必须要这样写，转kt的时候不这样写会出现问题 -->\r\n          <fui-checkbox class=\"agreement-checkbox\" :checked=\"isAgreeProtocol\" borderRadius=\"8rpx\" :scaleRatio=\"0.78\"\r\n            @change=\"ChangeIsAgreeProtocol\"></fui-checkbox>\r\n          <text class=\"agreement-text\">我已阅读并同意</text>\r\n          <text class=\"agreement-text agreement-link\">《用户服务条款》</text>\r\n          <text class=\"agreement-text\">和</text>\r\n          <text class=\"agreement-text agreement-link\">《隐私协议》</text>\r\n        </fui-row>\r\n      </view>\r\n\r\n      <!-- 验证测试按钮 -->\r\n      <view class=\"test-buttons\">\r\n        <button class=\"test-btn validate\" @click=\"testValidation\">测试验证</button>\r\n        <button class=\"test-btn svg\" @click=\"testSvgToast\">成功提示</button>\r\n        <button class=\"test-btn emoji\" @click=\"testEmojiToast\">Emoji图标</button>\r\n      </view>\r\n\r\n      <!-- 登录按钮 -->\r\n    </view>\r\n    <!-- 底部 -->\r\n    <view class=\"footer\">\r\n      <fui-footer text=\"by@海南长养乔智能科技有限责任公司\"></fui-footer>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup lang=\"uts\">\r\n\tconst inputStyle = {\r\n\t\tinputBorder: true,\r\n\t\tsize: 28,\r\n\t\tradius: 12,\r\n\t\tmarginTop: 16,\r\n\t\ttype: \"number\"\r\n\t}\r\n\t// 引入组件\r\n\timport fuiStatusBar from \"@/components/firstui/fui-status-bar/fui-status-bar\";\r\n\timport fuiFooter from \"@/components/firstui/fui-footer/fui-footer\";\r\n\timport fuiRow from \"@/components/firstui/fui-row/fui-row\";\r\n\timport fuiTabs from \"@/components/firstui/fui-tabs/fui-tabs\";\r\n\timport fuiInput from \"@/components/firstui/fui-input/fui-input\";\r\n\timport fuiButton from \"@/components/firstui/fui-button/fui-button\";\r\n\timport fuiIcon from \"@/components/firstui/fui-icon/fui-icon\";\r\n\timport fuiCheckbox from \"@/components/firstui/fui-checkbox/fui-checkbox\";\r\n\timport { FuiTabsItemParam } from \"@/components/firstui/fui-types/index.uts\";\r\n\timport uForm from \"@/components/uc/u-form/u-form\";\r\n\timport uFromItem from \"@/components/uc/u-from-item/u-from-item\";\r\n\timport uToast from \"@/components/uc/u-toast/u-toast\";\r\n\timport { phoneModelType } from \"./types\";\r\n\timport { FormItemVerifyResult, FormValidResult, FormValidResultItem, FormItemData, FormItemRule, ToastType, ToastPosition } from \"@/components/uc/types/index.uts\"\r\n\timport { ComponentPublicInstance } from 'vue'\r\n\tconst instance = getCurrentInstance();\r\n\r\n\tconst loginForm = ref<ComponentPublicInstance | null>(null)\r\n\tfunction onBlur(field : 'phone' | 'code') {\r\n\t\tconsole.log(\"field:\", field)\r\n\t\tconst f = loginForm.value;\r\n\t\tif (f != null) {\r\n\t\t\t// 使用 $callMethod 调用组件方法\r\n\t\t\tf.$callMethod('validItem', field, {\r\n\t\t\t\tsuccess() {\r\n\t\t\t\t\tconsole.log(\"success\");\r\n\t\t\t\t},\r\n\t\t\t\tfail(res) {\r\n\t\t\t\t\tconsole.log(\"fail:\", res);\r\n\t\t\t\t}\r\n\t\t\t} as FormValidResultItem)\r\n\t\t}\r\n\t}\r\n\t// 标签页数据\r\n\tconst tabsList = ref([\r\n\t\t{ name: '手机号登录', id: 0 },\r\n\t\t{ name: '账号登录', id: 1 },\r\n\t]);\r\n\r\n\t// 当前选中的标签页\r\n\tconst current = ref(0);\r\n\tfunction handleChangeTab(e : FuiTabsItemParam) {\r\n\t\tconsole.log(\"handleChangeTab:\", e);\r\n\t\tif (e.index !== null) {\r\n\t\t\tcurrent.value = e.index as number;\r\n\t\t}\r\n\t}\r\n\t// 手机号登录表单\r\n\tconst phoneModel = reactive<phoneModelType>({\r\n\t\tphone: '',\r\n\t\tcode: ''\r\n\t})\r\n\t// 表单配置\r\n\tconst phoneRules = ref<FormItemRule>({\r\n\t\ttype: 'phone',\r\n\t\trequired: true,\r\n\t\tmessage: '请输入正确的手机号'\r\n\t})\r\n\tconst codeRules = ref<FormItemRule>({\r\n\t\ttype: 'number',\r\n\t\trequired: true,\r\n\t\tmessage: '请输入验证码'\r\n\t})\r\n\t// 国家区号\r\n\tconst countryCode = ref('86');\r\n\r\n\t// 验证码相关\r\n\tconst codeText = ref('获取验证码');\r\n\tconst canGetCode = ref(true);\r\n\t//-------\r\n\t// 账号登录表单\r\n\tconst accountForm = reactive({\r\n\t\tusername: '',\r\n\t\tpassword: '',\r\n\t});\r\n\r\n\t// 协议同意\r\n\tconst isAgreeProtocol = ref(false);\r\n\tconst isDisabledloginButton = computed(() => {\r\n\t\treturn !isAgreeProtocol.value;\r\n\t});\r\n\r\n\t// 登录按钮是否禁用\r\n\tfunction ChangeIsAgreeProtocol() {\r\n\t\tisAgreeProtocol.value = !isAgreeProtocol.value;\r\n\t}\r\n\tfunction onSubmit(e : any) {\r\n\t\tconsole.log(\"onSubmit:\", e);\r\n\t}\r\n\r\n\t// Toast 相关数据\r\n\tconst toastRef = ref<ComponentPublicInstance | null>(null)\r\n\tconst toastVisible = ref(false)\r\n\tconst toastMessage = ref('')\r\n\tconst toastType = ref<ToastType>('info')\r\n\tconst toastIcon = ref('')\r\n\tconst toastIconColor = ref('')\r\n\r\n\t// 定义 Toast 选项类型\r\n\ttype ShowToastOptions = {\r\n\t\tmessage: string\r\n\t\ttype?: ToastType\r\n\t\ticon?: string\r\n\t\ticonColor?: string\r\n\t\tduration?: number\r\n\t}\r\n\r\n\t// Toast 方法\r\n\tfunction showToast(options: ShowToastOptions) {\r\n\t\ttoastMessage.value = options.message\r\n\t\tconst optionType = options.type\r\n\t\ttoastType.value = optionType != null ? optionType : 'info'\r\n\t\tconst optionIcon = options.icon\r\n\t\ttoastIcon.value = optionIcon != null ? optionIcon : ''\r\n\t\tconst optionIconColor = options.iconColor\r\n\t\ttoastIconColor.value = optionIconColor != null ? optionIconColor : ''\r\n\t\ttoastVisible.value = true\r\n\t}\r\n\r\n\tfunction onToastClose() {\r\n\t\ttoastVisible.value = false\r\n\t}\r\n\r\n\t// 测试验证功能\r\n\tfunction testValidation() {\r\n\t\tconst formInstance = loginForm.value\r\n\t\tif (formInstance != null) {\r\n\t\t\t// 使用 $callMethod 调用组件方法\r\n\t\t\tformInstance.$callMethod('valid', {\r\n\t\t\t\tsuccess: () => {\r\n\t\t\t\t\tconsole.log(\"表单验证成功\");\r\n\t\t\t\t},\r\n\t\t\t\tfail: (failResults: FormItemVerifyResult[]) => {\r\n\t\t\t\t\tconsole.log(\"表单验证失败:\", failResults);\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t}\r\n\t}\r\n\r\n\t// 测试成功图标 Toast\r\n\tfunction testSvgToast() {\r\n\t\tconst options: ShowToastOptions = {\r\n\t\t\tmessage: '这是成功提示！',\r\n\t\t\ttype: 'success',\r\n\t\t\ticon: '✓'\r\n\t\t}\r\n\t\tshowToast(options)\r\n\t}\r\n\r\n\t// 测试 Emoji 图标 Toast\r\n\tfunction testEmojiToast() {\r\n\t\tconst options: ShowToastOptions = {\r\n\t\t\tmessage: '这是使用 Emoji 图标的提示！',\r\n\t\t\ttype: 'warning',\r\n\t\t\ticon: '🎉'\r\n\t\t}\r\n\t\tshowToast(options)\r\n\t}\r\n\r\n\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.login-container {\r\n  height: 100%;\r\n  min-height: 100vh;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  position: relative;\r\n  // overflow: hidden;\r\n}\r\n\r\n.gradient-circle {\r\n  position: absolute;\r\n\r\n  border-radius: 200rpx;\r\n\r\n\r\n\r\n\r\n  background: radial-gradient(circle, #33a1fd 0%, #0062e6 70%);\r\n  background-color: #33a1fd;\r\n  /* 备用背景颜色 */\r\n  z-index: 0;\r\n}\r\n\r\n/* 添加调试样式 */\r\n.gradient-circle::after {\r\n  // content: '调试: 渐变球';\r\n  // color: red;\r\n  font-size: 12px;\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n}\r\n\r\n.top-right {\r\n  top: -80rpx;\r\n  right: -30rpx;\r\n  width: 300rpx;\r\n  /* 调整宽度 */\r\n  height: 300rpx;\r\n  /* 调整高度 */\r\n  opacity: 0.08;\r\n}\r\n\r\n.bottom-left {\r\n  bottom: -60rpx;\r\n  left: -60rpx;\r\n  width: 280rpx;\r\n  /* 调整宽度 */\r\n  height: 280rpx;\r\n  /* 调整高度 */\r\n  opacity: 0.1;\r\n}\r\n\r\n\r\n.logo-title {\r\n  text-align: center;\r\n  margin: 10rpx 0 30rpx;\r\n}\r\n\r\n.title-text {\r\n  font-size: 58rpx;\r\n  font-weight: bold;\r\n  color: $fui-color-primary;\r\n  //   font-family: ;\r\n}\r\n\r\n.form-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  width: 100%;\r\n  height: 70%;\r\n\r\n  .login-form-container {\r\n    // width: 100%;\r\n    padding: 0 16rpx;\r\n    display: flex;\r\n    min-width: 580rpx;\r\n  }\r\n\r\n  .form-login-tabs {}\r\n}\r\n\r\n.agreement {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  margin-top: 24rpx;\r\n  color: $fui-color-minor;\r\n\r\n  .agreement-row {\r\n    align-items: center;\r\n  }\r\n}\r\n\r\n.agreement-text {\r\n  font-size: 24rpx;\r\n}\r\n\r\n.agreement-link {\r\n  color: $fui-color-primary;\r\n  text-decoration: none;\r\n  font-weight: 500;\r\n}\r\n\r\n.footer {\r\n  text-align: center;\r\n  padding: 16rpx;\r\n  color: $fui-color-minor;\r\n  font-size: $fui-input-size;\r\n  margin-top: 20rpx;\r\n  // font-family: var(--font-content);\r\n}\r\n\r\n/* Toast 测试按钮样式 */\r\n.test-buttons {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 16rpx;\r\n  margin-top: 32rpx;\r\n  justify-content: center;\r\n}\r\n\r\n.test-btn {\r\n  padding: 16rpx 24rpx;\r\n  border-radius: 8rpx;\r\n  border: none;\r\n  font-size: 24rpx;\r\n  color: white;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  min-width: 120rpx;\r\n\r\n  &.validate {\r\n    background-color: #ff4d4f;\r\n\r\n    &:hover {\r\n      background-color: #ff7875;\r\n    }\r\n  }\r\n\r\n  &.svg {\r\n    background-color: #52c41a;\r\n\r\n    &:hover {\r\n      background-color: #73d13d;\r\n    }\r\n  }\r\n\r\n  &.emoji {\r\n    background-color: #faad14;\r\n\r\n    &:hover {\r\n      background-color: #ffc53d;\r\n    }\r\n  }\r\n}\r\n</style>"]}