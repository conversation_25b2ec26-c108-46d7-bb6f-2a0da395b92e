{"version": 3, "sources": ["pages/login/login.uvue"], "names": [], "mappings": "AAkFA,OAAO,YAAY,MAAM,oDAAoD,CAAC;AAC9E,OAAO,SAAS,MAAM,4CAA4C,CAAC;AACnE,OAAO,MAAM,MAAM,sCAAsC,CAAC;AAC1D,OAAO,OAAO,MAAM,wCAAwC,CAAC;AAC7D,OAAO,QAAQ,MAAM,0CAA0C,CAAC;AAChE,OAAO,SAAS,MAAM,4CAA4C,CAAC;AACnE,OAAO,OAAO,MAAM,wCAAwC,CAAC;AAC7D,OAAO,WAAW,MAAM,gDAAgD,CAAC;AACzE,OAAO,EAAE,gBAAgB,EAAE,MAAM,0CAA0C,CAAC;AAC5E,OAAO,KAAK,MAAM,+BAA+B,CAAC;AAClD,OAAO,SAAS,MAAM,yCAAyC,CAAC;AAChE,OAAO,EAAE,cAAc,EAAE,MAAM,SAAS,CAAC;AACzC,OAAO,EAAE,oBAAoB,EAAE,MAAM,iCAAiC,CAAA;;;;;;;QApBtE,MAAM,UAAU,GAAG,EAAA,mBAAA,EAAA,IAAA,oBAAA,CAAA,YAAA,EAAA,wBAAA,EAAA,EAAA,EAAA,CAAA,CAAA;YACjB,WAAW,EAAC,IAAI;YAChB,IAAI,EAAC,EAAE;YACP,MAAM,EAAC,EAAE;YACT,SAAS,EAAC,EAAE;YACZ,IAAI,EAAE,QAAQ;SACf,iBAAA,CAAA;QACD,OAAO;QAeP,MAAM,SAAS,GAAG,GAAG,CAAC,uBAAuB,GAAG,IAAI,EAAE,IAAI,CAAC,CAAA;QAC3D,SAAS,MAAM,CAAC,KAAK,EAAE,MAAM;YAC3B,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAC,KAAI,EAAA,+BAAC,CAAC,CAAC;YAE5B,IAAI,SAAS,CAAC,KAAK,IAAI,IAAI,EAAE;gBAC3B,wBAAwB;gBACxB,SAAS,CAAC,KAAK,CAAC,WAAW,CAAC,WAAW,EAAE,KAAK,EAAE;oBAC9C,OAAO;wBACL,OAAO,CAAC,GAAG,CAAC,SAAQ,EAAA,gCAAC,CAAC,CAAC;oBACzB,CAAC;oBACD,IAAI,CAAC,GAAG,EAAE,oBAAoB,GAAG,IAAI;wBACnC,OAAO,CAAC,GAAG,CAAC,OAAO,EAAC,GAAE,EAAA,gCAAC,CAAC,CAAC;oBAC3B,CAAC;iBACF,CAAC,CAAA;aACH;QACH,CAAC;QACD,QAAQ;QACR,MAAM,QAAQ,GAAG,GAAG,CAAC;YACpB,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC,EAAE;YACxB,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,EAAE;SACvB,CAAC,CAAC;QAEH,WAAW;QACX,MAAM,OAAO,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;QACvB,SAAS,eAAe,CAAC,CAAC,EAAC,gBAAgB;YAC1C,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAAC,CAAA,EAAA,gCAAC,CAAC,CAAC;YAClC,IAAG,CAAC,CAAC,KAAK,IAAK,IAAI,EAAC;gBACnB,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,IAAI,MAAM,CAAC;aAClC;QACF,CAAC;QACD,UAAU;QACV,MAAM,UAAU,GAAG,QAAQ,CAAC,cAAc,EAAE;YAC1C,KAAK,EAAE,EAAE;YACT,IAAI,EAAE,EAAE;SACT,mBAAC,CAAA;QACF,OAAO;QACP,MAAM,UAAU,GAAG,GAAG,CAAC;YACtB,OAAO,EAAE;gBACR,IAAI,EAAE,QAAQ;gBACd,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,QAAQ;gBACjB,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ,CAAC;aAC3B;YACD,MAAM,EAAE;gBACP,IAAI,EAAE,QAAQ;gBACd,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,QAAQ;gBACjB,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ,CAAC;aAC3B;SACD,CAAC,CAAA;QACF,OAAO;QACP,MAAM,WAAW,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;QAE9B,QAAQ;QACR,MAAM,QAAQ,GAAG,GAAG,CAAC,OAAO,CAAC,CAAC;QAC9B,MAAM,UAAU,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;QAC7B,SAAS;QACT,SAAS;QACT,MAAM,WAAW,GAAG,QAAQ,CAAC;YAC5B,QAAQ,EAAE,EAAE;YACZ,QAAQ,EAAE,EAAE;SACZ,CAAC,CAAC;QAEH,OAAO;QACP,MAAM,eAAe,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;QACnC,MAAM,qBAAqB,GAAG,QAAQ,CAAC,YAAG,EAAE;YAC3C,OAAO,CAAC,eAAe,CAAC,KAAK,CAAC;QAC/B,CAAC,CAAC,CAAC;QAEH,WAAW;QACX,SAAS,qBAAqB;YAC7B,eAAe,CAAC,KAAK,GAAG,CAAC,eAAe,CAAC,KAAK,CAAC;QAChD,CAAC;QACD,SAAS,QAAQ,CAAC,CAAC,EAAC,GAAG;YACtB,OAAO,CAAC,GAAG,CAAC,WAAW,EAAC,CAAA,EAAA,gCAAC,CAAC,CAAC;QAC5B,CAAC;;;mBAzKG,kBAAiC,CAAA,MAAA,EAAA,QAAA,CAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,CAAA,EAAA;gBAEjC,WAAA,CAAA,KAAA,CAAA,YAA+C,CAAA,CAAA;gBAE/C,kBAAA,CAAiD,MAAA,EAAA,QAAA,CAAA,EAA3C,KAAK,EAAC,2BAAA,EAA6B,CAAA,CAAA;gBAGzC,kBAAA,CAsDO,MAAA,EAAA,QAAA,CAAA,EAtDD,KAAK,EAAC,6BAAgB,EAAA,CAAA,CAAA;kCAE1B,CAAA,MAEO,EAAA,QAAA,CAAA,EAAA,KAFD,EAAK,gBAAa,EAAA,CAAA,EAAA;sCACtB,CAAA,MAAkC,EAAA,QAAA,CAAA,EAAA,KAA5B,EAAK,YAAC,EAAY,CAAA,EAAA;;qBAG1B,CAAA;+BAAgB,CAAA,KAAA,CAAA,OAAA,CAAiB,EAAA,QAAA,CAAA;wBAAE,KAAI,EAAE,iBAAQ;wBAAG,IAAA,EAAK,KAAE,CAAK,QAAA,CAAA;wBAAE,KAAA,EAAM,KAAA;wBAAE,MAAA,EAAO,EAAE;wBAChF,OAAA,EAAM,KAAE,CAAA,OAAA,CAAA;;qBACX,CAAA,EAAA,IAAA,EAAA,CAAA,CAAA,WA8BO,EAAA,CAAA,MAAA,EAAA,SA9BI,CAAA,CAAC;sCAEU,CAAA,MAAA,EAAA,QAAA,CAAA,EAAA,KAAA,EAAA,sBAAA,EAAA,CAAA,EAAA;6BAApB,CAAA,OAAA,CAAA,KAsBQ,CAAA;0DADE,EAAA,QAAA,CAAA,EAAA,GAAA,EAAA,CAAA,EAAA,CAAA,EAAA,QAAA,CAAA;uCAnBR,EAAA,WAmBQ,CAAA,IAAA,GAAA,EAAA,CAAA,EAAA,CAAA;+CAnBG,CAAA,KAAA,CAAA,KAAW,CAAA,EAAA,QAAA,CAAA;wCAAf,OAAI,EAAA,WAAW;wCAAE,GAAA,EAAA,SAAU;wCAAU,UAAA,EAAA,QAAa;qDAAU,EAAA,EAAA;;;;+CACjE,EAAA,WAQY,CAAA,IAAA,GAAA,EAAA,CAAA,EAAA,CAAA;uDARK,CAAA,KAAO,CAAA,SAAA,CAAA,EAAA,QAAA,CAAA;gDAAE,KAAI,EAAE,OAAA;;;;oDAI/B,OAAA,EAAA,WAAA;;;uDACC,EAAA,WAEY,CAAA,IAAA,GAAA,EAAA,CAAA,EAAA,CAAA;+DAFI,CAAA,KAAE,CAAA,QAAM,CAAA,EAAA,QAAA,CAAA;wDAAW,MAAA,EAAA,GAAW,EAAX,GAAW,MAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA;wDAAE,WAAQ,EAAA,EAAA;wDAAG,IAAA,EAAM,EAAE;wDAAK,MAAA,EAAA,EAAS;wDAAM,SAAK,EAAA,EAAA;wDAC1F,IAAA,EAAA,QAAY;;;;;;;6CAGhB,CAAA,CAAA;uDAAiB,CAAA,KAAM,CAAA,SAAA,CAAA,EAAA,QAAA,CAAA;gDAAE,KAAI,EAAE,MAAA;;;oDAG9B,OAAA,EAAA,QAAA;;;uDACC,EAAA,WAGY,CAAA,IAAA,GAAA,EAAA,CAAA,EAAA,CAAA;+DAHI,CAAA,KAAE,CAAA,QAAM,CAAA,EAAA,QAAA,CAAA;wDAAU,MAAA,EAAA,GAAW,EAAX,GAAW,MAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA;wDAAE,WAAQ,EAAA,EAAA;wDAAG,IAAA,EAAM,EAAE;wDAAK,MAAA,EAAA,EAAS;wDAAM,SAAK,EAAA,EAAA;wDACzF,IAAA,EAAA,QAAY;;;;;;;;;;;;;6BAQpB,CAAA,CAAA;0DAEA,EAAA,QAFc,CAAA,EAAA,GAEd,EAAA,CAAA,EAAA,CAAA,EAAA,QAAA,CAAA;;;;qBAIF,CAAA;sCASY,CAAA,MAAA,EAAA,QAAA,CAAA,EAAA,KAAA,EAAA,WAAA,EAAA,CAAA,EAAA;mCARO,CAAA,KAAA,CAAA,MAAQ,CAAA,EAAA,QAAA,CAAA;4BAAC,OAAM,EAAA,QAAA;;;mCAE9B,EAAA,WACiD,CAAA,IAAA,GAAA,EAAA,CAAA,EAAA,CAAA;2CAD7B,CAAA,KAAA,CAAA,WAAoB,CAAA,EAAA,QAAA,CAAA;oCAAE,KAAA,EAAO,oBAAE;oCAAiB,OAAA,EAAA,KAAa,CAAA,eAAM,CAAA;oCAAE,YAAY,EAAA,MAAI;oCACtG,UAAQ,EAAA,IAAA;;iCACX,CAAA,EAAA,IAAA,EAAA,CAAA,CAAA,WAA2C,EAAA,CAAA,SAAA,CAAA,CAAA;gCAC3C,kBAAA,CAA2D,MAAA,EAAA,QAAA,CAAA,EAArD,KAAK,EAAC,gBAAA,EAAA,CAAA,EAAA,SAAA,CAA+B;gCAC3C,kBAAA,CAAqC,MAAA,EAAA,QAAA,CAAA,EAA/B,KAAK,EAAC,+BAAkB,EAAA,CAAA,EAAA,UAAA,CAAA;gCAC9B,kBAAA,CAAyD,MAAA,EAAA,QAAA,CAAA,EAAnD,KAAK,EAAC,gBAAA,EAAA,CAAA,EAAA,GAAA,CAAA;;;;;;iBAOlB,CAAA;kCACqD,CAAA,MAAA,EAAA,QAAA,CAAA,EAAA,KAAvC,EAAA,QAAK,EAAA,CAAA,EAAA", "sourcesContent": ["<template>\n  <view class=\"login-container\">\n    <fui-status-bar></fui-status-bar>\n    <!-- 右上角渐变球 -->\n    <view class=\"gradient-circle top-right\"></view>\n    <!-- 左下角渐变球 -->\n    <view class=\"gradient-circle bottom-left\"></view>\n    <!-- 切换登录 -->\n    <!-- 登录表单 -->\n    <view class=\"form-container\">\n      <!-- Logo/标题 -->\n      <view class=\"logo-title\">\n        <text class=\"title-text\">登录</text>\n      </view>\n      <!-- 手机号登录 -->\n      <fui-tabs class=\"form-login-tabs\" :tabs=\"tabsList\" :short=\"false\" center :current=\"current\"\n        @change=\"handleChangeTab\"></fui-tabs>\n      <view class=\"login-form-container\">\n\n        <block v-if=\"current === 0\">\n          <!-- TODO: 要加前缀 +86 fui-dropdown-menu -->\n          <uForm ref=\"loginForm\" @submited=\"onSubmit\" triggerChange v-model=\"phoneModel\">\n            <uFromItem field=\"phone\" :rule=\"{\n              required: true,\n              type: 'phone',\n              message: '请输入正确的手机号'\n            }\">\n              <fui-input @blur=\"onBlur('phone')\" inputBorder :size=\"28\" :radius=\"12\" :marginTop=\"16\" type=\"number\"\n                placeholder=\"请输入手机号\" v-model=\"phoneModel.phone as string\">\n              </fui-input>\n            </uFromItem>\n            <uFromItem field=\"code\" :rule=\"{\n              required: true,\n              message: '请输入验证码'\n            }\">\n              <fui-input @blur=\"onBlur('code')\" inputBorder :size=\"28\" :radius=\"12\" :marginTop=\"16\" type=\"number\"\n                placeholder=\"请输入验证码\" v-model=\"phoneModel.code as string\">\n\n              </fui-input>\n            </uFromItem>\n          </uForm>\n        </block>\n\n        <!-- 账号登录 -->\n        <block v-else>\n          2\n        </block>\n      </view>\n\n      <!-- 协议同意 -->\n      <view class=\"agreement\">\n        <fui-row justify=\"center\" class=\"agreement-row\">\n          <!-- 必须要这样写，转kt的时候不这样写会出现问题 -->\n          <fui-checkbox class=\"agreement-checkbox\" :checked=\"isAgreeProtocol\" borderRadius=\"8rpx\" :scaleRatio=\"0.78\"\n            @change=\"ChangeIsAgreeProtocol\"></fui-checkbox>\n          <text class=\"agreement-text\">我已阅读并同意</text>\n          <text class=\"agreement-text agreement-link\">《用户服务条款》</text>\n          <text class=\"agreement-text\">和</text>\n          <text class=\"agreement-text agreement-link\">《隐私协议》</text>\n        </fui-row>\n      </view>\n\n      <!-- 登录按钮 -->\n    </view>\n    <!-- 底部 -->\n    <view class=\"footer\">\n      <fui-footer text=\"by@海南长养乔智能科技有限责任公司\"></fui-footer>\n    </view>\n  </view>\n</template>\n\n<script setup lang=\"uts\">\n\n\nconst inputStyle = {\n  inputBorder:true,\n  size:28,\n  radius:12,\n  marginTop:16,\n  type: \"number\"\n}\n// 引入组件\nimport fuiStatusBar from \"@/components/firstui/fui-status-bar/fui-status-bar\";\nimport fuiFooter from \"@/components/firstui/fui-footer/fui-footer\";\nimport fuiRow from \"@/components/firstui/fui-row/fui-row\";\nimport fuiTabs from \"@/components/firstui/fui-tabs/fui-tabs\";\nimport fuiInput from \"@/components/firstui/fui-input/fui-input\";\nimport fuiButton from \"@/components/firstui/fui-button/fui-button\";\nimport fuiIcon from \"@/components/firstui/fui-icon/fui-icon\";\nimport fuiCheckbox from \"@/components/firstui/fui-checkbox/fui-checkbox\";\nimport { FuiTabsItemParam } from \"@/components/firstui/fui-types/index.uts\";\nimport uForm from \"@/components/uc/u-form/u-form\";\nimport uFromItem from \"@/components/uc/u-from-item/u-from-item\";\nimport { phoneModelType } from \"./types\";\nimport { FormItemVerifyResult } from \"@/components/uc/types/index.uts\"\n\nconst loginForm = ref<ComponentPublicInstance | null>(null)\nfunction onBlur(field: string){\n  console.log(\"field:\",field);\n\n  if (loginForm.value != null) {\n    // 使用 $callMethod 调用组件方法\n    loginForm.value.$callMethod('validItem', field, {\n      success(){\n        console.log(\"success\");\n      },\n      fail(res: FormItemVerifyResult | null){\n        console.log(\"fail:\",res);\n      }\n    })\n  }\n}\n// 标签页数据\nconst tabsList = ref([\n\t{ name: '手机号登录', id: 0 },\n\t{ name: '账号登录', id: 1 },\n]);\n\n// 当前选中的标签页\nconst current = ref(0);\nfunction handleChangeTab(e:FuiTabsItemParam){\n\tconsole.log(\"handleChangeTab:\",e);\n\tif(e.index !== null){\n\t\tcurrent.value = e.index as number;\n\t}\n}\n// 手机号登录表单\nconst phoneModel = reactive<phoneModelType>({\n\t\tphone: '',\n\t\tcode: ''\n})\n// 表单配置\nconst phoneRules = ref({\n\t'phone': {\n\t\ttype: 'string',\n\t\trequired: true,\n\t\tmessage: '请输入手机号',\n\t\ttrigger: ['blur', 'change']\n\t},\n\t'code': {\n\t\ttype: 'string',\n\t\trequired: true,\n\t\tmessage: '请输入验证码',\n\t\ttrigger: ['blur', 'change']\n\t}\n})\n// 国家区号\nconst countryCode = ref('86');\n\n// 验证码相关\nconst codeText = ref('获取验证码');\nconst canGetCode = ref(true);\n//-------\n// 账号登录表单\nconst accountForm = reactive({\n\tusername: '',\n\tpassword: '',\n});\n\n// 协议同意\nconst isAgreeProtocol = ref(false);\nconst isDisabledloginButton = computed(() => {\n\treturn !isAgreeProtocol.value;\n});\n\n// 登录按钮是否禁用\nfunction ChangeIsAgreeProtocol(){\n\tisAgreeProtocol.value = !isAgreeProtocol.value;\n}\nfunction onSubmit(e:any){\n\tconsole.log(\"onSubmit:\",e);\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.login-container {\n  height: 100%;\n  min-height: 100vh;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  position: relative;\n  // overflow: hidden;\n}\n\n.gradient-circle {\n  position: absolute;\n\n  border-radius: 200rpx;\n\n\n\n\n  background: radial-gradient(circle, #33a1fd 0%, #0062e6 70%);\n  background-color: #33a1fd;\n  /* 备用背景颜色 */\n  z-index: 0;\n}\n\n/* 添加调试样式 */\n.gradient-circle::after {\n  // content: '调试: 渐变球';\n  // color: red;\n  font-size: 12px;\n  position: absolute;\n  top: 0;\n  left: 0;\n}\n\n.top-right {\n  top: -80rpx;\n  right: -30rpx;\n  width: 300rpx;\n  /* 调整宽度 */\n  height: 300rpx;\n  /* 调整高度 */\n  opacity: 0.08;\n}\n\n.bottom-left {\n  bottom: -60rpx;\n  left: -60rpx;\n  width: 280rpx;\n  /* 调整宽度 */\n  height: 280rpx;\n  /* 调整高度 */\n  opacity: 0.1;\n}\n\n\n.logo-title {\n  text-align: center;\n  margin: 10rpx 0 30rpx;\n}\n\n.title-text {\n  font-size: 58rpx;\n  font-weight: bold;\n  color: $fui-color-primary;\n  //   font-family: ;\n}\n\n.form-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  width: 100%;\n  height: 70%;\n\n  .login-form-container {\n    // width: 100%;\n    padding: 0 16rpx;\n    display: flex;\n    min-width: 580rpx;\n  }\n\n  .form-login-tabs {}\n}\n\n.agreement {\n  display: flex;\n  align-items: flex-start;\n  margin-top: 24rpx;\n  color: $fui-color-minor;\n\n  .agreement-row {\n    align-items: center;\n  }\n}\n\n.agreement-text {\n  font-size: 24rpx;\n}\n\n.agreement-link {\n  color: $fui-color-primary;\n  text-decoration: none;\n  font-weight: 500;\n}\n\n.footer {\n  text-align: center;\n  padding: 16rpx;\n  color: $fui-color-minor;\n  font-size: $fui-input-size;\n  margin-top: 20rpx;\n  // font-family: var(--font-content);\n}\n</style>\n"]}