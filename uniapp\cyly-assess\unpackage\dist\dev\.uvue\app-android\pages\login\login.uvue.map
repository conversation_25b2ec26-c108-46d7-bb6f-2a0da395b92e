{"version": 3, "sources": ["pages/login/login.uvue"], "names": [], "mappings": "AAyEC,OAAO,YAAY,MAAM,oDAAoD,CAAC;AAC9E,OAAO,SAAS,MAAM,4CAA4C,CAAC;AACnE,OAAO,MAAM,MAAM,sCAAsC,CAAC;AAC1D,OAAO,OAAO,MAAM,wCAAwC,CAAC;AAC7D,OAAO,QAAQ,MAAM,0CAA0C,CAAC;AAChE,OAAO,SAAS,MAAM,4CAA4C,CAAC;AACnE,OAAO,OAAO,MAAM,wCAAwC,CAAC;AAC7D,OAAO,WAAW,MAAM,gDAAgD,CAAC;AACzE,OAAO,EAAE,gBAAgB,EAAE,MAAM,0CAA0C,CAAC;AAC5E,OAAO,KAAK,MAAM,+BAA+B,CAAC;AAClD,OAAO,SAAS,MAAM,yCAAyC,CAAC;AAChE,OAAO,EAAE,cAAc,EAAE,MAAM,SAAS,CAAC;AACzC,OAAO,EAAE,oBAAoB,EAAE,eAAe,EAAE,mBAAmB,EAAE,YAAY,EAAE,YAAY,EAAE,MAAM,iCAAiC,CAAA;AACxI,OAAO,EAAE,uBAAuB,EAAE,MAAM,KAAK,CAAA;;;;;;;QArB7C,MAAM,UAAU,GAAG,EAAA,mBAAA,EAAA,IAAA,oBAAA,CAAA,YAAA,EAAA,wBAAA,EAAA,EAAA,EAAA,CAAA,CAAA;YAClB,WAAW,EAAE,IAAI;YACjB,IAAI,EAAE,EAAE;YACR,MAAM,EAAE,EAAE;YACV,SAAS,EAAE,EAAE;YACb,IAAI,EAAE,QAAQ;SACd,iBAAA,CAAA;QACD,OAAO;QAgBP,MAAM,SAAS,GAAG,GAAG,CAAC,uBAAuB,GAAG,IAAI,EAAE,IAAI,CAAC,CAAA;QAC3D,SAAS,MAAM,CAAC,KAAK,EAAG,MAAM;YAC7B,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAI,EAAA,+BAAC,CAAC,CAAA;YAC5B,MAAM,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC;YAC1B,IAAI,CAAC,IAAI,IAAI,EAAE;gBACd,wBAAwB;gBACxB,CAAC,CAAC,WAAW,CAAC,WAAW,EAAE,KAAK,EAAE;oBACjC,OAAO;wBACN,OAAO,CAAC,GAAG,CAAC,SAAQ,EAAA,+BAAC,CAAC,CAAC;oBACxB,CAAC;oBACD,IAAI,CAAC,GAAG;wBACP,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,GAAE,EAAA,gCAAC,CAAC,CAAC;wBAC1B,yBAAyB;wBACzB,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,KAAI,EAAA,gCAAC,CAAC,CAAC;oBAC/B,CAAC;iBACD,IAAI,mBAAmB,CAAC,CAAA;aACzB;QACF,CAAC;QACD,QAAQ;QACR,MAAM,QAAQ,GAAG,GAAG,CAAC;YACpB,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC,EAAE;YACxB,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,EAAE;SACvB,CAAC,CAAC;QAEH,WAAW;QACX,MAAM,OAAO,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;QACvB,SAAS,eAAe,CAAC,CAAC,EAAG,gBAAgB;YAC5C,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAAE,CAAA,EAAA,gCAAC,CAAC,CAAC;YACnC,IAAI,CAAC,CAAC,KAAK,IAAK,IAAI,EAAE;gBACrB,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,IAAI,MAAM,CAAC;aAClC;QACF,CAAC;QACD,UAAU;QACV,MAAM,UAAU,GAAG,QAAQ,CAAC,cAAc,EAAE;YAC3C,KAAK,EAAE,EAAE;YACT,IAAI,EAAE,EAAE;SACR,mBAAC,CAAA;QACF,OAAO;QACP,MAAM,UAAU,GAAG,GAAG,CAAC,YAAY,EAAE;YACpC,IAAI,EAAE,OAAO;YACb,QAAQ,EAAE,IAAI;YACd,OAAO,EAAE,WAAW;SACpB,iBAAC,CAAA;QACF,MAAM,SAAS,GAAG,GAAG,CAAC,YAAY,EAAE;YACnC,IAAI,EAAE,QAAQ;YACd,QAAQ,EAAE,IAAI;YACd,OAAO,EAAE,QAAQ;SACjB,iBAAC,CAAA;QACF,OAAO;QACP,MAAM,WAAW,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;QAE9B,QAAQ;QACR,MAAM,QAAQ,GAAG,GAAG,CAAC,OAAO,CAAC,CAAC;QAC9B,MAAM,UAAU,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;QAC7B,SAAS;QACT,SAAS;QACT,MAAM,WAAW,GAAG,QAAQ,CAAC;YAC5B,QAAQ,EAAE,EAAE;YACZ,QAAQ,EAAE,EAAE;SACZ,CAAC,CAAC;QAEH,OAAO;QACP,MAAM,eAAe,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;QACnC,MAAM,qBAAqB,GAAG,QAAQ,CAAC,YAAG,EAAE;YAC3C,OAAO,CAAC,eAAe,CAAC,KAAK,CAAC;QAC/B,CAAC,CAAC,CAAC;QAEH,WAAW;QACX,SAAS,qBAAqB;YAC7B,eAAe,CAAC,KAAK,GAAG,CAAC,eAAe,CAAC,KAAK,CAAC;QAChD,CAAC;QACD,SAAS,QAAQ,CAAC,CAAC,EAAG,GAAG;YACxB,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE,CAAA,EAAA,gCAAC,CAAC,CAAC;QAC7B,CAAC;;;mBA/JE,kBAAiC,CAAA,MAAA,EAAA,QAAA,CAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,CAAA,EAAA;gBAEjC,WAAA,CAAA,KAAA,CAAA,YAA+C,CAAA,CAAA;gBAE/C,kBAAA,CAAiD,MAAA,EAAA,QAAA,CAAA,EAA3C,KAAK,EAAC,2BAAA,EAA6B,CAAA,CAAA;gBAGzC,kBAAA,CA+CO,MAAA,EAAA,QAAA,CAAA,EA/CD,KAAK,EAAC,6BAAgB,EAAA,CAAA,CAAA;kCAE1B,CAAA,MAEO,EAAA,QAAA,CAAA,EAAA,KAFD,EAAK,gBAAa,EAAA,CAAA,EAAA;sCACtB,CAAA,MAAkC,EAAA,QAAA,CAAA,EAAA,KAA5B,EAAK,YAAC,EAAY,CAAA,EAAA;;qBAG1B,CAAA;+BAAgB,CAAA,KAAA,CAAA,OAAA,CAAiB,EAAA,QAAA,CAAA;wBAAE,KAAI,EAAE,iBAAQ;wBAAG,IAAA,EAAK,KAAE,CAAK,QAAA,CAAA;wBAAE,KAAA,EAAM,KAAA;wBAAE,MAAA,EAAO,EAAE;wBAChF,OAAA,EAAM,KAAE,CAAA,OAAA,CAAA;;qBACX,CAAA,EAAA,IAAA,EAAA,CAAA,CAAA,WAuBO,EAAA,CAAA,MAAA,EAAA,SAvBI,CAAA,CAAC;sCAEU,CAAA,MAAA,EAAA,QAAA,CAAA,EAAA,KAAA,EAAA,sBAAA,EAAA,CAAA,EAAA;6BAApB,CAAA,OAAA,CAAA,KAeQ,CAAA;0DADE,EAAA,QAAA,CAAA,EAAA,GAAA,EAAA,CAAA,EAAA,CAAA,EAAA,QAAA,CAAA;uCAZR,EAAA,WAYQ,CAAA,IAAA,GAAA,EAAA,CAAA,EAAA,CAAA;+CAZG,CAAA,KAAA,CAAA,KAAW,CAAA,EAAA,QAAA,CAAA;wCAAf,OAAI,EAAA,WAAW;wCAAE,GAAA,EAAA,SAAU;wCAAU,UAAA,EAAA,QAAa;qDAAU,EAAA,EAAA;;;;+CACjE,EAAA,WAIY,CAAA,IAAA,GAAA,EAAA,CAAA,EAAA,CAAA;uDAJK,CAAA,KAAO,CAAA,SAAA,CAAA,EAAA,QAAA,CAAA;gDAAE,KAAI,EAAE,OAAA;;;uDAC9B,EAAA,WAEY,CAAA,IAAA,GAAA,EAAA,CAAA,EAAA,CAAA;+DAFG,CAAA,KAAO,CAAA,QAAA,CAAA,EAAA,QAAA,CAAA;wDAAE,GAAA,EAAA,OAAI;wDAAmB,MAAA,EAAA,GAAW,EAAX,GAAW,MAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA;wDAAE,WAAQ,EAAA,EAAA;wDAAG,IAAA,EAAM,EAAE;wDAAK,MAAA,EAAA,EAAS;wDAC3F,SAAK,EAAA,EAAA;wDAAS,IAAA,EAAA,QAAY;;;;;;;6CAG9B,CAAA,EAAA,CAAA,CAAA,WAKY,EAAA,CAAA,MAAA,CAAA,CAAA;uDALG,CAAA,KAAM,CAAA,SAAA,CAAA,EAAA,QAAA,CAAA;gDAAC,GAAA,EAAK,MAAC;gDAAQ,KAAI,EAAE,MAAA;;;uDACxC,EAAA,WAGY,CAAA,IAAA,GAAA,EAAA,CAAA,EAAA,CAAA;+DAHI,CAAA,KAAE,CAAA,QAAM,CAAA,EAAA,QAAA,CAAA;wDAAU,MAAA,EAAA,GAAW,EAAX,GAAW,MAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA;wDAAE,WAAQ,EAAA,EAAA;wDAAG,IAAA,EAAM,EAAE;wDAAK,MAAA,EAAA,EAAS;wDAAM,SAAK,EAAA,EAAA;wDACzF,IAAA,EAAA,QAAY;;;;;;;;;;;;;6BAQpB,CAAA,CAAA;0DAEA,EAAA,QAFc,CAAA,EAAA,GAEd,EAAA,CAAA,EAAA,CAAA,EAAA,QAAA,CAAA;;;;qBAIF,CAAA;sCASY,CAAA,MAAA,EAAA,QAAA,CAAA,EAAA,KAAA,EAAA,WAAA,EAAA,CAAA,EAAA;mCARO,CAAA,KAAA,CAAA,MAAQ,CAAA,EAAA,QAAA,CAAA;4BAAC,OAAM,EAAA,QAAA;;;mCAE9B,EAAA,WACiD,CAAA,IAAA,GAAA,EAAA,CAAA,EAAA,CAAA;2CAD7B,CAAA,KAAA,CAAA,WAAoB,CAAA,EAAA,QAAA,CAAA;oCAAE,KAAA,EAAO,oBAAE;oCAAiB,OAAA,EAAA,KAAa,CAAA,eAAM,CAAA;oCAAE,YAAY,EAAA,MAAI;oCACtG,UAAQ,EAAA,IAAA;;iCACX,CAAA,EAAA,IAAA,EAAA,CAAA,CAAA,WAA2C,EAAA,CAAA,SAAA,CAAA,CAAA;gCAC3C,kBAAA,CAA2D,MAAA,EAAA,QAAA,CAAA,EAArD,KAAK,EAAC,gBAAA,EAAA,CAAA,EAAA,SAAA,CAA+B;gCAC3C,kBAAA,CAAqC,MAAA,EAAA,QAAA,CAAA,EAA/B,KAAK,EAAC,+BAAkB,EAAA,CAAA,EAAA,UAAA,CAAA;gCAC9B,kBAAA,CAAyD,MAAA,EAAA,QAAA,CAAA,EAAnD,KAAK,EAAC,gBAAA,EAAA,CAAA,EAAA,GAAA,CAAA;;;;;;iBAOlB,CAAA;kCACqD,CAAA,MAAA,EAAA,QAAA,CAAA,EAAA,KAAvC,EAAA,QAAK,EAAA,CAAA,EAAA", "sourcesContent": ["<template>\r\n  <view class=\"login-container\">\r\n    <fui-status-bar></fui-status-bar>\r\n    <!-- 右上角渐变球 -->\r\n    <view class=\"gradient-circle top-right\"></view>\r\n    <!-- 左下角渐变球 -->\r\n    <view class=\"gradient-circle bottom-left\"></view>\r\n    <!-- 切换登录 -->\r\n    <!-- 登录表单 -->\r\n    <view class=\"form-container\">\r\n      <!-- Logo/标题 -->\r\n      <view class=\"logo-title\">\r\n        <text class=\"title-text\">登录</text>\r\n      </view>\r\n      <!-- 手机号登录 -->\r\n      <fui-tabs class=\"form-login-tabs\" :tabs=\"tabsList\" :short=\"false\" center :current=\"current\"\r\n        @change=\"handleChangeTab\"></fui-tabs>\r\n      <view class=\"login-form-container\">\r\n\r\n        <block v-if=\"current === 0\">\r\n          <!-- TODO: 要加前缀 +86 fui-dropdown-menu -->\r\n          <uForm ref=\"loginForm\" @submited=\"onSubmit\" triggerChange v-model=\"phoneModel\">\r\n            <uFromItem field=\"phone\" :rule=\"phoneRules\">\r\n              <fui-input ref=\"phone\" @blur=\"onBlur('phone')\" inputBorder :size=\"28\" :radius=\"12\" :marginTop=\"16\"\r\n                type=\"number\" placeholder=\"请输入手机号\" v-model=\"phoneModel.phone as string\">\r\n              </fui-input>\r\n            </uFromItem>\r\n            <uFromItem ref=\"code\" field=\"code\" :rule=\"codeRules\">\r\n              <fui-input @blur=\"onBlur('code')\" inputBorder :size=\"28\" :radius=\"12\" :marginTop=\"16\" type=\"number\"\r\n                placeholder=\"请输入验证码\" v-model=\"phoneModel.code as string\">\r\n\r\n              </fui-input>\r\n            </uFromItem>\r\n          </uForm>\r\n        </block>\r\n\r\n        <!-- 账号登录 -->\r\n        <block v-else>\r\n          2\r\n        </block>\r\n      </view>\r\n\r\n      <!-- 协议同意 -->\r\n      <view class=\"agreement\">\r\n        <fui-row justify=\"center\" class=\"agreement-row\">\r\n          <!-- 必须要这样写，转kt的时候不这样写会出现问题 -->\r\n          <fui-checkbox class=\"agreement-checkbox\" :checked=\"isAgreeProtocol\" borderRadius=\"8rpx\" :scaleRatio=\"0.78\"\r\n            @change=\"ChangeIsAgreeProtocol\"></fui-checkbox>\r\n          <text class=\"agreement-text\">我已阅读并同意</text>\r\n          <text class=\"agreement-text agreement-link\">《用户服务条款》</text>\r\n          <text class=\"agreement-text\">和</text>\r\n          <text class=\"agreement-text agreement-link\">《隐私协议》</text>\r\n        </fui-row>\r\n      </view>\r\n\r\n      <!-- 登录按钮 -->\r\n    </view>\r\n    <!-- 底部 -->\r\n    <view class=\"footer\">\r\n      <fui-footer text=\"by@海南长养乔智能科技有限责任公司\"></fui-footer>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup lang=\"uts\">\r\n\tconst inputStyle = {\r\n\t\tinputBorder: true,\r\n\t\tsize: 28,\r\n\t\tradius: 12,\r\n\t\tmarginTop: 16,\r\n\t\ttype: \"number\"\r\n\t}\r\n\t// 引入组件\r\n\timport fuiStatusBar from \"@/components/firstui/fui-status-bar/fui-status-bar\";\r\n\timport fuiFooter from \"@/components/firstui/fui-footer/fui-footer\";\r\n\timport fuiRow from \"@/components/firstui/fui-row/fui-row\";\r\n\timport fuiTabs from \"@/components/firstui/fui-tabs/fui-tabs\";\r\n\timport fuiInput from \"@/components/firstui/fui-input/fui-input\";\r\n\timport fuiButton from \"@/components/firstui/fui-button/fui-button\";\r\n\timport fuiIcon from \"@/components/firstui/fui-icon/fui-icon\";\r\n\timport fuiCheckbox from \"@/components/firstui/fui-checkbox/fui-checkbox\";\r\n\timport { FuiTabsItemParam } from \"@/components/firstui/fui-types/index.uts\";\r\n\timport uForm from \"@/components/uc/u-form/u-form\";\r\n\timport uFromItem from \"@/components/uc/u-from-item/u-from-item\";\r\n\timport { phoneModelType } from \"./types\";\r\n\timport { FormItemVerifyResult, FormValidResult, FormValidResultItem, FormItemData, FormItemRule } from \"@/components/uc/types/index.uts\"\r\n\timport { ComponentPublicInstance } from 'vue'\r\n\r\n\tconst loginForm = ref<ComponentPublicInstance | null>(null)\r\n\tfunction onBlur(field : string) {\r\n\t\tconsole.log(\"field:\", field)\r\n\t\tconst f = loginForm.value;\r\n\t\tif (f != null) {\r\n\t\t\t// 使用 $callMethod 调用组件方法\r\n\t\t\tf.$callMethod('validItem', field, {\r\n\t\t\t\tsuccess() {\r\n\t\t\t\t\tconsole.log(\"success\");\r\n\t\t\t\t},\r\n\t\t\t\tfail(res) {\r\n\t\t\t\t\tconsole.log(\"fail:\", res);\r\n\t\t\t\t\t// 移除不支持的 $refs[field] 语法\r\n\t\t\t\t\tconsole.log(\"field :\", field);\r\n\t\t\t\t}\r\n\t\t\t} as FormValidResultItem)\r\n\t\t}\r\n\t}\r\n\t// 标签页数据\r\n\tconst tabsList = ref([\r\n\t\t{ name: '手机号登录', id: 0 },\r\n\t\t{ name: '账号登录', id: 1 },\r\n\t]);\r\n\r\n\t// 当前选中的标签页\r\n\tconst current = ref(0);\r\n\tfunction handleChangeTab(e : FuiTabsItemParam) {\r\n\t\tconsole.log(\"handleChangeTab:\", e);\r\n\t\tif (e.index !== null) {\r\n\t\t\tcurrent.value = e.index as number;\r\n\t\t}\r\n\t}\r\n\t// 手机号登录表单\r\n\tconst phoneModel = reactive<phoneModelType>({\r\n\t\tphone: '',\r\n\t\tcode: ''\r\n\t})\r\n\t// 表单配置\r\n\tconst phoneRules = ref<FormItemRule>({\r\n\t\ttype: 'phone',\r\n\t\trequired: true,\r\n\t\tmessage: '请输入正确的手机号'\r\n\t})\r\n\tconst codeRules = ref<FormItemRule>({\r\n\t\ttype: 'number',\r\n\t\trequired: true,\r\n\t\tmessage: '请输入验证码'\r\n\t})\r\n\t// 国家区号\r\n\tconst countryCode = ref('86');\r\n\r\n\t// 验证码相关\r\n\tconst codeText = ref('获取验证码');\r\n\tconst canGetCode = ref(true);\r\n\t//-------\r\n\t// 账号登录表单\r\n\tconst accountForm = reactive({\r\n\t\tusername: '',\r\n\t\tpassword: '',\r\n\t});\r\n\r\n\t// 协议同意\r\n\tconst isAgreeProtocol = ref(false);\r\n\tconst isDisabledloginButton = computed(() => {\r\n\t\treturn !isAgreeProtocol.value;\r\n\t});\r\n\r\n\t// 登录按钮是否禁用\r\n\tfunction ChangeIsAgreeProtocol() {\r\n\t\tisAgreeProtocol.value = !isAgreeProtocol.value;\r\n\t}\r\n\tfunction onSubmit(e : any) {\r\n\t\tconsole.log(\"onSubmit:\", e);\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.login-container {\r\n  height: 100%;\r\n  min-height: 100vh;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  position: relative;\r\n  // overflow: hidden;\r\n}\r\n\r\n.gradient-circle {\r\n  position: absolute;\r\n\r\n  border-radius: 200rpx;\r\n\r\n\r\n\r\n\r\n  background: radial-gradient(circle, #33a1fd 0%, #0062e6 70%);\r\n  background-color: #33a1fd;\r\n  /* 备用背景颜色 */\r\n  z-index: 0;\r\n}\r\n\r\n/* 添加调试样式 */\r\n.gradient-circle::after {\r\n  // content: '调试: 渐变球';\r\n  // color: red;\r\n  font-size: 12px;\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n}\r\n\r\n.top-right {\r\n  top: -80rpx;\r\n  right: -30rpx;\r\n  width: 300rpx;\r\n  /* 调整宽度 */\r\n  height: 300rpx;\r\n  /* 调整高度 */\r\n  opacity: 0.08;\r\n}\r\n\r\n.bottom-left {\r\n  bottom: -60rpx;\r\n  left: -60rpx;\r\n  width: 280rpx;\r\n  /* 调整宽度 */\r\n  height: 280rpx;\r\n  /* 调整高度 */\r\n  opacity: 0.1;\r\n}\r\n\r\n\r\n.logo-title {\r\n  text-align: center;\r\n  margin: 10rpx 0 30rpx;\r\n}\r\n\r\n.title-text {\r\n  font-size: 58rpx;\r\n  font-weight: bold;\r\n  color: $fui-color-primary;\r\n  //   font-family: ;\r\n}\r\n\r\n.form-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  width: 100%;\r\n  height: 70%;\r\n\r\n  .login-form-container {\r\n    // width: 100%;\r\n    padding: 0 16rpx;\r\n    display: flex;\r\n    min-width: 580rpx;\r\n  }\r\n\r\n  .form-login-tabs {}\r\n}\r\n\r\n.agreement {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  margin-top: 24rpx;\r\n  color: $fui-color-minor;\r\n\r\n  .agreement-row {\r\n    align-items: center;\r\n  }\r\n}\r\n\r\n.agreement-text {\r\n  font-size: 24rpx;\r\n}\r\n\r\n.agreement-link {\r\n  color: $fui-color-primary;\r\n  text-decoration: none;\r\n  font-weight: 500;\r\n}\r\n\r\n.footer {\r\n  text-align: center;\r\n  padding: 16rpx;\r\n  color: $fui-color-minor;\r\n  font-size: $fui-input-size;\r\n  margin-top: 20rpx;\r\n  // font-family: var(--font-content);\r\n}\r\n</style>"]}