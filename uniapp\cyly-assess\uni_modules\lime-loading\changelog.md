## 0.1.7（2025-05-16）
- fix: 修复 uniappx ios 初始化尺寸为0导致不生效的问题
## 0.1.6（2025-04-21）
- fix: uniappx 鸿蒙next 尺寸从0变化时无法渲染
## 0.1.5（2025-04-21）
- feat: 兼容uniappx 鸿蒙next
## 0.1.4（2025-04-10）
- feat: 增加暂停
## 0.1.3（2025-03-12）
- feat: uniappx app使用`requestAnimationFrame`
## 0.1.2（2025-02-13）
- fix: 修复因uniapp x ios监听元素不生效导致不生效问题
## 0.1.1（2025-02-09）
- chore: 去掉多余console
## 0.1.0（2025-02-09）
- feat: 重构useLoading
## 0.0.9（2025-01-14）
- feat: useLoading 增加color ref
## 0.0.8（2024-12-24）
- feat: 改用监听尺寸方式
## 0.0.7（2024-12-18）
- fix: 修复vue2 微信小程序不显示加载图标的问题
## 0.0.6（2024-12-18）
- chore: 更新文档
## 0.0.5（2024-09-30）
- fix: vue2 app 使用渐变
## 0.0.4（2024-09-29）
- chore: 非uvue app size使用css变量
## 0.0.3（2024-09-25）
- fix: useLoading
## 0.0.2（2024-04-05）
- feat: 支持 uniapp x ios(app-js)
## 0.0.1（2023-10-13）
- 首次上传
