export type FormItemType = 'input' | 'textarea' | 'select' | 'radio' | 'checkbox' | 'switch' | 'date' | 'time' | 'datetime' | 'upload' | 'custom';
export type FormItem = {
    label: string;
    name: string;
    type: FormItemType;
    value?: any;
    placeholder?: string;
    options?: Array<{
        label: string;
        value: any;
    }>;
    rules?: Array<{
        required?: boolean;
        message?: string;
        trigger?: string;
    }>;
    disabled?: boolean;
    readonly?: boolean;
    clearable?: boolean;
    showWordLimit?: boolean;
    maxLength?: number;
    minLength?: number;
    step?: number;
    min?: number | string;
    max?: number | string;
    format?: string;
};
export type FormItemData = {
    field: string;
    instance: ComponentPublicInstance;
};
export type FormItemRule = {
    /**
     * 字段类型
     */
    type?: 'string' | 'number' | 'boolean' | 'integer' | 'float' | 'array' | 'object' | 'enum' | 'url' | 'email' | 'phone' | 'idcard' | null;
    /**
     * 是否必填
     */
    required?: boolean | null;
    /**
     * 校验失败提示信息
     */
    message?: string | null;
    /**
     * 正则校验规则
     */
    pattern?: RegExp | null;
    /**
     * 最小长度
     */
    min?: number | null;
    /**
     * 最大长度
     */
    max?: number | null;
    /**
     * 值的长度（同时设置 min、max 和 len，以len的值为准）
     */
    len?: number | null;
    /**
     * 值的枚举值，限制值只能为此枚举数组的子项
     */
    enum?: Array<any> | null;
    /**
     * 数据转换函数，校验前先执行此函数对原始数据进行处理
     */
    transform?: ((value: any) => any) | null;
    /**
     * 自定义校验函数，在默认的校验前先执行此函数。
     *
     * 返回空文本串表示校验通过；返回其他字符串表示校验失败，且返回的字符串将作为校验失败的提示信息
     */
    valid?: ((value: any) => string) | null;
};
export type FormItemVerifyResult = {
    /**
     * 子项校验是否通过
     */
    valid: boolean;
    /**
     * 子项校验失败的提示信息
     */
    message?: string | null;
    /**
     * 子项的名称
     */
    field: string;
};
export type FormValidResult = {
    /**
     * 表单校验成功回调
     */
    success?: (() => void) | null;
    /**
     * 表单校验失败回调，会将所有校验失败的子项的错误信息作为 failResults 数组参数
     */
    fail?: ((failResults: FormItemVerifyResult[]) => void) | null;
};
export type FormValidResultItem = {
    /**
     * 表单校验成功回调
     */
    success?: (() => void) | null;
    /**
     * 表单校验失败回调，会将所有校验失败的子项的错误信息作为 failResults 数组参数
     */
    fail?: ((failResults?: FormItemVerifyResult) => void) | null;
};
/**
 * Toast 提示类型
 */
export type ToastType = 'success' | 'error' | 'warning' | 'info' | 'default';
/**
 * Toast 位置
 */
export type ToastPosition = 'top' | 'bottom';
/**
 * Toast 配置选项
 */
export type ToastOptions = {
    /**
     * 消息内容
     */
    message: string;
    /**
     * 提示类型
     */
    type?: ToastType;
    /**
     * 自定义背景色
     */
    backgroundColor?: string;
    /**
     * 自定义文字颜色
     */
    textColor?: string;
    /**
     * 图标
     */
    icon?: string;
    /**
     * 是否显示图标
     */
    showIcon?: boolean;
    /**
     * 是否可关闭
     */
    closable?: boolean;
    /**
     * 自动关闭时间（毫秒）
     */
    duration?: number;
    /**
     * 位置
     */
    position?: ToastPosition;
    /**
     * 距离顶部的距离
     */
    top?: number;
    /**
     * 自定义样式
     */
    customStyle?: UTSJSONObject;
};
//# sourceMappingURL=index.uts.map