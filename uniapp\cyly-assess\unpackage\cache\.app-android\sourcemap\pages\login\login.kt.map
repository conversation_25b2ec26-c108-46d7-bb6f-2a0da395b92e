{"version": 3, "sources": ["pages/login/login.uvue", "pages/login/login.uvue?type=page"], "sourcesContent": ["<template>\n  <view class=\"login-container\">\n    <fui-status-bar></fui-status-bar>\n    <!-- 右上角渐变球 -->\n    <view class=\"gradient-circle top-right\"></view>\n    <!-- 左下角渐变球 -->\n    <view class=\"gradient-circle bottom-left\"></view>\n    <!-- 切换登录 -->\n    <!-- 登录表单 -->\n    <view class=\"form-container\">\n      <!-- Logo/标题 -->\n      <view class=\"logo-title\">\n        <text class=\"title-text\">登录</text>\n      </view>\n      <!-- 手机号登录 -->\n      <fui-tabs class=\"form-login-tabs\" :tabs=\"tabsList\" :short=\"false\" center :current=\"current\"\n        @change=\"handleChangeTab\"></fui-tabs>\n      <view class=\"login-form-container\">\n\n        <block v-if=\"current === 0\">\n          <!-- TODO: 要加前缀 +86 fui-dropdown-menu -->\n          <uForm ref=\"loginForm\" @submited=\"onSubmit\" triggerChange v-model=\"phoneModel\">\n            <uFromItem field=\"phone\" :rule=\"{\n              required: true,\n              type: 'phone',\n              message: '请输入正确的手机号'\n            }\">\n              <fui-input @blur=\"onBlur('phone')\" inputBorder :size=\"28\" :radius=\"12\" :marginTop=\"16\" type=\"number\"\n                placeholder=\"请输入手机号\" v-model=\"phoneModel.phone as string\">\n              </fui-input>\n            </uFromItem>\n            <uFromItem field=\"code\" :rule=\"{\n              required: true,\n              message: '请输入验证码'\n            }\">\n              <fui-input @blur=\"onBlur('code')\" inputBorder :size=\"28\" :radius=\"12\" :marginTop=\"16\" type=\"number\"\n                placeholder=\"请输入验证码\" v-model=\"phoneModel.code as string\">\n\n              </fui-input>\n            </uFromItem>\n          </uForm>\n        </block>\n\n        <!-- 账号登录 -->\n        <block v-else>\n          2\n        </block>\n      </view>\n\n      <!-- 协议同意 -->\n      <view class=\"agreement\">\n        <fui-row justify=\"center\" class=\"agreement-row\">\n          <!-- 必须要这样写，转kt的时候不这样写会出现问题 -->\n          <fui-checkbox class=\"agreement-checkbox\" :checked=\"isAgreeProtocol\" borderRadius=\"8rpx\" :scaleRatio=\"0.78\"\n            @change=\"ChangeIsAgreeProtocol\"></fui-checkbox>\n          <text class=\"agreement-text\">我已阅读并同意</text>\n          <text class=\"agreement-text agreement-link\">《用户服务条款》</text>\n          <text class=\"agreement-text\">和</text>\n          <text class=\"agreement-text agreement-link\">《隐私协议》</text>\n        </fui-row>\n      </view>\n\n      <!-- 登录按钮 -->\n    </view>\n    <!-- 底部 -->\n    <view class=\"footer\">\n      <fui-footer text=\"by@海南长养乔智能科技有限责任公司\"></fui-footer>\n    </view>\n  </view>\n</template>\n\n<script setup lang=\"uts\">\n\n\nconst inputStyle = {\n  inputBorder:true,\n  size:28,\n  radius:12,\n  marginTop:16,\n  type: \"number\"\n}\n// 引入组件\nimport fuiStatusBar from \"@/components/firstui/fui-status-bar/fui-status-bar\";\nimport fuiFooter from \"@/components/firstui/fui-footer/fui-footer\";\nimport fuiRow from \"@/components/firstui/fui-row/fui-row\";\nimport fuiTabs from \"@/components/firstui/fui-tabs/fui-tabs\";\nimport fuiInput from \"@/components/firstui/fui-input/fui-input\";\nimport fuiButton from \"@/components/firstui/fui-button/fui-button\";\nimport fuiIcon from \"@/components/firstui/fui-icon/fui-icon\";\nimport fuiCheckbox from \"@/components/firstui/fui-checkbox/fui-checkbox\";\nimport { FuiTabsItemParam } from \"@/components/firstui/fui-types/index.uts\";\nimport uForm from \"@/components/uc/u-form/u-form\";\nimport uFromItem from \"@/components/uc/u-from-item/u-from-item\";\nimport { phoneModelType } from \"./types\";\nimport { FormItemVerifyResult } from \"@/components/uc/types/index.uts\"\n\nconst loginForm = ref<FormItemVerifyResult | null>(null)\nfunction onBlur(field: string){\n  console.log(\"field:\",field);\n\n  // 将 loginForm.value 赋值给局部变量以避免智能转换问题\n  const formInstance = loginForm.value\n  if (formInstance != null) {\n    // 使用 $callMethod 调用组件方法\n    formInstance.$callMethod('validItem', field, {\n      success(){\n        console.log(\"success\");\n      },\n      fail(res: FormItemVerifyResult | null){\n        console.log(\"fail:\",res);\n      }\n    })\n  }\n}\n// 标签页数据\nconst tabsList = ref([\n\t{ name: '手机号登录', id: 0 },\n\t{ name: '账号登录', id: 1 },\n]);\n\n// 当前选中的标签页\nconst current = ref(0);\nfunction handleChangeTab(e:FuiTabsItemParam){\n\tconsole.log(\"handleChangeTab:\",e);\n\tif(e.index !== null){\n\t\tcurrent.value = e.index as number;\n\t}\n}\n// 手机号登录表单\nconst phoneModel = reactive<phoneModelType>({\n\t\tphone: '',\n\t\tcode: ''\n})\n// 表单配置\nconst phoneRules = ref({\n\t'phone': {\n\t\ttype: 'string',\n\t\trequired: true,\n\t\tmessage: '请输入手机号',\n\t\ttrigger: ['blur', 'change']\n\t},\n\t'code': {\n\t\ttype: 'string',\n\t\trequired: true,\n\t\tmessage: '请输入验证码',\n\t\ttrigger: ['blur', 'change']\n\t}\n})\n// 国家区号\nconst countryCode = ref('86');\n\n// 验证码相关\nconst codeText = ref('获取验证码');\nconst canGetCode = ref(true);\n//-------\n// 账号登录表单\nconst accountForm = reactive({\n\tusername: '',\n\tpassword: '',\n});\n\n// 协议同意\nconst isAgreeProtocol = ref(false);\nconst isDisabledloginButton = computed(() => {\n\treturn !isAgreeProtocol.value;\n});\n\n// 登录按钮是否禁用\nfunction ChangeIsAgreeProtocol(){\n\tisAgreeProtocol.value = !isAgreeProtocol.value;\n}\nfunction onSubmit(e:any){\n\tconsole.log(\"onSubmit:\",e);\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.login-container {\n  height: 100%;\n  min-height: 100vh;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  position: relative;\n  // overflow: hidden;\n}\n\n.gradient-circle {\n  position: absolute;\n\n  border-radius: 200rpx;\n\n\n\n\n  background: radial-gradient(circle, #33a1fd 0%, #0062e6 70%);\n  background-color: #33a1fd;\n  /* 备用背景颜色 */\n  z-index: 0;\n}\n\n/* 添加调试样式 */\n.gradient-circle::after {\n  // content: '调试: 渐变球';\n  // color: red;\n  font-size: 12px;\n  position: absolute;\n  top: 0;\n  left: 0;\n}\n\n.top-right {\n  top: -80rpx;\n  right: -30rpx;\n  width: 300rpx;\n  /* 调整宽度 */\n  height: 300rpx;\n  /* 调整高度 */\n  opacity: 0.08;\n}\n\n.bottom-left {\n  bottom: -60rpx;\n  left: -60rpx;\n  width: 280rpx;\n  /* 调整宽度 */\n  height: 280rpx;\n  /* 调整高度 */\n  opacity: 0.1;\n}\n\n\n.logo-title {\n  text-align: center;\n  margin: 10rpx 0 30rpx;\n}\n\n.title-text {\n  font-size: 58rpx;\n  font-weight: bold;\n  color: $fui-color-primary;\n  //   font-family: ;\n}\n\n.form-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  width: 100%;\n  height: 70%;\n\n  .login-form-container {\n    // width: 100%;\n    padding: 0 16rpx;\n    display: flex;\n    min-width: 580rpx;\n  }\n\n  .form-login-tabs {}\n}\n\n.agreement {\n  display: flex;\n  align-items: flex-start;\n  margin-top: 24rpx;\n  color: $fui-color-minor;\n\n  .agreement-row {\n    align-items: center;\n  }\n}\n\n.agreement-text {\n  font-size: 24rpx;\n}\n\n.agreement-link {\n  color: $fui-color-primary;\n  text-decoration: none;\n  font-weight: 500;\n}\n\n.footer {\n  text-align: center;\n  padding: 16rpx;\n  color: $fui-color-minor;\n  font-size: $fui-input-size;\n  margin-top: 20rpx;\n  // font-family: var(--font-content);\n}\n</style>\n", null], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;YA0EA,IAAM,4BAAa,uBAAA,qBAAA,cAAA,0BAAA,EAAA,EAAA,CAAA;gBACjB,IAAA,cAAY,IAAI;gBAChB,IAAA,eAAK,EAAE;gBACP,IAAA,iBAAO,EAAE;gBACT,IAAA,oBAAU,EAAE;gBACZ,IAAA,OAAM;aACP;YAgBD,IAAM,YAAY,2BAAiC,IAAI;YACvD,IAAS,cAAO,OAAO,MAAM,EAAA;gBAC3B,QAAQ,GAAG,CAAC,UAAS,OAAI;gBAGzB,IAAM,eAAe,UAAU,KAAK;gBACpC,IAAI,gBAAgB,IAAI,EAAE;oBAExB,aAAa,aAAW,CAAC,aAAa,OAAO;wBAC3C,IAAA,UAAA,MAAO;4BACL,QAAQ,GAAG,CAAC,WAAQ;wBACtB;wBACA,IAAA,OAAA,IAAK,0BAAgC,EAAA;4BACnC,QAAQ,GAAG,CAAC,SAAQ,KAAE;wBACxB;qBACD;;YAEL;gBAhBS;YAkBT,IAAM,WAAW,IAAI;gBACpB;oBAAE,IAAA,OAAM;oBAAS,IAAA,aAAI,CAAC;iBAAE;gBACxB;oBAAE,IAAA,OAAM;oBAAQ,IAAA,aAAI,CAAC;iBAAE;aACvB;YAGD,IAAM,UAAU,IAAI,CAAC;YACrB,IAAS,uBAAgB,mBAAkB,EAAA;gBAC1C,QAAQ,GAAG,CAAC,oBAAmB,GAAA;gBAC/B,IAAG,EAAE,KAAK,IAAK,IAAI,EAAC;oBACnB,QAAQ,KAAK,GAAG,EAAE,KAAK,CAAA,EAAA,CAAI,MAAM;;YAEnC;gBALS;YAOT,IAAM,aAAa,wCACjB,QAAO,IACP,OAAM;YAGR,IAAM,aAAa,IAAI;gBACtB,YAAS;oBACR,IAAA,OAAM;oBACN,IAAA,WAAU,IAAI;oBACd,IAAA,UAAS;oBACT,IAAA,UAAS;wBAAC;wBAAQ;qBAAS;iBAC3B;gBACD,WAAQ;oBACP,IAAA,OAAM;oBACN,IAAA,WAAU,IAAI;oBACd,IAAA,UAAS;oBACT,IAAA,UAAS;wBAAC;wBAAQ;qBAAS;iBAC3B;aACD;YAED,IAAM,cAAc,IAAI;YAGxB,IAAM,WAAW,IAAI;YACrB,IAAM,aAAa,IAAI,IAAI;YAG3B,IAAM,cAAc,SAAS;gBAC5B,IAAA,WAAU;gBACV,IAAA,WAAU;aACV;YAGD,IAAM,kBAAkB,IAAI,KAAK;YACjC,IAAM,wBAAwB,SAAS,OAAA,OAAA,CAAK;gBAC3C,OAAO,CAAC,gBAAgB,KAAK;YAC9B;;YAGA,IAAS,+BAAqB;gBAC7B,gBAAgB,KAAK,GAAG,CAAC,gBAAgB,KAAK;YAC/C;gBAFS;YAGT,IAAS,gBAAS,GAAE,GAAG,EAAA;gBACtB,QAAQ,GAAG,CAAC,aAAY,GAAA;YACzB;gBAFS;;;uBAzKL,mBAAiC,QAAA,SAAA,WAAA,oBAAA;oBAEjC,YAAA;oBAEA,mBAAiD,QAAA,SAA3C,WAAM;oBAGZ,mBAsDO,QAAA,SAtDD,WAAM;uCAEV,QAEO,SAAA,WAFI,mBAAa;2CACtB,QAAkC,SAAA,WAAvB,eAAa;;yBAG1B;oCAAgB,gDAAiB,SAAE,WAAM,mBAAW,UAAK,MAAO,WAAE,WAAM,KAAA,EAAE,YAAO,IAC9E,aAAM,MAAE,0CACX,IAAA,EAAA,CAAA,EA8BO;4BAAA;4BAAA;yBA9BI;2CAEW,QAAA,SAAA,WAAA,yBAAA;sCAApB,aAsBQ,CAAA;8DADE,SAAA,SAAA,CAAA,GAAA,sBAnBR,YAmBQ,gBAAA,GAAA;2CAAA;oDAnBG,uCAAW,SAAf,aAAI,aAAa,SAAA,WAAoB,gBAAA,6BAAuB;;kEACjE,YAQY,gBAAA,GAAA;mDAAA;4DARK,+CAAO,SAAE,WAAM;;;oDAI/B,IAAA,UAAA;0EACC,YAEY,gBAAA,GAAA;2DAAA;oEAFI,kDAAQ,SAAW,YAAA,KAAA;4DAAW,OAAA;wDAAA,GAAE,iBAAQ,IAAG,UAAM,EAAE,EAAK,YAAA,EAAS,EAAM,eAAK,EAAA,EAC1F,UAAA;;;;;;;;;4DAGa,+CAAM,SAAE,WAAM;;oDAG9B,IAAA,UAAA;0EACC,YAGY,gBAAA,GAAA;2DAAA;oEAHI,kDAAQ,SAAU,YAAA,KAAA;4DAAW,OAAA;wDAAA,GAAE,iBAAQ,IAAG,UAAM,EAAE,EAAK,YAAA,EAAS,EAAM,eAAK,EAAA,EACzF,UAAA;;;;;;;;;;;;;;;;8DAUR,SAFc,SAEd,CAAA,GAAA;;;;;;;yBAIF;2CASY,QAAA,SAAA,WAAA,cAAA;wCARO,8CAAQ,SAAC,aAAM,6DAE9B,YACiD,gBAAA,GAAA;uCAAA;gDAD7B,wDAAoB,SAAE,WAAO,sBAAmB,aAAA,MAAa,kBAAQ,kBAAY,QAClG,gBAAQ,IAAA,wCACX,IAAA,EAAA,CAAA,EAA2C;wCAAA;qCAAA;oCAC3C,mBAA2D,QAAA,SAArD,WAAM,mBAAA;oCACZ,mBAAqC,QAAA,SAA/B,WAAM,kCAAkB;oCAC9B,mBAAyD,QAAA,SAAnD,WAAM,mBAAA;;;;;;qBAOlB;uCACqD,QAAA,SAAA,WAAvC,WAAK"}