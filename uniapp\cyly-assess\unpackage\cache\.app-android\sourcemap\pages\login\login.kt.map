{"version": 3, "sources": ["pages/login/login.uvue", "pages/login/login.uvue?type=page"], "sourcesContent": ["<template>\r\n  <view class=\"login-container\">\r\n    <fui-status-bar></fui-status-bar>\r\n    <!-- 右上角渐变球 -->\r\n    <view class=\"gradient-circle top-right\"></view>\r\n    <!-- 左下角渐变球 -->\r\n    <view class=\"gradient-circle bottom-left\"></view>\r\n    <!-- 切换登录 -->\r\n    <!-- 登录表单 -->\r\n    <view class=\"form-container\">\r\n      <!-- Logo/标题 -->\r\n      <view class=\"logo-title\">\r\n        <text class=\"title-text\">登录</text>\r\n      </view>\r\n      <!-- 手机号登录 -->\r\n      <fui-tabs class=\"form-login-tabs\" :tabs=\"tabsList\" :short=\"false\" center :current=\"current\"\r\n        @change=\"handleChangeTab\"></fui-tabs>\r\n      <view class=\"login-form-container\">\r\n\r\n        <block v-if=\"current === 0\">\r\n          <!-- TODO: 要加前缀 +86 fui-dropdown-menu -->\r\n          <uForm ref=\"loginForm\" @submited=\"onSubmit\" triggerChange v-model=\"phoneModel\">\r\n            <uFromItem field=\"phone\" :rule=\"{\r\n              required: true,\r\n              type: 'phone',\r\n              message: '请输入正确的手机号'\r\n            }\">\r\n              <fui-input @blur=\"onBlur('phone')\" inputBorder :size=\"28\" :radius=\"12\" :marginTop=\"16\" type=\"number\"\r\n                placeholder=\"请输入手机号\" v-model=\"phoneModel.phone as string\">\r\n              </fui-input>\r\n            </uFromItem>\r\n            <uFromItem field=\"code\" :rule=\"{\r\n              required: true,\r\n              message: '请输入验证码'\r\n            }\">\r\n              <fui-input @blur=\"onBlur('code')\" inputBorder :size=\"28\" :radius=\"12\" :marginTop=\"16\" type=\"number\"\r\n                placeholder=\"请输入验证码\" v-model=\"phoneModel.code as string\">\r\n\r\n              </fui-input>\r\n            </uFromItem>\r\n          </uForm>\r\n        </block>\r\n\r\n        <!-- 账号登录 -->\r\n        <block v-else>\r\n          2\r\n        </block>\r\n      </view>\r\n\r\n      <!-- 协议同意 -->\r\n      <view class=\"agreement\">\r\n        <fui-row justify=\"center\" class=\"agreement-row\">\r\n          <!-- 必须要这样写，转kt的时候不这样写会出现问题 -->\r\n          <fui-checkbox class=\"agreement-checkbox\" :checked=\"isAgreeProtocol\" borderRadius=\"8rpx\" :scaleRatio=\"0.78\"\r\n            @change=\"ChangeIsAgreeProtocol\"></fui-checkbox>\r\n          <text class=\"agreement-text\">我已阅读并同意</text>\r\n          <text class=\"agreement-text agreement-link\">《用户服务条款》</text>\r\n          <text class=\"agreement-text\">和</text>\r\n          <text class=\"agreement-text agreement-link\">《隐私协议》</text>\r\n        </fui-row>\r\n      </view>\r\n\r\n      <!-- 登录按钮 -->\r\n    </view>\r\n    <!-- 底部 -->\r\n    <view class=\"footer\">\r\n      <fui-footer text=\"by@海南长养乔智能科技有限责任公司\"></fui-footer>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup lang=\"uts\">\r\n\r\n\r\nconst inputStyle = {\r\n  inputBorder:true,\r\n  size:28,\r\n  radius:12,\r\n  marginTop:16,\r\n  type: \"number\"\r\n}\r\n// 引入组件\r\nimport fuiStatusBar from \"@/components/firstui/fui-status-bar/fui-status-bar\";\r\nimport fuiFooter from \"@/components/firstui/fui-footer/fui-footer\";\r\nimport fuiRow from \"@/components/firstui/fui-row/fui-row\";\r\nimport fuiTabs from \"@/components/firstui/fui-tabs/fui-tabs\";\r\nimport fuiInput from \"@/components/firstui/fui-input/fui-input\";\r\nimport fuiButton from \"@/components/firstui/fui-button/fui-button\";\r\nimport fuiIcon from \"@/components/firstui/fui-icon/fui-icon\";\r\nimport fuiCheckbox from \"@/components/firstui/fui-checkbox/fui-checkbox\";\r\nimport { FuiTabsItemParam } from \"@/components/firstui/fui-types/index.uts\";\r\nimport uForm from \"@/components/uc/u-form/u-form\";\r\nimport uFromItem from \"@/components/uc/u-from-item/u-from-item\";\r\nimport { phoneModelType } from \"./types\";\r\nimport { FormItemVerifyResult, FormValidResultItem } from \"@/components/uc/types/index.uts\"\r\nimport { ComponentPublicInstance } from 'vue'\r\n\r\nconst loginForm = ref<ComponentPublicInstance | null>(null)\r\nfunction onBlur(field: string){\r\n  console.log(\"field:\",field);\r\n\r\n  // 将 loginForm.value 赋值给局部变量以避免智能转换问题\r\n  const formInstance = loginForm.value\r\n  if (formInstance != null) {\r\n    // 使用 $callMethod 调用组件方法\r\n    formInstance.$callMethod('validItem', field, {\r\n      success(){\r\n        console.log(\"success\");\r\n      },\r\n      fail(res: FormItemVerifyResult | null){\r\n        console.log(\"fail:\",res);\r\n      }\r\n    })\r\n  }\r\n}\r\n// 标签页数据\r\nconst tabsList = ref([\r\n\t{ name: '手机号登录', id: 0 },\r\n\t{ name: '账号登录', id: 1 },\r\n]);\r\n\r\n// 当前选中的标签页\r\nconst current = ref(0);\r\nfunction handleChangeTab(e:FuiTabsItemParam){\r\n\tconsole.log(\"handleChangeTab:\",e);\r\n\tif(e.index !== null){\r\n\t\tcurrent.value = e.index as number;\r\n\t}\r\n}\r\n// 手机号登录表单\r\nconst phoneModel = reactive<phoneModelType>({\r\n\t\tphone: '',\r\n\t\tcode: ''\r\n})\r\n// 表单配置\r\nconst phoneRules = ref({\r\n\t'phone': {\r\n\t\ttype: 'string',\r\n\t\trequired: true,\r\n\t\tmessage: '请输入手机号',\r\n\t\ttrigger: ['blur', 'change']\r\n\t},\r\n\t'code': {\r\n\t\ttype: 'string',\r\n\t\trequired: true,\r\n\t\tmessage: '请输入验证码',\r\n\t\ttrigger: ['blur', 'change']\r\n\t}\r\n})\r\n// 国家区号\r\nconst countryCode = ref('86');\r\n\r\n// 验证码相关\r\nconst codeText = ref('获取验证码');\r\nconst canGetCode = ref(true);\r\n//-------\r\n// 账号登录表单\r\nconst accountForm = reactive({\r\n\tusername: '',\r\n\tpassword: '',\r\n});\r\n\r\n// 协议同意\r\nconst isAgreeProtocol = ref(false);\r\nconst isDisabledloginButton = computed(() => {\r\n\treturn !isAgreeProtocol.value;\r\n});\r\n\r\n// 登录按钮是否禁用\r\nfunction ChangeIsAgreeProtocol(){\r\n\tisAgreeProtocol.value = !isAgreeProtocol.value;\r\n}\r\nfunction onSubmit(e:any){\r\n\tconsole.log(\"onSubmit:\",e);\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.login-container {\r\n  height: 100%;\r\n  min-height: 100vh;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  position: relative;\r\n  // overflow: hidden;\r\n}\r\n\r\n.gradient-circle {\r\n  position: absolute;\r\n\r\n  border-radius: 200rpx;\r\n\r\n\r\n\r\n\r\n  background: radial-gradient(circle, #33a1fd 0%, #0062e6 70%);\r\n  background-color: #33a1fd;\r\n  /* 备用背景颜色 */\r\n  z-index: 0;\r\n}\r\n\r\n/* 添加调试样式 */\r\n.gradient-circle::after {\r\n  // content: '调试: 渐变球';\r\n  // color: red;\r\n  font-size: 12px;\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n}\r\n\r\n.top-right {\r\n  top: -80rpx;\r\n  right: -30rpx;\r\n  width: 300rpx;\r\n  /* 调整宽度 */\r\n  height: 300rpx;\r\n  /* 调整高度 */\r\n  opacity: 0.08;\r\n}\r\n\r\n.bottom-left {\r\n  bottom: -60rpx;\r\n  left: -60rpx;\r\n  width: 280rpx;\r\n  /* 调整宽度 */\r\n  height: 280rpx;\r\n  /* 调整高度 */\r\n  opacity: 0.1;\r\n}\r\n\r\n\r\n.logo-title {\r\n  text-align: center;\r\n  margin: 10rpx 0 30rpx;\r\n}\r\n\r\n.title-text {\r\n  font-size: 58rpx;\r\n  font-weight: bold;\r\n  color: $fui-color-primary;\r\n  //   font-family: ;\r\n}\r\n\r\n.form-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  width: 100%;\r\n  height: 70%;\r\n\r\n  .login-form-container {\r\n    // width: 100%;\r\n    padding: 0 16rpx;\r\n    display: flex;\r\n    min-width: 580rpx;\r\n  }\r\n\r\n  .form-login-tabs {}\r\n}\r\n\r\n.agreement {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  margin-top: 24rpx;\r\n  color: $fui-color-minor;\r\n\r\n  .agreement-row {\r\n    align-items: center;\r\n  }\r\n}\r\n\r\n.agreement-text {\r\n  font-size: 24rpx;\r\n}\r\n\r\n.agreement-link {\r\n  color: $fui-color-primary;\r\n  text-decoration: none;\r\n  font-weight: 500;\r\n}\r\n\r\n.footer {\r\n  text-align: center;\r\n  padding: 16rpx;\r\n  color: $fui-color-minor;\r\n  font-size: $fui-input-size;\r\n  margin-top: 20rpx;\r\n  // font-family: var(--font-content);\r\n}\r\n</style>\r\n", null], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;YA0EA,IAAM,4BAAa,uBAAA,qBAAA,cAAA,0BAAA,EAAA,EAAA,CAAA;gBACjB,IAAA,cAAY,IAAI;gBAChB,IAAA,eAAK,EAAE;gBACP,IAAA,iBAAO,EAAE;gBACT,IAAA,oBAAU,EAAE;gBACZ,IAAA,OAAM;aACP;YAiBD,IAAM,YAAY,IAAI,0BAAgC,IAAI;YAC1D,IAAS,cAAO,OAAO,MAAM,EAAA;gBAC3B,QAAQ,GAAG,CAAC,UAAS,OAAI;gBAGzB,IAAM,eAAe,UAAU,KAAK;gBACpC,IAAI,gBAAgB,IAAI,EAAE;oBAExB,aAAa,aAAW,CAAC,aAAa,OAAO;wBAC3C,IAAA,UAAA,MAAO;4BACL,QAAQ,GAAG,CAAC,WAAQ;wBACtB;wBACA,IAAA,OAAA,IAAK,0BAAgC,EAAA;4BACnC,QAAQ,GAAG,CAAC,SAAQ,KAAE;wBACxB;qBACD;;YAEL;gBAhBS;YAkBT,IAAM,WAAW,IAAI;gBACpB;oBAAE,IAAA,OAAM;oBAAS,IAAA,aAAI,CAAC;iBAAE;gBACxB;oBAAE,IAAA,OAAM;oBAAQ,IAAA,aAAI,CAAC;iBAAE;aACvB;YAGD,IAAM,UAAU,IAAI,CAAC;YACrB,IAAS,uBAAgB,mBAAkB,EAAA;gBAC1C,QAAQ,GAAG,CAAC,oBAAmB,GAAA;gBAC/B,IAAG,EAAE,KAAK,IAAK,IAAI,EAAC;oBACnB,QAAQ,KAAK,GAAG,EAAE,KAAK,CAAA,EAAA,CAAI,MAAM;;YAEnC;gBALS;YAOT,IAAM,aAAa,wCACjB,QAAO,IACP,OAAM;YAGR,IAAM,aAAa,IAAI;gBACtB,YAAS;oBACR,IAAA,OAAM;oBACN,IAAA,WAAU,IAAI;oBACd,IAAA,UAAS;oBACT,IAAA,UAAS;wBAAC;wBAAQ;qBAAS;iBAC3B;gBACD,WAAQ;oBACP,IAAA,OAAM;oBACN,IAAA,WAAU,IAAI;oBACd,IAAA,UAAS;oBACT,IAAA,UAAS;wBAAC;wBAAQ;qBAAS;iBAC3B;aACD;YAED,IAAM,cAAc,IAAI;YAGxB,IAAM,WAAW,IAAI;YACrB,IAAM,aAAa,IAAI,IAAI;YAG3B,IAAM,cAAc,SAAS;gBAC5B,IAAA,WAAU;gBACV,IAAA,WAAU;aACV;YAGD,IAAM,kBAAkB,IAAI,KAAK;YACjC,IAAM,wBAAwB,SAAS,OAAA,OAAA,CAAK;gBAC3C,OAAO,CAAC,gBAAgB,KAAK;YAC9B;;YAGA,IAAS,+BAAqB;gBAC7B,gBAAgB,KAAK,GAAG,CAAC,gBAAgB,KAAK;YAC/C;gBAFS;YAGT,IAAS,gBAAS,GAAE,GAAG,EAAA;gBACtB,QAAQ,GAAG,CAAC,aAAY,GAAA;YACzB;gBAFS;;;uBA1KL,mBAAiC,QAAA,SAAA,WAAA,oBAAA;oBAEjC,YAAA;oBAEA,mBAAiD,QAAA,SAA3C,WAAM;oBAGZ,mBAsDO,QAAA,SAtDD,WAAM;uCAEV,QAEO,SAAA,WAFI,mBAAa;2CACtB,QAAkC,SAAA,WAAvB,eAAa;;yBAG1B;oCAAgB,gDAAiB,SAAE,WAAM,mBAAW,UAAK,MAAO,WAAE,WAAM,KAAA,EAAE,YAAO,IAC9E,aAAM,MAAE,0CACX,IAAA,EAAA,CAAA,EA8BO;4BAAA;4BAAA;yBA9BI;2CAEW,QAAA,SAAA,WAAA,yBAAA;sCAApB,aAsBQ,CAAA;8DADE,SAAA,SAAA,CAAA,GAAA,sBAnBR,YAmBQ,gBAAA,GAAA;2CAAA;oDAnBG,uCAAW,SAAf,aAAI,aAAa,SAAA,WAAoB,gBAAA,6BAAuB;;kEACjE,YAQY,gBAAA,GAAA;mDAAA;4DARK,+CAAO,SAAE,WAAM;;;oDAI/B,IAAA,UAAA;0EACC,YAEY,gBAAA,GAAA;2DAAA;oEAFI,kDAAQ,SAAW,YAAA,KAAA;4DAAW,OAAA;wDAAA,GAAE,iBAAQ,IAAG,UAAM,EAAE,EAAK,YAAA,EAAS,EAAM,eAAK,EAAA,EAC1F,UAAA;;;;;;;;;4DAGa,+CAAM,SAAE,WAAM;;oDAG9B,IAAA,UAAA;0EACC,YAGY,gBAAA,GAAA;2DAAA;oEAHI,kDAAQ,SAAU,YAAA,KAAA;4DAAW,OAAA;wDAAA,GAAE,iBAAQ,IAAG,UAAM,EAAE,EAAK,YAAA,EAAS,EAAM,eAAK,EAAA,EACzF,UAAA;;;;;;;;;;;;;;;;8DAUR,SAFc,SAEd,CAAA,GAAA;;;;;;;yBAIF;2CASY,QAAA,SAAA,WAAA,cAAA;wCARO,8CAAQ,SAAC,aAAM,6DAE9B,YACiD,gBAAA,GAAA;uCAAA;gDAD7B,wDAAoB,SAAE,WAAO,sBAAmB,aAAA,MAAa,kBAAQ,kBAAY,QAClG,gBAAQ,IAAA,wCACX,IAAA,EAAA,CAAA,EAA2C;wCAAA;qCAAA;oCAC3C,mBAA2D,QAAA,SAArD,WAAM,mBAAA;oCACZ,mBAAqC,QAAA,SAA/B,WAAM,kCAAkB;oCAC9B,mBAAyD,QAAA,SAAnD,WAAM,mBAAA;;;;;;qBAOlB;uCACqD,QAAA,SAAA,WAAvC,WAAK"}