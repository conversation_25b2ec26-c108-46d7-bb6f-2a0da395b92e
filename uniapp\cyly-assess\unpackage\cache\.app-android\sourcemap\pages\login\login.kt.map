{"version": 3, "sources": ["pages/login/login.uvue", "pages/login/login.uvue?type=page"], "sourcesContent": ["<template>\r\n  <view class=\"login-container\">\r\n\r\n\r\n    <fui-status-bar></fui-status-bar>\r\n    <!-- 右上角渐变球 -->\r\n    <view class=\"gradient-circle top-right\"></view>\r\n    <!-- 左下角渐变球 -->\r\n    <view class=\"gradient-circle bottom-left\"></view>\r\n    <!-- 切换登录 -->\r\n    <!-- 登录表单 -->\r\n    <view class=\"form-container\">\r\n      <!-- Logo/标题 -->\r\n      <view class=\"logo-title\">\r\n        <text class=\"title-text\">登录</text>\r\n      </view>\r\n      <!-- 手机号登录 -->\r\n      <fui-tabs class=\"form-login-tabs\" :tabs=\"tabsList\" :short=\"false\" center :current=\"current\"\r\n        @change=\"handleChangeTab\"></fui-tabs>\r\n      <view class=\"login-form-container\">\r\n\r\n        <block v-if=\"current === 0\">\r\n          <!-- TODO: 要加前缀 +86 fui-dropdown-menu -->\r\n          <uForm ref=\"loginForm\" @submited=\"onSubmit\" v-model=\"phoneModel\" :showToast=\"true\" :toastTop=\"120\"\r\n            :toastDuration=\"4000\">\r\n            <uFromItem field=\"phone\" :rule=\"phoneRules\" :marginTop=\"16\">\r\n              <fui-input ref=\"phone\" @blur=\"onBlur('phone')\" :borderBottom=\"false\" :size=\"28\" :radius=\"12\" type=\"number\"\r\n                placeholder=\"请输入手机号\" v-model=\"phoneModel.phone as string\">\r\n              </fui-input>\r\n            </uFromItem>\r\n            <uFromItem field=\"code\" :rule=\"codeRules\" :marginTop=\"16\">\r\n              <fui-input ref=\"code\" @blur=\"onBlur('code')\" :borderBottom=\"false\" :size=\"28\" :radius=\"12\" type=\"number\"\r\n                placeholder=\"请输入验证码\" v-model=\"phoneModel.code as string\">\r\n\r\n              </fui-input>\r\n            </uFromItem>\r\n          </uForm>\r\n        </block>\r\n\r\n        <!-- 账号登录 -->\r\n        <block v-else>\r\n          2\r\n        </block>\r\n      </view>\r\n\r\n      <!-- 协议同意 -->\r\n      <view class=\"agreement\">\r\n        <fui-row justify=\"center\" class=\"agreement-row\">\r\n          <!-- 必须要这样写，转kt的时候不这样写会出现问题 -->\r\n          <fui-checkbox class=\"agreement-checkbox\" :checked=\"isAgreeProtocol\" borderRadius=\"8rpx\" :scaleRatio=\"0.78\"\r\n            @change=\"ChangeIsAgreeProtocol\"></fui-checkbox>\r\n          <text class=\"agreement-text\">我已阅读并同意</text>\r\n          <text class=\"agreement-text agreement-link\">《用户服务条款》</text>\r\n          <text class=\"agreement-text\">和</text>\r\n          <text class=\"agreement-text agreement-link\">《隐私协议》</text>\r\n        </fui-row>\r\n      </view>\r\n\r\n      <view class=\"login-button-container\">\r\n        <fui-button :radius=\"16\"> 登录 </fui-button>\r\n      </view>\r\n\r\n      <!-- 登录按钮 -->\r\n    </view>\r\n    <!-- 底部 -->\r\n    <view class=\"footer\">\r\n      <fui-footer text=\"by@海南长养乔智能科技有限责任公司\"></fui-footer>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup lang=\"uts\">\r\n\tconst inputStyle = {\r\n\t\tinputBorder: true,\r\n\t\tsize: 28,\r\n\t\tradius: 12,\r\n\t\tmarginTop: 16,\r\n\t\ttype: \"number\"\r\n\t}\r\n\t// 引入组件\r\n\timport fuiStatusBar from \"@/components/firstui/fui-status-bar/fui-status-bar\";\r\n\timport fuiFooter from \"@/components/firstui/fui-footer/fui-footer\";\r\n\timport fuiRow from \"@/components/firstui/fui-row/fui-row\";\r\n\timport fuiTabs from \"@/components/firstui/fui-tabs/fui-tabs\";\r\n\timport fuiInput from \"@/components/firstui/fui-input/fui-input\";\r\n\timport fuiButton from \"@/components/firstui/fui-button/fui-button\";\r\n\timport fuiIcon from \"@/components/firstui/fui-icon/fui-icon\";\r\n\timport fuiCheckbox from \"@/components/firstui/fui-checkbox/fui-checkbox\";\r\n\timport { FuiTabsItemParam } from \"@/components/firstui/fui-types/index.uts\";\r\n\timport uForm from \"@/components/uc/u-form/u-form\";\r\n\timport uFromItem from \"@/components/uc/u-from-item/u-from-item\";\r\n\timport { phoneModelType } from \"./types\";\r\n\timport { FormItemVerifyResult, FormValidResult, FormValidResultItem, FormItemData, FormItemRule } from \"@/components/uc/types/index.uts\"\r\n\timport { ComponentPublicInstance } from 'vue'\r\n\tconst instance = getCurrentInstance();\r\n\r\n\tconst loginForm = ref<ComponentPublicInstance | null>(null)\r\n\tfunction onBlur(field : 'phone' | 'code') {\r\n\t\tconsole.log(\"field:\", field)\r\n\t\tconst f = loginForm.value;\r\n\t\tif (f != null) {\r\n\t\t\t// 使用 $callMethod 调用组件方法\r\n\t\t\tf.$callMethod('validItem', field, {\r\n\t\t\t\tsuccess() {\r\n\t\t\t\t\tconsole.log(\"success\");\r\n\t\t\t\t},\r\n\t\t\t\tfail(res) {\r\n\t\t\t\t\tconsole.log(\"fail:\", res);\r\n\t\t\t\t}\r\n\t\t\t} as FormValidResultItem)\r\n\t\t}\r\n\t}\r\n\t// 标签页数据\r\n\tconst tabsList = ref([\r\n\t\t{ name: '手机号登录', id: 0 },\r\n\t\t{ name: '账号登录', id: 1 },\r\n\t]);\r\n\r\n\t// 当前选中的标签页\r\n\tconst current = ref(0);\r\n\tfunction handleChangeTab(e : FuiTabsItemParam) {\r\n\t\tconsole.log(\"handleChangeTab:\", e);\r\n\t\tif (e.index !== null) {\r\n\t\t\tcurrent.value = e.index as number;\r\n\t\t}\r\n\t}\r\n\t// 手机号登录表单\r\n\tconst phoneModel = reactive<phoneModelType>({\r\n\t\tphone: '',\r\n\t\tcode: ''\r\n\t})\r\n\t// 表单配置\r\n\tconst phoneRules = ref<FormItemRule>({\r\n\t\ttype: 'phone',\r\n\t\trequired: true,\r\n\t\tmessage: '请输入正确的手机号'\r\n\t})\r\n\tconst codeRules = ref<FormItemRule>({\r\n\t\ttype: 'number',\r\n\t\trequired: true,\r\n\t\tmessage: '请输入验证码'\r\n\t})\r\n\t// 国家区号\r\n\tconst countryCode = ref('86');\r\n\r\n\t// 验证码相关\r\n\tconst codeText = ref('获取验证码');\r\n\tconst canGetCode = ref(true);\r\n\t//-------\r\n\t// 账号登录表单\r\n\tconst accountForm = reactive({\r\n\t\tusername: '',\r\n\t\tpassword: '',\r\n\t});\r\n\r\n\t// 协议同意\r\n\tconst isAgreeProtocol = ref(false);\r\n\tconst isDisabledloginButton = computed(() => {\r\n\t\treturn !isAgreeProtocol.value;\r\n\t});\r\n\r\n\t// 登录按钮是否禁用\r\n\tfunction ChangeIsAgreeProtocol() {\r\n\t\tisAgreeProtocol.value = !isAgreeProtocol.value;\r\n\t}\r\n\tfunction onSubmit(e : any) {\r\n\t\tconsole.log(\"onSubmit:\", e);\r\n\t}\r\n\r\n\r\n\r\n\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.login-container {\r\n  height: 100%;\r\n  min-height: 100vh;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  position: relative;\r\n  // overflow: hidden;\r\n}\r\n\r\n.gradient-circle {\r\n  position: absolute;\r\n\r\n  border-radius: 200rpx;\r\n\r\n\r\n\r\n\r\n  background: radial-gradient(circle, #33a1fd 0%, #0062e6 70%);\r\n  background-color: #33a1fd;\r\n  /* 备用背景颜色 */\r\n  z-index: 0;\r\n}\r\n\r\n/* 添加调试样式 */\r\n.gradient-circle::after {\r\n  // content: '调试: 渐变球';\r\n  // color: red;\r\n  font-size: 12px;\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n}\r\n\r\n.top-right {\r\n  top: -80rpx;\r\n  right: -30rpx;\r\n  width: 300rpx;\r\n  /* 调整宽度 */\r\n  height: 300rpx;\r\n  /* 调整高度 */\r\n  opacity: 0.08;\r\n}\r\n\r\n.bottom-left {\r\n  bottom: -60rpx;\r\n  left: -60rpx;\r\n  width: 280rpx;\r\n  /* 调整宽度 */\r\n  height: 280rpx;\r\n  /* 调整高度 */\r\n  opacity: 0.1;\r\n}\r\n\r\n\r\n.logo-title {\r\n  text-align: center;\r\n  margin: 10rpx 0 30rpx;\r\n}\r\n\r\n.title-text {\r\n  font-size: 58rpx;\r\n  font-weight: bold;\r\n  color: $fui-color-primary;\r\n  //   font-family: ;\r\n}\r\n\r\n.form-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  width: 80%;\r\n  height: 70%;\r\n\r\n  .login-form-container {\r\n    // width: 100%;\r\n    padding: 0 16rpx;\r\n    display: flex;\r\n    min-width: 580rpx;\r\n  }\r\n\r\n  .form-login-tabs {}\r\n}\r\n\r\n.agreement {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  margin-top: 24rpx;\r\n  color: $fui-color-minor;\r\n\r\n  .agreement-row {\r\n    align-items: center;\r\n  }\r\n}\r\n\r\n.agreement-text {\r\n  font-size: 24rpx;\r\n}\r\n\r\n.agreement-link {\r\n  color: $fui-color-primary;\r\n  text-decoration: none;\r\n  font-weight: 500;\r\n}\r\n\r\n.footer {\r\n  text-align: center;\r\n  padding: 16rpx;\r\n  color: $fui-color-minor;\r\n  font-size: $fui-input-size;\r\n  margin-top: 20rpx;\r\n  // font-family: var(--font-content);\r\n}\r\n</style>", null], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;YAwEC,IAAM,4BAAa,uBAAA,qBAAA,cAAA,0BAAA,EAAA,EAAA,CAAA;gBAClB,IAAA,cAAa,IAAI;gBACjB,IAAA,eAAM,EAAE;gBACR,IAAA,iBAAQ,EAAE;gBACV,IAAA,oBAAW,EAAE;gBACb,IAAA,OAAM;aACN;YAgBD,IAAM,WAAW;YAEjB,IAAM,YAAY,IAAI,0BAAgC,IAAI;YAC1D,IAAS,cAAO,OAAQ,MAAO,AAAS,EAAA;gBACvC,QAAQ,GAAG,CAAC,UAAU,OAAI;gBAC1B,IAAM,IAAI,UAAU,KAAK;gBACzB,IAAI,KAAK,IAAI,EAAE;oBAEd,EAAE,aAAW,CAAC,aAAa,2BAC1B,UAAA,MAAO;wBACN,QAAQ,GAAG,CAAC,WAAQ;oBACrB;sBACA,OAAA,IAAK,GAAG,EAAA;wBACP,QAAQ,GAAG,CAAC,SAAS,KAAE;oBACxB;;;YAGH;gBAdS;YAgBT,IAAM,WAAW,IAAI;gBACpB;oBAAE,IAAA,OAAM;oBAAS,IAAA,aAAI,CAAC;iBAAE;gBACxB;oBAAE,IAAA,OAAM;oBAAQ,IAAA,aAAI,CAAC;iBAAE;aACvB;YAGD,IAAM,UAAU,IAAI,CAAC;YACrB,IAAS,uBAAgB,mBAAoB,EAAA;gBAC5C,QAAQ,GAAG,CAAC,oBAAoB,GAAA;gBAChC,IAAI,EAAE,KAAK,IAAK,IAAI,EAAE;oBACrB,QAAQ,KAAK,GAAG,EAAE,KAAK,CAAA,EAAA,CAAI,MAAM;;YAEnC;gBALS;YAOT,IAAM,aAAa,wCAClB,QAAO,IACP,OAAM;YAGP,IAAM,aAAa,+BAClB,OAAM,SACN,WAAU,IAAI,EACd,UAAS;YAEV,IAAM,YAAY,+BACjB,OAAM,UACN,WAAU,IAAI,EACd,UAAS;YAGV,IAAM,cAAc,IAAI;YAGxB,IAAM,WAAW,IAAI;YACrB,IAAM,aAAa,IAAI,IAAI;YAG3B,IAAM,cAAc,SAAS;gBAC5B,IAAA,WAAU;gBACV,IAAA,WAAU;aACV;YAGD,IAAM,kBAAkB,IAAI,KAAK;YACjC,IAAM,wBAAwB,SAAS,OAAA,OAAA,CAAK;gBAC3C,OAAO,CAAC,gBAAgB,KAAK;YAC9B;;YAGA,IAAS,+BAAqB;gBAC7B,gBAAgB,KAAK,GAAG,CAAC,gBAAgB,KAAK;YAC/C;gBAFS;YAGT,IAAS,gBAAS,GAAI,GAAG,EAAA;gBACxB,QAAQ,GAAG,CAAC,aAAa,GAAA;YAC1B;gBAFS;;;uBAjKN,mBAAiC,QAAA,SAAA,WAAA,oBAAA;oBAEjC,YAAA;oBAEA,mBAAiD,QAAA,SAA3C,WAAM;oBAGZ,mBAoDO,QAAA,SApDD,WAAM;uCAEV,QAEO,SAAA,WAFI,mBAAa;2CACtB,QAAkC,SAAA,WAAvB,eAAa;;yBAG1B;oCAAgB,gDAAiB,SAAE,WAAM,mBAAW,UAAK,MAAO,WAAE,WAAM,KAAA,EAAE,YAAO,IAC9E,aAAM,MAAE,0CACX,IAAA,EAAA,CAAA,EAwBO;4BAAA;4BAAA;yBAxBI;2CAEW,QAAA,SAAA,WAAA,yBAAA;sCAApB,aAgBQ,CAAA;8DADE,SAAA,SAAA,CAAA,GAAA,sBAbR,YAaQ,gBAAA,GAAA;2CAAA;oDAbG,uCAAW,SAAf,aAAI,aAAa,SAAA,2BAA6B,6CAAa,yBAAe,IAAA,yBAAA;4CAAA,eAAA,YAAA;wCAAA,GAAG,eAAU,IAAG,EAC9F,cAAA,GAAa,kDACd,YAIY,gBAAA,GAAA;mDAAA;4DAJK,+CAAO,SAAE,WAAM,SAAa,UAAA,MAAW,uDACtD,YAEY,gBAAA,GAAA;2DAAA;oEAFG,kDAAO,SAAE,SAAA,SAAwB,YAAA,KAAY;4DAAE,OAAK;wDAAA,GAAG,kBAAQ,KAAA,EAAG,UAAM,EAAE,EAAI,YAAK,EAAA,EAChG,UAAA;;;;;;;;+DAGJ,CAAA,EAKY;oDAAA;iDAAA;4DALK,+CAAM,SAAE,WAAM,QAAY,UAAA,MAAW,sDACpD,YAGY,gBAAA,GAAA;2DAAA;oEAHG,kDAAM,SAAE,SAAA,QAAuB,YAAA,KAAY;4DAAE,OAAK;wDAAA,GAAG,kBAAQ,KAAA,EAAG,UAAM,EAAE,EAAI,YAAK,EAAA,EAC9F,UAAA;;;;;;;;;;;;;;;;;;8DAUR,SAFc,SAEd,CAAA,GAAA;;;;;;;yBAIF;2CASY,QAAA,SAAA,WAAA,cAAA;wCARO,8CAAQ,SAAC,aAAM,6DAE9B,YACiD,gBAAA,GAAA;uCAAA;gDAD7B,wDAAoB,SAAE,WAAO,sBAAmB,aAAA,MAAa,kBAAQ,kBAAY,QAClG,gBAAQ,IAAA,wCACX,IAAA,EAAA,CAAA,EAA2C;wCAAA;qCAAA;oCAC3C,mBAA2D,QAAA,SAArD,WAAM,mBAAA;oCACZ,mBAAqC,QAAA,SAA/B,WAAM,kCAAkB;oCAC9B,mBAAyD,QAAA,SAAnD,WAAM,mBAAA;;;;;yBAIhB;2CAC4C,QAAA,SAAA,WAA7B,2BAAU;4FAAM,SAAJ,YAAI,EAAA,GAAA;;;;;;;qBAMjC;uCACqD,QAAA,SAAA,WAAvC,WAAK"}