{"version": 3, "sources": ["pages/login/login.uvue", "pages/login/login.uvue?type=page"], "sourcesContent": ["<template>\r\n  <view class=\"login-container\">\r\n    <!-- Toast 提示框 -->\r\n    <uToast ref=\"toastRef\" :visible=\"toastVisible\" :message=\"toastMessage\" :type=\"toastType\"\r\n      :backgroundColor=\"toastBgColor\" :textColor=\"toastTextColor\" :icon=\"toastIcon\" :showIcon=\"toastShowIcon\"\r\n      :duration=\"toastDuration\" :position=\"toastPosition\" :top=\"toastTop\" @close=\"onToastClose\" />\r\n\r\n    <fui-status-bar></fui-status-bar>\r\n    <!-- 右上角渐变球 -->\r\n    <view class=\"gradient-circle top-right\"></view>\r\n    <!-- 左下角渐变球 -->\r\n    <view class=\"gradient-circle bottom-left\"></view>\r\n    <!-- 切换登录 -->\r\n    <!-- 登录表单 -->\r\n    <view class=\"form-container\">\r\n      <!-- Logo/标题 -->\r\n      <view class=\"logo-title\">\r\n        <text class=\"title-text\">登录</text>\r\n      </view>\r\n      <!-- 手机号登录 -->\r\n      <fui-tabs class=\"form-login-tabs\" :tabs=\"tabsList\" :short=\"false\" center :current=\"current\"\r\n        @change=\"handleChangeTab\"></fui-tabs>\r\n      <view class=\"login-form-container\">\r\n\r\n        <block v-if=\"current === 0\">\r\n          <!-- TODO: 要加前缀 +86 fui-dropdown-menu -->\r\n          <uForm ref=\"loginForm\" @submited=\"onSubmit\" v-model=\"phoneModel\">\r\n            <uFromItem field=\"phone\" :rule=\"phoneRules\" :marginTop=\"16\">\r\n              <fui-input ref=\"phone\" @blur=\"onBlur('phone')\" :borderBottom=\"false\" :size=\"28\" :radius=\"12\" type=\"number\"\r\n                placeholder=\"请输入手机号\" v-model=\"phoneModel.phone as string\">\r\n              </fui-input>\r\n            </uFromItem>\r\n            <uFromItem field=\"code\" :rule=\"codeRules\" :marginTop=\"16\">\r\n              <fui-input ref=\"code\" @blur=\"onBlur('code')\" :borderBottom=\"false\" :size=\"28\" :radius=\"12\" type=\"number\"\r\n                placeholder=\"请输入验证码\" v-model=\"phoneModel.code as string\">\r\n\r\n              </fui-input>\r\n            </uFromItem>\r\n          </uForm>\r\n        </block>\r\n\r\n        <!-- 账号登录 -->\r\n        <block v-else>\r\n          2\r\n        </block>\r\n      </view>\r\n\r\n      <!-- 协议同意 -->\r\n      <view class=\"agreement\">\r\n        <fui-row justify=\"center\" class=\"agreement-row\">\r\n          <!-- 必须要这样写，转kt的时候不这样写会出现问题 -->\r\n          <fui-checkbox class=\"agreement-checkbox\" :checked=\"isAgreeProtocol\" borderRadius=\"8rpx\" :scaleRatio=\"0.78\"\r\n            @change=\"ChangeIsAgreeProtocol\"></fui-checkbox>\r\n          <text class=\"agreement-text\">我已阅读并同意</text>\r\n          <text class=\"agreement-text agreement-link\">《用户服务条款》</text>\r\n          <text class=\"agreement-text\">和</text>\r\n          <text class=\"agreement-text agreement-link\">《隐私协议》</text>\r\n        </fui-row>\r\n      </view>\r\n\r\n      <!-- Toast 测试按钮 -->\r\n      <view class=\"test-buttons\">\r\n        <button class=\"test-btn success\" @click=\"showSuccessToast\">基础使用</button>\r\n        <button class=\"test-btn custom\" @click=\"showCustomToast\">自定义背景色</button>\r\n        <button class=\"test-btn icon\" @click=\"showIconToast\">带图标提示</button>\r\n        <button class=\"test-btn time\" @click=\"showTimedToast\">设置显示时间</button>\r\n      </view>\r\n\r\n      <!-- 登录按钮 -->\r\n    </view>\r\n    <!-- 底部 -->\r\n    <view class=\"footer\">\r\n      <fui-footer text=\"by@海南长养乔智能科技有限责任公司\"></fui-footer>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup lang=\"uts\">\r\n\tconst inputStyle = {\r\n\t\tinputBorder: true,\r\n\t\tsize: 28,\r\n\t\tradius: 12,\r\n\t\tmarginTop: 16,\r\n\t\ttype: \"number\"\r\n\t}\r\n\t// 引入组件\r\n\timport fuiStatusBar from \"@/components/firstui/fui-status-bar/fui-status-bar\";\r\n\timport fuiFooter from \"@/components/firstui/fui-footer/fui-footer\";\r\n\timport fuiRow from \"@/components/firstui/fui-row/fui-row\";\r\n\timport fuiTabs from \"@/components/firstui/fui-tabs/fui-tabs\";\r\n\timport fuiInput from \"@/components/firstui/fui-input/fui-input\";\r\n\timport fuiButton from \"@/components/firstui/fui-button/fui-button\";\r\n\timport fuiIcon from \"@/components/firstui/fui-icon/fui-icon\";\r\n\timport fuiCheckbox from \"@/components/firstui/fui-checkbox/fui-checkbox\";\r\n\timport { FuiTabsItemParam } from \"@/components/firstui/fui-types/index.uts\";\r\n\timport uForm from \"@/components/uc/u-form/u-form\";\r\n\timport uFromItem from \"@/components/uc/u-from-item/u-from-item\";\r\n\timport uToast from \"@/components/uc/u-toast/u-toast\";\r\n\timport { phoneModelType } from \"./types\";\r\n\timport { FormItemVerifyResult, FormValidResult, FormValidResultItem, FormItemData, FormItemRule, ToastType, ToastPosition } from \"@/components/uc/types/index.uts\"\r\n\timport { ComponentPublicInstance } from 'vue'\r\n\tconst instance = getCurrentInstance();\r\n\r\n\tconst loginForm = ref<ComponentPublicInstance | null>(null)\r\n\tfunction onBlur(field : 'phone' | 'code') {\r\n\t\tconsole.log(\"field:\", field)\r\n\t\tconst f = loginForm.value;\r\n\t\tif (f != null) {\r\n\t\t\t// 使用 $callMethod 调用组件方法\r\n\t\t\tf.$callMethod('validItem', field, {\r\n\t\t\t\tsuccess() {\r\n\t\t\t\t\tconsole.log(\"success\");\r\n\t\t\t\t},\r\n\t\t\t\tfail(res) {\r\n\t\t\t\t\tconsole.log(\"fail:\", res);\r\n\t\t\t\t}\r\n\t\t\t} as FormValidResultItem)\r\n\t\t}\r\n\t}\r\n\t// 标签页数据\r\n\tconst tabsList = ref([\r\n\t\t{ name: '手机号登录', id: 0 },\r\n\t\t{ name: '账号登录', id: 1 },\r\n\t]);\r\n\r\n\t// 当前选中的标签页\r\n\tconst current = ref(0);\r\n\tfunction handleChangeTab(e : FuiTabsItemParam) {\r\n\t\tconsole.log(\"handleChangeTab:\", e);\r\n\t\tif (e.index !== null) {\r\n\t\t\tcurrent.value = e.index as number;\r\n\t\t}\r\n\t}\r\n\t// 手机号登录表单\r\n\tconst phoneModel = reactive<phoneModelType>({\r\n\t\tphone: '',\r\n\t\tcode: ''\r\n\t})\r\n\t// 表单配置\r\n\tconst phoneRules = ref<FormItemRule>({\r\n\t\ttype: 'phone',\r\n\t\trequired: true,\r\n\t\tmessage: '请输入正确的手机号'\r\n\t})\r\n\tconst codeRules = ref<FormItemRule>({\r\n\t\ttype: 'number',\r\n\t\trequired: true,\r\n\t\tmessage: '请输入验证码'\r\n\t})\r\n\t// 国家区号\r\n\tconst countryCode = ref('86');\r\n\r\n\t// 验证码相关\r\n\tconst codeText = ref('获取验证码');\r\n\tconst canGetCode = ref(true);\r\n\t//-------\r\n\t// 账号登录表单\r\n\tconst accountForm = reactive({\r\n\t\tusername: '',\r\n\t\tpassword: '',\r\n\t});\r\n\r\n\t// 协议同意\r\n\tconst isAgreeProtocol = ref(false);\r\n\tconst isDisabledloginButton = computed(() => {\r\n\t\treturn !isAgreeProtocol.value;\r\n\t});\r\n\r\n\t// 登录按钮是否禁用\r\n\tfunction ChangeIsAgreeProtocol() {\r\n\t\tisAgreeProtocol.value = !isAgreeProtocol.value;\r\n\t}\r\n\tfunction onSubmit(e : any) {\r\n\t\tconsole.log(\"onSubmit:\", e);\r\n\t}\r\n\r\n\t// Toast 相关数据\r\n\tconst toastRef = ref<ComponentPublicInstance | null>(null)\r\n\tconst toastVisible = ref(false)\r\n\tconst toastMessage = ref('')\r\n\tconst toastType = ref<ToastType>('info')\r\n\tconst toastBgColor = ref('')\r\n\tconst toastTextColor = ref('')\r\n\tconst toastIcon = ref('')\r\n\tconst toastShowIcon = ref(true)\r\n\tconst toastDuration = ref(3000)\r\n\tconst toastPosition = ref<ToastPosition>('top')\r\n\tconst toastTop = ref(100)\r\n\r\n\t// Toast 方法\r\n\tfunction showToast(options: {\r\n\t\tmessage: string,\r\n\t\ttype?: ToastType,\r\n\t\tbackgroundColor?: string,\r\n\t\ttextColor?: string,\r\n\t\ticon?: string,\r\n\t\tshowIcon?: boolean,\r\n\t\tduration?: number,\r\n\t\tposition?: ToastPosition,\r\n\t\ttop?: number\r\n\t}) {\r\n\t\ttoastMessage.value = options.message\r\n\t\ttoastType.value = options.type || 'info'\r\n\t\ttoastBgColor.value = options.backgroundColor || ''\r\n\t\ttoastTextColor.value = options.textColor || ''\r\n\t\ttoastIcon.value = options.icon || ''\r\n\t\ttoastShowIcon.value = options.showIcon !== false\r\n\t\ttoastDuration.value = options.duration || 3000\r\n\t\ttoastPosition.value = options.position || 'top'\r\n\t\ttoastTop.value = options.top || 100\r\n\t\ttoastVisible.value = true\r\n\t}\r\n\r\n\tfunction onToastClose() {\r\n\t\ttoastVisible.value = false\r\n\t}\r\n\r\n\t// 测试方法\r\n\tfunction showSuccessToast() {\r\n\t\tshowToast({\r\n\t\t\tmessage: '这是一条成功提示消息！',\r\n\t\t\ttype: 'success',\r\n\t\t\ticon: '✓'\r\n\t\t})\r\n\t}\r\n\r\n\tfunction showCustomToast() {\r\n\t\tshowToast({\r\n\t\t\tmessage: '自定义背景色',\r\n\t\t\tbackgroundColor: '#6366f1',\r\n\t\t\ttextColor: '#ffffff',\r\n\t\t\ticon: '🎨'\r\n\t\t})\r\n\t}\r\n\r\n\tfunction showIconToast() {\r\n\t\tshowToast({\r\n\t\t\tmessage: '带图标提示',\r\n\t\t\ttype: 'warning',\r\n\t\t\ticon: '⚠️'\r\n\t\t})\r\n\t}\r\n\r\n\tfunction showTimedToast() {\r\n\t\tshowToast({\r\n\t\t\tmessage: '设置显示时间',\r\n\t\t\ttype: 'info',\r\n\t\t\ticon: '⏰',\r\n\t\t\tduration: 5000\r\n\t\t})\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.login-container {\r\n  height: 100%;\r\n  min-height: 100vh;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  position: relative;\r\n  // overflow: hidden;\r\n}\r\n\r\n.gradient-circle {\r\n  position: absolute;\r\n\r\n  border-radius: 200rpx;\r\n\r\n\r\n\r\n\r\n  background: radial-gradient(circle, #33a1fd 0%, #0062e6 70%);\r\n  background-color: #33a1fd;\r\n  /* 备用背景颜色 */\r\n  z-index: 0;\r\n}\r\n\r\n/* 添加调试样式 */\r\n.gradient-circle::after {\r\n  // content: '调试: 渐变球';\r\n  // color: red;\r\n  font-size: 12px;\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n}\r\n\r\n.top-right {\r\n  top: -80rpx;\r\n  right: -30rpx;\r\n  width: 300rpx;\r\n  /* 调整宽度 */\r\n  height: 300rpx;\r\n  /* 调整高度 */\r\n  opacity: 0.08;\r\n}\r\n\r\n.bottom-left {\r\n  bottom: -60rpx;\r\n  left: -60rpx;\r\n  width: 280rpx;\r\n  /* 调整宽度 */\r\n  height: 280rpx;\r\n  /* 调整高度 */\r\n  opacity: 0.1;\r\n}\r\n\r\n\r\n.logo-title {\r\n  text-align: center;\r\n  margin: 10rpx 0 30rpx;\r\n}\r\n\r\n.title-text {\r\n  font-size: 58rpx;\r\n  font-weight: bold;\r\n  color: $fui-color-primary;\r\n  //   font-family: ;\r\n}\r\n\r\n.form-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  width: 100%;\r\n  height: 70%;\r\n\r\n  .login-form-container {\r\n    // width: 100%;\r\n    padding: 0 16rpx;\r\n    display: flex;\r\n    min-width: 580rpx;\r\n  }\r\n\r\n  .form-login-tabs {}\r\n}\r\n\r\n.agreement {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  margin-top: 24rpx;\r\n  color: $fui-color-minor;\r\n\r\n  .agreement-row {\r\n    align-items: center;\r\n  }\r\n}\r\n\r\n.agreement-text {\r\n  font-size: 24rpx;\r\n}\r\n\r\n.agreement-link {\r\n  color: $fui-color-primary;\r\n  text-decoration: none;\r\n  font-weight: 500;\r\n}\r\n\r\n.footer {\r\n  text-align: center;\r\n  padding: 16rpx;\r\n  color: $fui-color-minor;\r\n  font-size: $fui-input-size;\r\n  margin-top: 20rpx;\r\n  // font-family: var(--font-content);\r\n}\r\n\r\n/* Toast 测试按钮样式 */\r\n.test-buttons {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 16rpx;\r\n  margin-top: 32rpx;\r\n  justify-content: center;\r\n}\r\n\r\n.test-btn {\r\n  padding: 16rpx 24rpx;\r\n  border-radius: 8rpx;\r\n  border: none;\r\n  font-size: 24rpx;\r\n  color: white;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  min-width: 120rpx;\r\n\r\n  &.success {\r\n    background-color: #52c41a;\r\n\r\n    &:hover {\r\n      background-color: #73d13d;\r\n    }\r\n  }\r\n\r\n  &.custom {\r\n    background-color: #6366f1;\r\n\r\n    &:hover {\r\n      background-color: #818cf8;\r\n    }\r\n  }\r\n\r\n  &.icon {\r\n    background-color: #faad14;\r\n\r\n    &:hover {\r\n      background-color: #ffc53d;\r\n    }\r\n  }\r\n\r\n  &.time {\r\n    background-color: #1890ff;\r\n\r\n    &:hover {\r\n      background-color: #40a9ff;\r\n    }\r\n  }\r\n}\r\n</style>", null], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;YA8EC,IAAM,4BAAa,uBAAA,qBAAA,cAAA,0BAAA,EAAA,EAAA,CAAA;gBAClB,IAAA,cAAa,IAAI;gBACjB,IAAA,eAAM,EAAE;gBACR,IAAA,iBAAQ,EAAE;gBACV,IAAA,oBAAW,EAAE;gBACb,IAAA,OAAM;aACN;YAiBD,IAAM,WAAW;YAEjB,IAAM,YAAY,IAAI,0BAAgC,IAAI;YAC1D,IAAS,cAAO,OAAQ,MAAO,AAAS,EAAA;gBACvC,QAAQ,GAAG,CAAC,UAAU,OAAI;gBAC1B,IAAM,IAAI,UAAU,KAAK;gBACzB,IAAI,KAAK,IAAI,EAAE;oBAEd,EAAE,aAAW,CAAC,aAAa,2BAC1B,UAAA,MAAO;wBACN,QAAQ,GAAG,CAAC,WAAQ;oBACrB;sBACA,OAAA,IAAK,GAAG,EAAA;wBACP,QAAQ,GAAG,CAAC,SAAS,KAAE;oBACxB;;;YAGH;gBAdS;YAgBT,IAAM,WAAW,IAAI;gBACpB;oBAAE,IAAA,OAAM;oBAAS,IAAA,aAAI,CAAC;iBAAE;gBACxB;oBAAE,IAAA,OAAM;oBAAQ,IAAA,aAAI,CAAC;iBAAE;aACvB;YAGD,IAAM,UAAU,IAAI,CAAC;YACrB,IAAS,uBAAgB,mBAAoB,EAAA;gBAC5C,QAAQ,GAAG,CAAC,oBAAoB,GAAA;gBAChC,IAAI,EAAE,KAAK,IAAK,IAAI,EAAE;oBACrB,QAAQ,KAAK,GAAG,EAAE,KAAK,CAAA,EAAA,CAAI,MAAM;;YAEnC;gBALS;YAOT,IAAM,aAAa,wCAClB,QAAO,IACP,OAAM;YAGP,IAAM,aAAa,+BAClB,OAAM,SACN,WAAU,IAAI,EACd,UAAS;YAEV,IAAM,YAAY,+BACjB,OAAM,UACN,WAAU,IAAI,EACd,UAAS;YAGV,IAAM,cAAc,IAAI;YAGxB,IAAM,WAAW,IAAI;YACrB,IAAM,aAAa,IAAI,IAAI;YAG3B,IAAM,cAAc,SAAS;gBAC5B,IAAA,WAAU;gBACV,IAAA,WAAU;aACV;YAGD,IAAM,kBAAkB,IAAI,KAAK;YACjC,IAAM,wBAAwB,SAAS,OAAA,OAAA,CAAK;gBAC3C,OAAO,CAAC,gBAAgB,KAAK;YAC9B;;YAGA,IAAS,+BAAqB;gBAC7B,gBAAgB,KAAK,GAAG,CAAC,gBAAgB,KAAK;YAC/C;gBAFS;YAGT,IAAS,gBAAS,GAAI,GAAG,EAAA;gBACxB,QAAQ,GAAG,CAAC,aAAa,GAAA;YAC1B;gBAFS;YAKT,IAAM,WAAW,IAAI,0BAAgC,IAAI;YACzD,IAAM,eAAe,IAAI,KAAK;YAC9B,IAAM,eAAe,IAAI;YACzB,IAAM,YAAY,eAAe;YACjC,IAAM,eAAe,IAAI;YACzB,IAAM,iBAAiB,IAAI;YAC3B,IAAM,YAAY,IAAI;YACtB,IAAM,gBAAgB,IAAI,IAAI;YAC9B,IAAM,gBAAgB,IAAI,IAAI;YAC9B,IAAM,gBAAgB,mBAAmB;YACzC,IAAM,WAAW,IAAI,GAAG;YAGxB,IAAS,iBAAU;oBAClB,SAAS,MAAM;oBACf;oBACA,iBAAkB,MAAM;oBACxB,WAAY,MAAM;oBAClB,MAAO,MAAM;oBACb,UAAW,OAAO;oBAClB,UAAW,MAAM;oBACjB;oBACA,KAAM,MAAM;aACZ,EAAA;gBACA,aAAa,KAAK,GAAG,QAAQ,OAAO;gBACpC,UAAU,KAAK,GAAG,QAAQ,IAAI,IAAI;gBAClC,aAAa,KAAK,GAAG,QAAQ,eAAe,IAAI;gBAChD,eAAe,KAAK,GAAG,QAAQ,SAAS,IAAI;gBAC5C,UAAU,KAAK,GAAG,QAAQ,IAAI,IAAI;gBAClC,cAAc,KAAK,GAAG,QAAQ,QAAQ,KAAK,KAAK;gBAChD,cAAc,KAAK,GAAG,QAAQ,QAAQ,IAAI,IAAI;gBAC9C,cAAc,KAAK,GAAG,QAAQ,QAAQ,IAAI;gBAC1C,SAAS,KAAK,GAAG,QAAQ,GAAG,IAAI,GAAG;gBACnC,aAAa,KAAK,GAAG,IAAI;YAC1B;gBArBS;YAuBT,IAAS,sBAAY;gBACpB,aAAa,KAAK,GAAG,KAAK;YAC3B;gBAFS;YAKT,IAAS,0BAAgB;gBACxB,UAAU;oBACT,IAAA,UAAS;oBACT,IAAA,OAAM;oBACN,IAAA,OAAM;iBACN;YACF;gBANS;YAQT,IAAS,yBAAe;gBACvB,UAAU;oBACT,IAAA,UAAS;oBACT,IAAA,kBAAiB;oBACjB,IAAA,YAAW;oBACX,IAAA,OAAM;iBACN;YACF;gBAPS;YAST,IAAS,uBAAa;gBACrB,UAAU;oBACT,IAAA,UAAS;oBACT,IAAA,OAAM;oBACN,IAAA,OAAM;iBACN;YACF;gBANS;YAQT,IAAS,wBAAc;gBACtB,UAAU;oBACT,IAAA,UAAS;oBACT,IAAA,OAAM;oBACN,IAAA,OAAM;oBACN,IAAA,mBAAU,IAAI;iBACd;YACF;gBAPS;;;uBAhPN,mBAE8F,QAAA,SAAA,WAAA,oBAAA;gCAFlF,yCAAU,SAAd,aAAI,YAAY,SAAA,UAAwB,aAAS,MAAA,eAAe,aAAM,MAAA,eAC3E,UAAA,MAAA,YAAgC,qBAAW,MAAA,eAAiB,eAAM,MAAA,iBAAY,UAAA,MAAU,YACxF,cAAU,MAAA,gBAAgB,cAAU,MAAA,gBAAgB,cAAK,MAAA,gBAAW,SAAA,MAAO,uCAE9E,IAAA,EAAA,CAAA,EAAiC;wBAAA;wBAAA;wBAAA;wBAAA;wBAAA;wBAAA;wBAAA;wBAAA;wBAAA;wBAAA;qBAAA;oBAEjC,YAAA;oBAEA,mBAAiD,QAAA,SAA3C,WAAM;oBAGZ,mBAuDO,QAAA,SAvDD,WAAM;uCAEV,QAEO,SAAA,WAFI,mBAAa;2CACtB,QAAkC,SAAA,WAAvB,eAAa;;yBAG1B;oCAAgB,gDAAiB,SAAE,WAAM,mBAAW,UAAK,MAAO,WAAE,WAAM,KAAA,EAAE,YAAO,IAC9E,aAAM,MAAE,0CACX,IAAA,EAAA,CAAA,EAuBO;4BAAA;4BAAA;yBAvBI;2CAEW,QAAA,SAAA,WAAA,yBAAA;sCAApB,aAeQ,CAAA;8DADE,SAAA,SAAA,CAAA,GAAA,sBAZR,YAYQ,gBAAA,GAAA;2CAAA;oDAZG,uCAAW,SAAf,aAAI,aAAa,SAAA,2BAA6B;;kEACnD,YAIY,gBAAA,GAAA;mDAAA;4DAJK,+CAAO,SAAE,WAAM,SAAa,UAAA,MAAW,uDACtD,YAEY,gBAAA,GAAA;2DAAA;oEAFG,kDAAO,SAAE,SAAA,SAAwB,YAAA,KAAY;4DAAE,OAAK;wDAAA,GAAG,kBAAQ,KAAA,EAAG,UAAM,EAAE,EAAI,YAAK,EAAA,EAChG,UAAA;;;;;;;;+DAGJ,CAAA,EAKY;oDAAA;iDAAA;4DALK,+CAAM,SAAE,WAAM,QAAY,UAAA,MAAW,sDACpD,YAGY,gBAAA,GAAA;2DAAA;oEAHG,kDAAM,SAAE,SAAA,QAAuB,YAAA,KAAY;4DAAE,OAAK;wDAAA,GAAG,kBAAQ,KAAA,EAAG,UAAM,EAAE,EAAI,YAAK,EAAA,EAC9F,UAAA;;;;;;;;;;;;;;;;;;8DAUR,SAFc,SAEd,CAAA,GAAA;;;;;;;yBAIF;2CASY,QAAA,SAAA,WAAA,cAAA;wCARO,8CAAQ,SAAC,aAAM,6DAE9B,YACiD,gBAAA,GAAA;uCAAA;gDAD7B,wDAAoB,SAAE,WAAO,sBAAmB,aAAA,MAAa,kBAAQ,kBAAY,QAClG,gBAAQ,IAAA,wCACX,IAAA,EAAA,CAAA,EAA2C;wCAAA;qCAAA;oCAC3C,mBAA2D,QAAA,SAArD,WAAM,mBAAA;oCACZ,mBAAqC,QAAA,SAA/B,WAAM,kCAAkB;oCAC9B,mBAAyD,QAAA,SAAnD,WAAM,mBAAA;;;;;yBAKhB;2CACE,QAAwE,SAAA,WAAA,iBAAA;+CAA1D,UAAkB,SAAE,WAAK,iCAAwB,mBAC/D;+CAAc,UAAiB,SAAE,WAAK,gCAAmB,kBACzD;+CAAc,UAAe,SAAE,WAAK,8BAAsB,gBAC1D;+CAAc,UAAe,SAAE,WAAK,8BAAkB;;qBAM1D;uCACqD,QAAA,SAAA,WAAvC,WAAK"}