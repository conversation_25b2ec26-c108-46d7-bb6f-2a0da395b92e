export type RGB = {
	r : number;
	g : number;
	b : number;
}
export type RGBA = {
	r : number;
	g : number;
	b : number;
	a : number;
}
export type RGBAString = {
	r : string;
	g : string;
	b : string;
	a : number;
}
export type HSL = {
	h : number;
	s : number;
	l : number;
}

export type HSLA = {
	h : number;
	s : number;
	l : number;
	a : number;
}
export type HSV = {
	h : number;
	s : number;
	v : number;
}

export type HSVA = {
	h : number;
	s : number;
	v : number;
	a : number;
}

// 增加部分
export type HSB = {
	h : number;
	s : number;
	b : number;
}
export type HSBA = {
	h : number;
	s : number;
	b : number;
	a : number;
}

export type LColorInfo = {
	ok ?: boolean;
	format ?: LColorFormats;
	r : number;
	g : number;
	b : number;
	a : number;
}

export type LColorFormats =
	| 'rgb'
	| 'prgb'
	| 'hex'
	| 'hex3'
	| 'hex4'
	| 'hex6'
	| 'hex8'
	| 'name'
	| 'hsl'
	| 'hsb'
	| 'hsv';

export type LColorOptions = {
	format ?: LColorFormats;
	gradientType ?: string;
}
export type LColorInput = any //string | number | RGB | RGBA | HSL | HSLA | HSV | HSVA | LimeColor;

export type LGenerateOptions = {
	theme ?: 'dark' | 'default';
	backgroundColor ?: string;
}
