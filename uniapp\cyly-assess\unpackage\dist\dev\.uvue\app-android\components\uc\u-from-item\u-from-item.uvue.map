{"version": 3, "sources": ["components/uc/u-from-item/u-from-item.uvue"], "names": [], "mappings": "AAkBI,OAAO,EAAE,YAAY,EAAC,YAAY,EAAC,oBAAoB,EAAE,MAAM,UAAU,CAAA;AACzE,OAAO,EAAE,SAAS,EAAC,UAAU,EAAE,MAAM,UAAU,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;QAC/C,MAAM,OAAO,GAAG,GAAG,CAAC,IAAI,CAAC,CAAA;QACzB,MAAM,eAAe,GAAG,QAAQ,CAAA;QACjC,MAAM,WAAW,GAAG,GAAG,CAAC,QAAQ,CAAC,CAAA;QAChC,MAAM,QAAQ,GAAG,kBAAkB,EAAE,CAAC,CAAC;QACvC,MAAM,KAAK,GAAG,OAmBZ,CAAC;QAEN,SAAS,MAAM,CAAC,KAAK,EAAG,GAAG,EAAE,QAAQ,EAAG,CAAC,GAAG,EAAG,oBAAoB,KAAK,IAAI;YAC3E,OAAO,SAAS,CAAC,OAAO,EAAE,WAAW,EAAE,KAAK,CAAC,KAAK,IAAI,MAAM,EAAE,KAAK,CAAC,IAAI,IAAI,YAAY,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAA;QAC3G,CAAC;QACD,SAAS,uBAAuB;YAC9B,MAAM,IAAI,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;YAC7B,WAAW;YACX,MAAM,MAAM,GAAG,UAAU,CAAC,IAAI,EAAE,CAAC,eAAe,CAAC,CAAC,CAAC;YACnD,OAAO,CAAC,GAAG,CAAC,WAAW,EAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,IAAG,EAAA,mDAAC,CAAC,CAAA;YACpD,IAAI,MAAM,IAAI,IAAI,EAAE;gBACnB,OAAO,CAAC,KAAK,CAAC,QAAQ,EAAC,wCAAuC,EAAA,mDAAC,CAAC,CAAC;gBACjE,OAAO;aACP;YACD,MAAM,IAAI,GAAG;gBACZ,KAAK,EAAE,KAAK,CAAC,KAAK,IAAI,MAAM;gBAC5B,QAAQ,EAAE,IAAI;aACd,IAAI,YAAY,CAAC;YAClB,MAAM,CAAC,WAAW,CAAC,eAAe,EAAE,IAAI,CAAC,CAAA;QAC3C,CAAC;QACD,SAAS,CAAC,GAAE,EAAE;YACb,uBAAuB,EAAE,CAAC;QAC3B,CAAC,CAAC,CAAA;QACF,QAAY,CAAC;YACZ,MAAM;SACN,CAAC,CAAA;;mBAnEU,kBAAC,CAAA,MAAmB,EAAA,QAAA,CAAA;gBAAE,KAAK,EAAA,mBAAG;;cAEvC,EAAA;kCAEE,CAAA,MAEO,EAAA,QAAA,CAAA,EAAA,KAFD,EAAK,eAAC,EAAA,CAAA,EAAiB;sCACd,CAAA,MAAA,EAAA,QAAA,CAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,CAAA,EAAA;;;0BAGf,CAAA,IAAA,CAAA,SAAA,IAGO,CAAA,KAAA,CAAA,OAAA,CAAA,CAAA;;4BAHD,GAAA,EAAK,CAAA;;0BACT,EAAA;4BACA,kBAAA,CAAiD,MAAA,EAAA,QAAA,CAAA,EAA3C,KAAK,EAAC,YAAY,EAAA,CAAA,EAAA,GAAA,CAAA", "sourcesContent": ["<template>\n  <view class=\"form-item-wrapper\" :style=\"[customStyle]\">\n    <!-- 输入框和错误提示在同一行 -->\n    <view class=\"form-item-row\">\n      <!-- 输入框容器 -->\n      <view class=\"input-container\">\n        <slot></slot>\n      </view>\n      <!-- 错误提示 - 在输入框右侧显示 -->\n      <view class=\"error-message-inline\" v-if=\"showError && !isValid\">\n        <view class=\"error-icon\">!</view>\n        <text class=\"error-text\">{{ hintMessage }}</text>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script lang=\"uts\" setup>\n    import { FormItemData,FormItemRule,FormItemVerifyResult } from \"../types\"\n    import { useVerify,findParent } from \"../utils\"\n    const isValid = ref(true)\n    const parentLabelName = \"u-form\"\n\t  const hintMessage = ref('\\u3000')\n    const instance = getCurrentInstance()!;\n    const props = defineProps({\n        customStyle: {\n          type: Object as PropType<UTSJSONObject>,\n          default: {} as UTSJSONObject as UTSJSONObject\n        },\n        field: {\n            type: String,\n            default: ''\n        },\n        rule: {\n          type: Object as PropType<FormItemRule>,\n          default: (): FormItemRule =>{\n            return {} as FormItemRule\n          }\n        },\n        showError:{\n          type:Boolean,\n          default:true\n        }\n    });\n    \n\tfunction verify(value : any, callback : (res : FormItemVerifyResult) => void){\n\t\treturn useVerify(isValid, hintMessage, props.field as string, props.rule as FormItemRule, value, callback)\n\t}\n\tfunction pushFormItemFieldToForm(){\n\t\t\tconst that = instance.proxy!;\n\t\t\t//找到父组件form\n\t\t\tconst parent = findParent(that, [parentLabelName]);\n\t\t\tconsole.log(\"==that==:\",that.$parent?.$options.name)\n\t\t\tif (parent == null) {\n\t\t\t\tconsole.error('error:','u-form-item must be used inside u-form');\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tconst item = {\n\t\t\t\tfield: props.field as string,\n\t\t\t\tinstance: that\n\t\t\t} as FormItemData;\n\t\t\tparent.$callMethod('pushFielditem', item)\n\t}\n\tonMounted(()=>{\n\t\tpushFormItemFieldToForm();\n\t})\n\tdefineExpose({\n\t\tverify\n\t})\n</script>\n\n<style lang=\"scss\" scoped>\n/* 表单项包装器 */\n.form-item-wrapper {\n  margin-bottom: 24rpx;\n  width: 100%;\n}\n\n/* 表单项行 - 输入框和错误提示在同一行 */\n.form-item-row {\n  display: flex;\n  align-items: center;\n  width: 100%;\n  gap: 16rpx;\n  /* 输入框和错误提示之间的间距 */\n}\n\n/* 输入框容器 */\n.input-container {\n  flex: 1;\n  min-width: 0;\n  /* 防止flex子项溢出 */\n}\n\n\n/* 内联错误提示样式 */\n.error-message-inline {\n  display: flex;\n  align-items: center;\n  flex-shrink: 0;\n  /* 防止错误提示被压缩 */\n  max-width: 240rpx;\n  /* 限制最大宽度，防止过长 */\n  padding: 8rpx 12rpx;\n  background-color: rgba(255, 77, 79, 0.08);\n  border-radius: 20rpx;\n  border: 1px solid rgba(255, 77, 79, 0.2);\n\n  /* 添加淡入动画 */\n  animation: errorSlideIn 0.3s ease-out;\n}\n\n/* 错误图标 */\n.error-icon {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  width: 28rpx;\n  height: 28rpx;\n  background-color: #ff4d4f;\n  color: white;\n  border-radius: 50%;\n  font-size: 18rpx;\n  font-weight: bold;\n  margin-right: 8rpx;\n  flex-shrink: 0;\n}\n\n/* 错误文本 */\n.error-text {\n  color: #ff4d4f;\n  font-size: 22rpx;\n  line-height: 1.3;\n  font-weight: 500;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n/* 错误提示动画 - 从右侧滑入 */\n@keyframes errorSlideIn {\n  0% {\n    opacity: 0;\n    transform: translateX(20rpx);\n  }\n\n  100% {\n    opacity: 1;\n    transform: translateX(0);\n  }\n}\n\n/* 响应式设计 */\n@media (max-width: 575px) {\n  .form-item-wrapper {\n    margin-bottom: 16rpx;\n  }\n\n  .form-item-row {\n    gap: 12rpx;\n  }\n\n  .error-message-inline {\n    max-width: 180rpx;\n    padding: 6rpx 10rpx;\n  }\n\n  .error-text {\n    font-size: 20rpx;\n  }\n\n  .error-icon {\n    width: 24rpx;\n    height: 24rpx;\n    font-size: 16rpx;\n    margin-right: 6rpx;\n  }\n}\n\n/* 超小屏幕适配 */\n@media (max-width: 400px) {\n  .form-item-row {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 8rpx;\n  }\n\n  .error-message-inline {\n    max-width: 100%;\n    align-self: flex-start;\n  }\n}\n</style>"]}