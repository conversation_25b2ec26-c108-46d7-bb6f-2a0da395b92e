{"version": 3, "sources": ["components/uc/u-from-item/u-from-item.uvue"], "names": [], "mappings": "AAeI,OAAO,EAAE,YAAY,EAAC,YAAY,EAAC,oBAAoB,EAAE,MAAM,UAAU,CAAA;AACzE,OAAO,EAAE,SAAS,EAAC,UAAU,EAAE,MAAM,UAAU,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAC/C,MAAM,OAAO,GAAG,GAAG,CAAC,IAAI,CAAC,CAAA;QACzB,MAAM,eAAe,GAAG,QAAQ,CAAA;QACjC,MAAM,WAAW,GAAG,GAAG,CAAC,QAAQ,CAAC,CAAA;QAChC,MAAM,QAAQ,GAAG,kBAAkB,EAAE,CAAC,CAAC;QACvC,MAAM,KAAK,GAAG,OAuBZ,CAAC;QAEH,MAAM,QAAQ,GAAG,QAAQ,CAAC,wBAAG,EAAE;YAC7B,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,SAAS,EAAC,iBAAgB,EAAA,mDAAC,CAAC,CAAC;YAC/C,MAAM,EAAE,EAAC,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,GAAI,IAAI,GAAG,EAAE,CAAC;YAC1C,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,SAAS,EAAC,iBAAgB,EAAA,mDAAC,CAAC,CAAC;YAC/C,EAAE,CAAC,GAAG,CAAC,YAAY,EAAE,GAAG,KAAK,CAAC,SAAS,KAAK,CAAC,CAAC;YAC9C,OAAO,EAAE,CAAC;QACZ,CAAC,CAAC,CAAA;QACL,SAAS,MAAM,CAAC,KAAK,EAAG,GAAG,EAAE,QAAQ,EAAG,CAAC,GAAG,EAAG,oBAAoB,KAAK,IAAI;YAC3E,OAAO,SAAS,CAAC,OAAO,EAAE,WAAW,EAAE,KAAK,CAAC,KAAK,IAAI,MAAM,EAAE,KAAK,CAAC,IAAI,IAAI,YAAY,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAA;QAC3G,CAAC;QACD,SAAS,uBAAuB;YAC9B,MAAM,IAAI,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;YAC7B,WAAW;YACX,MAAM,MAAM,GAAG,UAAU,CAAC,IAAI,EAAE,CAAC,eAAe,CAAC,CAAC,CAAC;YACnD,OAAO,CAAC,GAAG,CAAC,WAAW,EAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,IAAG,EAAA,mDAAC,CAAC,CAAA;YACpD,IAAI,MAAM,IAAI,IAAI,EAAE;gBACnB,OAAO,CAAC,KAAK,CAAC,QAAQ,EAAC,wCAAuC,EAAA,mDAAC,CAAC,CAAC;gBACjE,OAAO;aACP;YACD,MAAM,IAAI,GAAG;gBACZ,KAAK,EAAE,KAAK,CAAC,KAAK,IAAI,MAAM;gBAC5B,QAAQ,EAAE,IAAI;aACd,IAAI,YAAY,CAAC;YAClB,MAAM,CAAC,WAAW,CAAC,eAAe,EAAE,IAAI,CAAC,CAAA;QAC3C,CAAC;QACD,SAAS,CAAC,GAAE,EAAE;YACb,uBAAuB,EAAE,CAAC;QAC3B,CAAC,CAAC,CAAA;QACF,QAAY,CAAC;YACZ,MAAM;SACN,CAAC,CAAA;;mBA3EU,kBAAC,CAAA,MAAmB,EAAA,QAAA,CAAA;gBAAE,KAAK,EAAA,mBAAE;;cAEtC,EAAA;kCAAW,CAAA,MAAC,EAAA,QAAA,CAAA;;kBACV,EAAA;;gCAGuC,CAAA;sBAAzC,CAAA,IAAA,CAAA,SAAA,IAGO,CAAA,KAAA,CAAA,OAAA,CAAA,CAAA;;wBAHD,GAAA,EAAK,CAAA;;sBAET,EAAA", "sourcesContent": ["<template>\n  <view class=\"form-item-wrapper\" :style=\"getStyle\">\n    <!-- 输入框容器 -->\n    <view class=\"input-container\" :class=\"{ 'has-error': showError && !isValid }\">\n      <slot></slot>\n    </view>\n    <!-- 错误提示 - 在输入框下方显示，不影响布局 -->\n    <view class=\"error-message-bottom\" v-if=\"showError && !isValid\">\n      <!-- <view class=\"error-icon\">!</view> -->\n      <text class=\"error-text\">{{ hintMessage }}</text>\n    </view>\n  </view>\n</template>\n\n<script lang=\"uts\" setup>\n    import { FormItemData,FormItemRule,FormItemVerifyResult } from \"../types\"\n    import { useVerify,findParent } from \"../utils\"\n    const isValid = ref(true)\n    const parentLabelName = \"u-form\"\n\t  const hintMessage = ref('\\u3000')\n    const instance = getCurrentInstance()!;\n    const props = defineProps({\n        customStyle: {\n          type: Object as PropType<UTSJSONObject>,\n          default: {} as UTSJSONObject as UTSJSONObject\n        },\n        field: {\n            type: String,\n            default: ''\n        },\n        rule: {\n          type: Object as PropType<FormItemRule>,\n          default: (): FormItemRule =>{\n            return {} as FormItemRule\n          }\n        },\n        showError:{\n          type:Boolean,\n          default:true\n        },\n        marginTop:{\n          type:Number,\n          default:0\n        }\n    });\n\n    const getStyle = computed(() => {\n      console.log(props.marginTop,\"props.marginTop\");\n      const mp:Map<string, string>  = new Map();\n      console.log(props.marginTop,\"props.marginTop\");\n      mp.set('margin-top', `${props.marginTop}rpx`);\n      return mp;\n    })\n\tfunction verify(value : any, callback : (res : FormItemVerifyResult) => void){\n\t\treturn useVerify(isValid, hintMessage, props.field as string, props.rule as FormItemRule, value, callback)\n\t}\n\tfunction pushFormItemFieldToForm(){\n\t\t\tconst that = instance.proxy!;\n\t\t\t//找到父组件form\n\t\t\tconst parent = findParent(that, [parentLabelName]);\n\t\t\tconsole.log(\"==that==:\",that.$parent?.$options.name)\n\t\t\tif (parent == null) {\n\t\t\t\tconsole.error('error:','u-form-item must be used inside u-form');\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tconst item = {\n\t\t\t\tfield: props.field as string,\n\t\t\t\tinstance: that\n\t\t\t} as FormItemData;\n\t\t\tparent.$callMethod('pushFielditem', item)\n\t}\n\tonMounted(()=>{\n\t\tpushFormItemFieldToForm();\n\t})\n\tdefineExpose({\n\t\tverify\n\t})\n</script>\n\n<style lang=\"scss\" scoped>\n/* 表单项包装器 */\n.form-item-wrapper {\n  margin-bottom: 24rpx;\n  width: 100%;\n}\n\n/* 输入框容器 */\n.input-container {\n  width: 100%;\n  border: 1px solid #EEEEEE;\n  border-radius: 12rpx;\n  transition: border-color 0.3s ease;\n}\n\n/* 错误提示样式 - 显示在输入框下方 */\n.error-message-bottom {\n  display: flex;\n  align-items: flex-start;\n  margin-top: 8rpx;\n  padding: 12rpx 16rpx;\n  background-color: #fff2f0;\n  border-radius: 8rpx;\n  border-left: 4rpx solid #ff4d4f;\n  box-shadow: 0 2rpx 8rpx rgba(255, 77, 79, 0.1);\n\n  /* 添加淡入动画 */\n  animation: errorFadeIn 0.3s ease-out;\n}\n\n.has-error {\n  border: 1px solid #ff4d4f;\n}\n\n/* 错误图标 */\n// .error-icon {\n//   display: flex;\n//   justify-content: center;\n//   align-items: center;\n//   width: 32rpx;\n//   height: 32rpx;\n//   background-color: #ff4d4f;\n//   color: white;\n//   border-radius: 50%;\n//   font-size: 20rpx;\n//   font-weight: bold;\n//   margin-right: 12rpx;\n//   flex-shrink: 0;\n//   margin-top: 2rpx;\n// }\n\n/* 错误文本 */\n.error-text {\n  color: #ff4d4f;\n  font-size: 24rpx;\n  line-height: 1.5;\n  font-weight: 500;\n  flex: 1;\n  word-wrap: break-word;\n  word-break: break-all;\n}\n\n/* 错误提示动画 - 淡入效果 */\n@keyframes errorFadeIn {\n  0% {\n    opacity: 0;\n    transform: translateY(-10rpx);\n    max-height: 0;\n  }\n\n  100% {\n    opacity: 1;\n    transform: translateY(0);\n    max-height: 200rpx;\n  }\n}\n\n/* 响应式设计 */\n@media (max-width: 575px) {\n  .form-item-wrapper {\n    margin-bottom: 16rpx;\n  }\n\n  .error-message-bottom {\n    padding: 10rpx 14rpx;\n    margin-top: 6rpx;\n  }\n\n  .error-text {\n    font-size: 22rpx;\n  }\n\n  .error-icon {\n    width: 28rpx;\n    height: 28rpx;\n    font-size: 18rpx;\n    margin-right: 10rpx;\n  }\n}\n</style>"]}