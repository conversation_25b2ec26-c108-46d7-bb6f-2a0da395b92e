<template>
  <view class="login-container">
    <fui-status-bar></fui-status-bar>
    <!-- 右上角渐变球 -->
    <view class="gradient-circle top-right"></view>
    <!-- 左下角渐变球 -->
    <view class="gradient-circle bottom-left"></view>
    <!-- 切换登录 -->
    <!-- 登录表单 -->
    <view class="form-container">
      <!-- Logo/标题 -->
      <view class="logo-title">
        <text class="title-text">登录</text>
      </view>
      <!-- 手机号登录 -->
      <fui-tabs class="form-login-tabs" :tabs="tabsList" :short="false" center :current="current"
        @change="handleChangeTab"></fui-tabs>
      <view class="login-form-container">

        <block v-if="current === 0">
          <!-- TODO: 要加前缀 +86 fui-dropdown-menu -->
          <uForm ref="loginForm" @submited="onSubmit" triggerChange v-model="phoneModel">
            <uFromItem field="phone" :rule="phoneRules">
              <fui-input ref="phone" @blur="onBlur('phone')" inputBorder :size="28" :radius="12" :marginTop="16"
                type="number" placeholder="请输入手机号" v-model="phoneModel.phone as string">
              </fui-input>
            </uFromItem>
            <uFromItem ref="code" field="code" :rule="codeRules">
              <fui-input @blur="onBlur('code')" inputBorder :size="28" :radius="12" :marginTop="16" type="number"
                placeholder="请输入验证码" v-model="phoneModel.code as string">

              </fui-input>
            </uFromItem>
          </uForm>
        </block>

        <!-- 账号登录 -->
        <block v-else>
          2
        </block>
      </view>

      <!-- 协议同意 -->
      <view class="agreement">
        <fui-row justify="center" class="agreement-row">
          <!-- 必须要这样写，转kt的时候不这样写会出现问题 -->
          <fui-checkbox class="agreement-checkbox" :checked="isAgreeProtocol" borderRadius="8rpx" :scaleRatio="0.78"
            @change="ChangeIsAgreeProtocol"></fui-checkbox>
          <text class="agreement-text">我已阅读并同意</text>
          <text class="agreement-text agreement-link">《用户服务条款》</text>
          <text class="agreement-text">和</text>
          <text class="agreement-text agreement-link">《隐私协议》</text>
        </fui-row>
      </view>

      <!-- 登录按钮 -->
    </view>
    <!-- 底部 -->
    <view class="footer">
      <fui-footer text="by@海南长养乔智能科技有限责任公司"></fui-footer>
    </view>
  </view>
</template>

<script setup lang="uts">
	const inputStyle = {
		inputBorder: true,
		size: 28,
		radius: 12,
		marginTop: 16,
		type: "number"
	}
	// 引入组件
	import fuiStatusBar from "@/components/firstui/fui-status-bar/fui-status-bar";
	import fuiFooter from "@/components/firstui/fui-footer/fui-footer";
	import fuiRow from "@/components/firstui/fui-row/fui-row";
	import fuiTabs from "@/components/firstui/fui-tabs/fui-tabs";
	import fuiInput from "@/components/firstui/fui-input/fui-input";
	import fuiButton from "@/components/firstui/fui-button/fui-button";
	import fuiIcon from "@/components/firstui/fui-icon/fui-icon";
	import fuiCheckbox from "@/components/firstui/fui-checkbox/fui-checkbox";
	import { FuiTabsItemParam } from "@/components/firstui/fui-types/index.uts";
	import uForm from "@/components/uc/u-form/u-form";
	import uFromItem from "@/components/uc/u-from-item/u-from-item";
	import { phoneModelType } from "./types";
	import { FormItemVerifyResult, FormValidResult, FormValidResultItem, FormItemData, FormItemRule } from "@/components/uc/types/index.uts"
	import { ComponentPublicInstance } from 'vue'

	const loginForm = ref<ComponentPublicInstance | null>(null)
	function onBlur(field : string) {
		console.log("field:", field)
		const f = loginForm.value;
		if (f != null) {
			// 使用 $callMethod 调用组件方法
			f.$callMethod('validItem', field, {
				success() {
					console.log("success");
				},
				fail(res) {
					console.log("fail:", res);
					// 移除不支持的 $refs[field] 语法
					console.log("field :", field);
				}
			} as FormValidResultItem)
		}
	}
	// 标签页数据
	const tabsList = ref([
		{ name: '手机号登录', id: 0 },
		{ name: '账号登录', id: 1 },
	]);

	// 当前选中的标签页
	const current = ref(0);
	function handleChangeTab(e : FuiTabsItemParam) {
		console.log("handleChangeTab:", e);
		if (e.index !== null) {
			current.value = e.index as number;
		}
	}
	// 手机号登录表单
	const phoneModel = reactive<phoneModelType>({
		phone: '',
		code: ''
	})
	// 表单配置
	const phoneRules = ref<FormItemRule>({
		type: 'phone',
		required: true,
		message: '请输入正确的手机号'
	})
	const codeRules = ref<FormItemRule>({
		type: 'number',
		required: true,
		message: '请输入验证码'
	})
	// 国家区号
	const countryCode = ref('86');

	// 验证码相关
	const codeText = ref('获取验证码');
	const canGetCode = ref(true);
	//-------
	// 账号登录表单
	const accountForm = reactive({
		username: '',
		password: '',
	});

	// 协议同意
	const isAgreeProtocol = ref(false);
	const isDisabledloginButton = computed(() => {
		return !isAgreeProtocol.value;
	});

	// 登录按钮是否禁用
	function ChangeIsAgreeProtocol() {
		isAgreeProtocol.value = !isAgreeProtocol.value;
	}
	function onSubmit(e : any) {
		console.log("onSubmit:", e);
	}
</script>

<style lang="scss" scoped>
.login-container {
  height: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  // overflow: hidden;
}

.gradient-circle {
  position: absolute;
  /* #ifdef APP */
  border-radius: 200rpx;
  /* #endif */
  /* #ifdef WEB */
  border-radius: 50%;
  /* #endif */
  background: radial-gradient(circle, #33a1fd 0%, #0062e6 70%);
  background-color: #33a1fd;
  /* 备用背景颜色 */
  z-index: 0;
}

/* 添加调试样式 */
.gradient-circle::after {
  // content: '调试: 渐变球';
  // color: red;
  font-size: 12px;
  position: absolute;
  top: 0;
  left: 0;
}

.top-right {
  top: -80rpx;
  right: -30rpx;
  width: 300rpx;
  /* 调整宽度 */
  height: 300rpx;
  /* 调整高度 */
  opacity: 0.08;
}

.bottom-left {
  bottom: -60rpx;
  left: -60rpx;
  width: 280rpx;
  /* 调整宽度 */
  height: 280rpx;
  /* 调整高度 */
  opacity: 0.1;
}


.logo-title {
  text-align: center;
  margin: 10rpx 0 30rpx;
}

.title-text {
  font-size: 58rpx;
  font-weight: bold;
  color: $fui-color-primary;
  //   font-family: ;
}

.form-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 70%;

  .login-form-container {
    // width: 100%;
    padding: 0 16rpx;
    display: flex;
    min-width: 580rpx;
  }

  .form-login-tabs {}
}

.agreement {
  display: flex;
  align-items: flex-start;
  margin-top: 24rpx;
  color: $fui-color-minor;

  .agreement-row {
    align-items: center;
  }
}

.agreement-text {
  font-size: 24rpx;
}

.agreement-link {
  color: $fui-color-primary;
  text-decoration: none;
  font-weight: 500;
}

.footer {
  text-align: center;
  padding: 16rpx;
  color: $fui-color-minor;
  font-size: $fui-input-size;
  margin-top: 20rpx;
  // font-family: var(--font-content);
}
</style>