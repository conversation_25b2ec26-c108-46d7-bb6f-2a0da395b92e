<template>
  <view class="login-container">
    <!-- 页面级 Toast 提示框 -->
    <uToast ref="toastRef" :visible="toastVisible" :message="toastMessage" :type="toastType" :icon="toastIcon"
      :iconColor="toastIconColor" :duration="4000" :position="'top'" :top="120" @close="onToastClose" />

    <fui-status-bar></fui-status-bar>
    <!-- 右上角渐变球 -->
    <view class="gradient-circle top-right"></view>
    <!-- 左下角渐变球 -->
    <view class="gradient-circle bottom-left"></view>
    <!-- 切换登录 -->
    <!-- 登录表单 -->
    <view class="form-container">
      <!-- Logo/标题 -->
      <view class="logo-title">
        <text class="title-text">登录</text>
      </view>
      <!-- 手机号登录 -->
      <fui-tabs class="form-login-tabs" :tabs="tabsList" :short="false" center :current="current"
        @change="handleChangeTab"></fui-tabs>
      <view class="login-form-container">

        <block v-if="current === 0">
          <!-- TODO: 要加前缀 +86 fui-dropdown-menu -->
          <uForm ref="loginForm" @submited="onSubmit" v-model="phoneModel" :showToast="true" :toastTop="120"
            :toastDuration="4000">
            <uFromItem field="phone" :rule="phoneRules" :marginTop="16">
              <fui-input ref="phone" @blur="onBlur('phone')" :borderBottom="false" :size="28" :radius="12" type="number"
                placeholder="请输入手机号" v-model="phoneModel.phone as string">
              </fui-input>
            </uFromItem>
            <uFromItem field="code" :rule="codeRules" :marginTop="16">
              <fui-input ref="code" @blur="onBlur('code')" :borderBottom="false" :size="28" :radius="12" type="number"
                placeholder="请输入验证码" v-model="phoneModel.code as string">

              </fui-input>
            </uFromItem>
          </uForm>
        </block>

        <!-- 账号登录 -->
        <block v-else>
          2
        </block>
      </view>

      <!-- 协议同意 -->
      <view class="agreement">
        <fui-row justify="center" class="agreement-row">
          <!-- 必须要这样写，转kt的时候不这样写会出现问题 -->
          <fui-checkbox class="agreement-checkbox" :checked="isAgreeProtocol" borderRadius="8rpx" :scaleRatio="0.78"
            @change="ChangeIsAgreeProtocol"></fui-checkbox>
          <text class="agreement-text">我已阅读并同意</text>
          <text class="agreement-text agreement-link">《用户服务条款》</text>
          <text class="agreement-text">和</text>
          <text class="agreement-text agreement-link">《隐私协议》</text>
        </fui-row>
      </view>

      <!-- 验证测试按钮 -->
      <view class="test-buttons">
        <button class="test-btn validate" @click="testValidation">测试验证</button>
        <button class="test-btn svg" @click="testSvgToast">成功提示</button>
        <button class="test-btn emoji" @click="testEmojiToast">Emoji图标</button>
      </view>

      <!-- 登录按钮 -->
    </view>
    <!-- 底部 -->
    <view class="footer">
      <fui-footer text="by@海南长养乔智能科技有限责任公司"></fui-footer>
    </view>
  </view>
</template>

<script setup lang="uts">
	const inputStyle = {
		inputBorder: true,
		size: 28,
		radius: 12,
		marginTop: 16,
		type: "number"
	}
	// 引入组件
	import fuiStatusBar from "@/components/firstui/fui-status-bar/fui-status-bar";
	import fuiFooter from "@/components/firstui/fui-footer/fui-footer";
	import fuiRow from "@/components/firstui/fui-row/fui-row";
	import fuiTabs from "@/components/firstui/fui-tabs/fui-tabs";
	import fuiInput from "@/components/firstui/fui-input/fui-input";
	import fuiButton from "@/components/firstui/fui-button/fui-button";
	import fuiIcon from "@/components/firstui/fui-icon/fui-icon";
	import fuiCheckbox from "@/components/firstui/fui-checkbox/fui-checkbox";
	import { FuiTabsItemParam } from "@/components/firstui/fui-types/index.uts";
	import uForm from "@/components/uc/u-form/u-form";
	import uFromItem from "@/components/uc/u-from-item/u-from-item";
	import uToast from "@/components/uc/u-toast/u-toast";
	import { phoneModelType } from "./types";
	import { FormItemVerifyResult, FormValidResult, FormValidResultItem, FormItemData, FormItemRule, ToastType, ToastPosition } from "@/components/uc/types/index.uts"
	import { ComponentPublicInstance } from 'vue'
	const instance = getCurrentInstance();

	const loginForm = ref<ComponentPublicInstance | null>(null)
	function onBlur(field : 'phone' | 'code') {
		console.log("field:", field)
		const f = loginForm.value;
		if (f != null) {
			// 使用 $callMethod 调用组件方法
			f.$callMethod('validItem', field, {
				success() {
					console.log("success");
				},
				fail(res) {
					console.log("fail:", res);
				}
			} as FormValidResultItem)
		}
	}
	// 标签页数据
	const tabsList = ref([
		{ name: '手机号登录', id: 0 },
		{ name: '账号登录', id: 1 },
	]);

	// 当前选中的标签页
	const current = ref(0);
	function handleChangeTab(e : FuiTabsItemParam) {
		console.log("handleChangeTab:", e);
		if (e.index !== null) {
			current.value = e.index as number;
		}
	}
	// 手机号登录表单
	const phoneModel = reactive<phoneModelType>({
		phone: '',
		code: ''
	})
	// 表单配置
	const phoneRules = ref<FormItemRule>({
		type: 'phone',
		required: true,
		message: '请输入正确的手机号'
	})
	const codeRules = ref<FormItemRule>({
		type: 'number',
		required: true,
		message: '请输入验证码'
	})
	// 国家区号
	const countryCode = ref('86');

	// 验证码相关
	const codeText = ref('获取验证码');
	const canGetCode = ref(true);
	//-------
	// 账号登录表单
	const accountForm = reactive({
		username: '',
		password: '',
	});

	// 协议同意
	const isAgreeProtocol = ref(false);
	const isDisabledloginButton = computed(() => {
		return !isAgreeProtocol.value;
	});

	// 登录按钮是否禁用
	function ChangeIsAgreeProtocol() {
		isAgreeProtocol.value = !isAgreeProtocol.value;
	}
	function onSubmit(e : any) {
		console.log("onSubmit:", e);
	}

	// Toast 相关数据
	const toastRef = ref<ComponentPublicInstance | null>(null)
	const toastVisible = ref(false)
	const toastMessage = ref('')
	const toastType = ref<ToastType>('info')
	const toastIcon = ref('')
	const toastIconColor = ref('')

	// 定义 Toast 选项类型
	type ShowToastOptions = {
		message: string
		type?: ToastType
		icon?: string
		iconColor?: string
		duration?: number
	}

	// Toast 方法
	function showToast(options: ShowToastOptions) {
		toastMessage.value = options.message
		const optionType = options.type
		toastType.value = optionType != null ? optionType : 'info'
		const optionIcon = options.icon
		toastIcon.value = optionIcon != null ? optionIcon : ''
		const optionIconColor = options.iconColor
		toastIconColor.value = optionIconColor != null ? optionIconColor : ''
		toastVisible.value = true
	}

	function onToastClose() {
		toastVisible.value = false
	}

	// 测试验证功能
	function testValidation() {
		const formInstance = loginForm.value
		if (formInstance != null) {
			// 使用 $callMethod 调用组件方法
			formInstance.$callMethod('valid', {
				success: () => {
					console.log("表单验证成功");
				},
				fail: (failResults: FormItemVerifyResult[]) => {
					console.log("表单验证失败:", failResults);
				}
			})
		}
	}

	// 测试成功图标 Toast
	function testSvgToast() {
		const options: ShowToastOptions = {
			message: '这是成功提示！',
			type: 'success',
			icon: '✓'
		}
		showToast(options)
	}

	// 测试 Emoji 图标 Toast
	function testEmojiToast() {
		const options: ShowToastOptions = {
			message: '这是使用 Emoji 图标的提示！',
			type: 'warning',
			icon: '🎉'
		}
		showToast(options)
	}


</script>

<style lang="scss" scoped>
.login-container {
  height: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  // overflow: hidden;
}

.gradient-circle {
  position: absolute;
  /* #ifdef APP */
  border-radius: 200rpx;
  /* #endif */
  /* #ifdef WEB */
  border-radius: 50%;
  /* #endif */
  background: radial-gradient(circle, #33a1fd 0%, #0062e6 70%);
  background-color: #33a1fd;
  /* 备用背景颜色 */
  z-index: 0;
}

/* 添加调试样式 */
.gradient-circle::after {
  // content: '调试: 渐变球';
  // color: red;
  font-size: 12px;
  position: absolute;
  top: 0;
  left: 0;
}

.top-right {
  top: -80rpx;
  right: -30rpx;
  width: 300rpx;
  /* 调整宽度 */
  height: 300rpx;
  /* 调整高度 */
  opacity: 0.08;
}

.bottom-left {
  bottom: -60rpx;
  left: -60rpx;
  width: 280rpx;
  /* 调整宽度 */
  height: 280rpx;
  /* 调整高度 */
  opacity: 0.1;
}


.logo-title {
  text-align: center;
  margin: 10rpx 0 30rpx;
}

.title-text {
  font-size: 58rpx;
  font-weight: bold;
  color: $fui-color-primary;
  //   font-family: ;
}

.form-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 70%;

  .login-form-container {
    // width: 100%;
    padding: 0 16rpx;
    display: flex;
    min-width: 580rpx;
  }

  .form-login-tabs {}
}

.agreement {
  display: flex;
  align-items: flex-start;
  margin-top: 24rpx;
  color: $fui-color-minor;

  .agreement-row {
    align-items: center;
  }
}

.agreement-text {
  font-size: 24rpx;
}

.agreement-link {
  color: $fui-color-primary;
  text-decoration: none;
  font-weight: 500;
}

.footer {
  text-align: center;
  padding: 16rpx;
  color: $fui-color-minor;
  font-size: $fui-input-size;
  margin-top: 20rpx;
  // font-family: var(--font-content);
}

/* Toast 测试按钮样式 */
.test-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-top: 32rpx;
  justify-content: center;
}

.test-btn {
  padding: 16rpx 24rpx;
  border-radius: 8rpx;
  border: none;
  font-size: 24rpx;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120rpx;

  &.validate {
    background-color: #ff4d4f;

    &:hover {
      background-color: #ff7875;
    }
  }

  &.svg {
    background-color: #52c41a;

    &:hover {
      background-color: #73d13d;
    }
  }

  &.emoji {
    background-color: #faad14;

    &:hover {
      background-color: #ffc53d;
    }
  }
}
</style>