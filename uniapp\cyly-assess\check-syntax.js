// 简单的语法检查脚本
const fs = require('fs');
const path = require('path');

function checkFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    console.log(`✅ ${filePath} - 文件读取成功`);
    
    // 检查一些基本的语法问题
    const lines = content.split('\n');
    let hasErrors = false;
    
    lines.forEach((line, index) => {
      // 检查常见的语法错误
      if (line.includes('validItem') && !line.includes('$callMethod')) {
        console.log(`⚠️  第 ${index + 1} 行可能有问题: ${line.trim()}`);
        hasErrors = true;
      }
    });
    
    if (!hasErrors) {
      console.log(`✅ ${filePath} - 语法检查通过`);
    }
    
  } catch (error) {
    console.log(`❌ ${filePath} - 错误: ${error.message}`);
  }
}

// 检查主要文件
const filesToCheck = [
  'pages/login/login.uvue',
  'components/uc/u-form/u-form.uvue',
  'components/uc/u-from-item/u-from-item.uvue'
];

console.log('🔍 开始语法检查...\n');

filesToCheck.forEach(file => {
  const fullPath = path.join(__dirname, file);
  checkFile(fullPath);
});

console.log('\n✨ 语法检查完成！');
