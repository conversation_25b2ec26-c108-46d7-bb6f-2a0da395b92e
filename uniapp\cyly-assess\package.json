{"id": "hello-uvue", "name": "hello-uvue", "displayName": "hello-uvue", "version": "1.0.18", "description": "uvue的vue语法示例工程", "main": "env.js", "scripts": {"dev:app": "uni build --platform app --mode development", "dev:app-android": "uni build --platform app-android --mode development", "dev:app-ios": "uni build --platform app-ios --mode development", "dev:h5": "uni serve --platform h5", "dev:mp-weixin": "uni build --platform mp-weixin --mode development --watch", "build:app": "uni build --platform app --mode production", "build:app-android": "uni build --platform app-android --mode production", "build:app-ios": "uni build --platform app-ios --mode production", "build:h5": "uni build --platform h5 --mode production", "build:mp-weixin": "uni build --platform mp-weixin --mode production", "test": "echo \"Error: no test specified\" && exit 1"}, "repository": "", "keywords": ["uvue", "uni-app2.0"], "author": "", "license": "MIT", "engines": {"HBuilderX": "^3.96"}, "dcloudext": {"sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "无", "permissions": "无"}, "npmurl": "", "type": "uniapp-template-project"}, "uni_modules": {"dependencies": [], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y", "alipay": "n"}, "client": {"App": {"app-vue": "n", "app-nvue": "n", "app-uvue": "y"}, "H5-mobile": {"Safari": "n", "Android Browser": "u", "微信浏览器(Android)": "u", "QQ浏览器(Android)": "u"}, "H5-pc": {"Chrome": "u", "IE": "u", "Edge": "u", "Firefox": "u", "Safari": "u"}, "小程序": {"微信": "u", "阿里": "u", "百度": "u", "字节跳动": "u", "QQ": "u", "京东": "u", "钉钉": "u", "快手": "u", "飞书": "u"}, "快应用": {"华为": "u", "联盟": "u"}, "Vue": {"vue2": "n", "vue3": "y"}}}}, "devDependencies": {"@dcloudio/types": "^3.4.15", "@eslint/css": "^0.8.1", "@eslint/js": "^9.27.0", "@vue/eslint-config-typescript": "^14.5.0", "eslint": "^9.27.0", "eslint-plugin-vue": "^10.1.0", "globals": "^16.1.0", "typescript-eslint": "^8.32.1"}}