{"version": 3, "sources": ["components/uc/u-form/u-form.uvue", "main.uts"], "sourcesContent": ["<template>\n\t<view class=\"u-form\" :style=\"[customStyle]\">\n\t\t<!-- Toast 提示框 -->\n\t\t<uToast ref=\"toastRef\" :visible=\"toastVisible\" :message=\"toastMessage\" :type=\"toastType\" :icon=\"toastIcon\"\n\t\t\t:duration=\"toastDuration\" :position=\"toastPosition\" :top=\"toastTop\" @close=\"onToastClose\" />\n\t\t<slot></slot>\n\t</view>\n</template>\n\n<script lang=\"uts\" setup>\n\timport { FormItemData, FormItemVerifyResult, FormValidResult, FormValidResultItem, ToastType, ToastPosition } from \"../types\";\n\timport uToast from \"../u-toast/u-toast\";\n\timport { ComponentPublicInstance } from 'vue'\n\n\tdefineOptions({\n\t\tname: 'u-form'\n\t})\n\tconst props = defineProps({\n\t\tcustomStyle: {\n\t\t\ttype: Object as PropType<UTSJSONObject>,\n\t\t\tdefault: {} as UTSJSONObject as UTSJSONObject\n\t\t},\n\t\ttriggerChange: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: false\n\t\t},\n\t\t// Toast 相关配置\n\t\tshowToast: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: true\n\t\t},\n\t\ttoastPosition: {\n\t\t\ttype: String as PropType<ToastPosition>,\n\t\t\tdefault: 'top' as ToastPosition\n\t\t},\n\t\ttoastTop: {\n\t\t\ttype: Number,\n\t\t\tdefault: 100\n\t\t},\n\t\ttoastDuration: {\n\t\t\ttype: Number,\n\t\t\tdefault: 3000\n\t\t}\n\t});\n\tconst fieldItems = ref<FormItemData[]>([])\n\n\tconst emit = defineEmits(['submited', 'change', 'focus'])\n\n\tconst model = defineModel({\n\t\ttype: Object as PropType<UTSJSONObject>,\n\t\tdefault: {} as UTSJSONObject\n\t})\n\n\t// Toast 相关数据\n\tconst toastRef = ref<ComponentPublicInstance | null>(null)\n\tconst toastVisible = ref(false)\n\tconst toastMessage = ref('')\n\tconst toastType = ref<ToastType>('error')\n\tconst toastIcon = ref('✕')\n\n\t// Toast 方法\n\tfunction showErrorToast(message: string) {\n\t\tconsole.log('u-form showErrorToast called with message:', message, 'showToast:', props.showToast)\n\t\tif (!props.showToast) return\n\n\t\ttoastMessage.value = message\n\t\ttoastType.value = 'error'\n\t\ttoastIcon.value = ''  // 使用默认图标\n\t\ttoastVisible.value = true\n\t\tconsole.log('u-form toast state set - visible:', toastVisible.value, 'message:', toastMessage.value)\n\t}\n\n\tfunction onToastClose() {\n\t\ttoastVisible.value = false\n\t}\n\n\t// 先定义 valid 函数\n\tfunction valid(callback : FormValidResult) {\n\t\tlet allow = true\n\t\tlet verifyRes = [] as FormItemVerifyResult[]\n\t\tfieldItems.value.forEach((item : FormItemData) => {\n\t\t\tlet field = item.field\n\t\t\tlet value = (model.value as UTSJSONObject)[field]\n\t\t\tif (value != null) {\n\t\t\t\tlet _item = fieldItems.value.find((it : FormItemData) : boolean => it.field == field)\n\t\t\t\tif (_item != null) {\n\t\t\t\t\titem.instance.$callMethod('verify', value, (res : FormItemVerifyResult) => {\n\t\t\t\t\t\tif (!res.valid) {\n\t\t\t\t\t\t\tallow = false\n\t\t\t\t\t\t\tverifyRes.push(res)\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tallow = false\n\t\t\t}\n\t\t})\n\t\tif (allow) {\n\t\t\tif (callback.success != null) {\n\t\t\t\tcallback.success!()\n\t\t\t}\n\t\t} else {\n\t\t\t// 显示第一个验证失败的错误信息\n\t\t\tif (verifyRes.length > 0) {\n\t\t\t\tconst errorMessage = verifyRes[0].message\n\t\t\t\tshowErrorToast(errorMessage != null ? errorMessage : '验证失败')\n\t\t\t}\n\t\t\tif (callback.fail != null) {\n\t\t\t\tcallback.fail!(verifyRes)\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction onSubmit(callback : FormValidResult) {\n\t\tvalid({\n\t\t\tsuccess: () => {\n\t\t\t\tif (callback.success != null) {\n\t\t\t\t\temit('submited')\n\t\t\t\t\tcallback.success!()\n\t\t\t\t}\n\t\t\t},\n\t\t\tfail: (failResults : FormItemVerifyResult[]) => {\n\t\t\t\tif (callback.fail != null) {\n\t\t\t\t\tcallback.fail!(failResults)\n\t\t\t\t}\n\t\t\t}\n\t\t} as FormValidResult)\n\t}\n\n\tfunction pushFielditem(fieldItem : FormItemData) {\n\t\tif (fieldItems.value.find((item : FormItemData) : boolean => item.field == fieldItem.field) != null) {\n\t\t\treturn;\n\t\t}\n\t\tconsole.log(fieldItem);\n\t\tfieldItems.value.push(fieldItem);\n\t\tconsole.log(\"pushFielditem-fieldItems:\", fieldItems.value.map(v=>v.field));\n\t}\n\tfunction validItem(field : string, callback : FormValidResultItem) {\n\t\tconst fieldItem = fieldItems.value.find(item => item.field == field);\n\t\tconsole.log(\"===fieldItems===:\", fieldItems);\n\t\tconsole.log(\"model:\", model);\n\t\tconst value = (model.value as UTSObject)[field]\n\t\tif (fieldItem != null) {\n\t\t\tfieldItem.instance.$callMethod('verify', value, (res : FormItemVerifyResult) => {\n\t\t\t\tif (!res.valid) {\n\t\t\t\t\t// 显示验证失败的错误信息\n\t\t\t\t\tconst errorMessage = res.message\n\t\t\t\t\tshowErrorToast(errorMessage != null ? errorMessage : '验证失败')\n\t\t\t\t\tif (callback.fail != null) {\n\t\t\t\t\t\tcallback.fail!(res)\n\t\t\t\t\t}\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tif (callback.success != null) {\n\t\t\t\t\tcallback.success!()\n\t\t\t\t}\n\t\t\t})\n\t\t} else {\n\t\t\t// 如果找不到字段项，调用失败回调\n\t\t\tconst errorMsg = `字段 ${field} 不存在`\n\t\t\tshowErrorToast(errorMsg)\n\t\t\tif (callback.fail != null) {\n\t\t\t\tcallback.fail!({\n\t\t\t\t\tvalid: false,\n\t\t\t\t\tmessage: errorMsg,\n\t\t\t\t\tfield: field\n\t\t\t\t} as FormItemVerifyResult)\n\t\t\t}\n\t\t}\n\t}\n\twatch(model, (value : UTSJSONObject) => {\n\t\temit('change', value)\n\t\t//TODO 测试\n\n\t\tif (props.triggerChange) {\n\t\t\tvalid({\n\t\t\t\tsuccess() {\n\t\t\t\t\tconsole.log(\"?\");\n\t\t\t\t},\n\t\t\t\tfail(res) {\n\t\t\t\t\tconsole.log(\"???\", res);\n\t\t\t\t}\n\t\t\t})\n\t\t}\n\t}, {\n\t\tdeep: true\n\t})\n\tdefineExpose({\n\t\tonSubmit,\n\t\tpushFielditem,\n\t\tvalidItem,\n\t\tvalid\n\t});\n</script>", null], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;wBAiHmB;;sDAvGlB,EAAA,EAuGkB;;;;;6BAgBK;;2DAvHvB,EAAA,EAuHuB;;;;;yBAQJ,OAAQ,MAAM,EAAE;;uDA/HnC,EAAA,EA+HmB,OAAQ,MAAM,EAAE;;;;;qBA5DpB;;mDAnEf,EAAA,EAmEe;;;;;;;;;;;;YA5Df,IAAM,QAAQ;YA2Bd,IAAM,aAAa;YAEnB,IAAA,KAAA,OAAA,MAAA,EAAA,OAAA,yBAAA,GAAA,CAAA,EAAA;gBAAA,MAAA,IAAA,CAAA,QAAA;YAAA;YAEA,IAAM,QAAQ,SAAA,eAAA,MAAA,KAAA,EAAA;YAMd,IAAM,WAAW,IAAI,0BAAgC,IAAI;YACzD,IAAM,eAAe,IAAI,KAAK;YAC9B,IAAM,eAAe,IAAI;YACzB,IAAM,YAAY,eAAe;YACjC,IAAM,YAAY,IAAI;YAGtB,IAAS,sBAAe,SAAS,MAAM,EAAA;gBACtC,QAAQ,GAAG,CAAC,8CAA8C,SAAS,cAAc,MAAM,SAAQ,EAAA;gBAC/F,IAAI,CAAC,MAAM,SAAS;oBAAE;;gBAEtB,aAAa,KAAK,GAAG;gBACrB,UAAU,KAAK,GAAG;gBAClB,UAAU,KAAK,GAAG;gBAClB,aAAa,KAAK,GAAG,IAAI;gBACzB,QAAQ,GAAG,CAAC,qCAAqC,aAAa,KAAK,EAAE,YAAY,aAAa,KAAI,EAAA;YACnG;gBATS;YAWT,IAAS,sBAAY;gBACpB,aAAa,KAAK,GAAG,KAAK;YAC3B;gBAFS;YAKT,IAAS,aAAM,yBAA0B,EAAA;gBACxC,IAAI,QAAQ,IAAI;gBAChB,IAAI,YAAY;gBAChB,WAAW,KAAK,CAAC,OAAO,CAAC,IAAC,mBAAuB;oBAChD,IAAI,QAAQ,KAAK,KAAK;oBACtB,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAA,EAAA,CAAI,aAAa,CAAC,CAAC,MAAM;oBACjD,IAAI,SAAS,IAAI,EAAE;wBAClB,IAAI,QAAQ,WAAW,KAAK,CAAC,IAAI,CAAC,IAAC,mBAAqB,OAAO;mCAAI,GAAG,KAAK,IAAI;;wBAC/E,IAAI,SAAS,IAAI,EAAE;4BAClB,KAAK,QAAQ,CAAC,aAAW,CAAC,UAAU,OAAO,IAAC,0BAA8B;gCACzE,IAAI,CAAC,IAAI,KAAK,EAAE;oCACf,QAAQ,KAAK;oCACb,UAAU,IAAI,CAAC;;4BAEjB;;;2BAEK;wBACN,QAAQ,KAAK;;gBAEf;;gBACA,IAAI,OAAO;oBACV,IAAI,SAAS,OAAO,IAAI,IAAI,EAAE;wBAC7B,SAAS,OAAO;;uBAEX;oBAEN,IAAI,UAAU,MAAM,GAAG,CAAC,EAAE;wBACzB,IAAM,eAAe,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO;wBACzC,eAAe,IAAA,gBAAgB,IAAI;4BAAG;;4BAAe;;wBAAM;;oBAE5D,IAAI,SAAS,IAAI,IAAI,IAAI,EAAE;wBAC1B,SAAS,IAAI,GAAE;;;YAGlB;gBAlCS;YAoCT,IAAS,gBAAS,yBAA0B,EAAA;gBAC3C,sBACC,UAAS,KAAK;oBACb,IAAI,SAAS,OAAO,IAAI,IAAI,EAAE;wBAC7B,KAAK;wBACL,SAAS,OAAO;;gBAElB;kBACA,OAAM,IAAC,4CAAwC;oBAC9C,IAAI,SAAS,IAAI,IAAI,IAAI,EAAE;wBAC1B,SAAS,IAAI,GAAE;;gBAEjB;;YAEF;gBAdS;YAgBT,IAAS,qBAAc,uBAAwB,EAAA;gBAC9C,IAAI,WAAW,KAAK,CAAC,IAAI,CAAC,IAAC,qBAAuB,OAAO;2BAAI,KAAK,KAAK,IAAI,UAAU,KAAK;;qBAAK,IAAI,EAAE;oBACpG;;gBAED,QAAQ,GAAG,CAAC,WAAQ;gBACpB,WAAW,KAAK,CAAC,IAAI,CAAC;gBACtB,QAAQ,GAAG,CAAC,6BAA6B,WAAW,KAAK,CAAC,GAAG,CAAC,IAAA,IAAC,MAAA;2BAAE,EAAE,KAAK;;mBAAA;YACzE;gBAPS;YAQT,IAAS,iBAAU,OAAQ,MAAM,EAAE,6BAA8B,EAAA;gBAChE,IAAM,YAAY,WAAW,KAAK,CAAC,IAAI,CAAC,IAAA,OAAI,OAAA;2BAAI,KAAK,KAAK,IAAI;;;gBAC9D,QAAQ,GAAG,CAAC,qBAAqB,YAAS;gBAC1C,QAAQ,GAAG,CAAC,UAAU,OAAI;gBAC1B,IAAM,QAAQ,CAAC,MAAM,KAAK,CAAA,EAAA,CAAI,SAAS,CAAC,CAAC,MAAM;gBAC/C,IAAI,aAAa,IAAI,EAAE;oBACtB,UAAU,QAAQ,CAAC,aAAW,CAAC,UAAU,OAAO,IAAC,0BAA8B;wBAC9E,IAAI,CAAC,IAAI,KAAK,EAAE;4BAEf,IAAM,eAAe,IAAI,OAAO;4BAChC,eAAe,IAAA,gBAAgB,IAAI;gCAAG;;gCAAe;;4BAAM;4BAC3D,IAAI,SAAS,IAAI,IAAI,IAAI,EAAE;gCAC1B,SAAS,IAAI,GAAE;;4BAEhB;;wBAED,IAAI,SAAS,OAAO,IAAI,IAAI,EAAE;4BAC7B,SAAS,OAAO;;oBAElB;uBACM;oBAEN,IAAM,WAAW,kBAAM,QAAK;oBAC5B,eAAe;oBACf,IAAI,SAAS,IAAI,IAAI,IAAI,EAAE;wBAC1B,SAAS,IAAI,wBACZ,QAAO,KAAK,EACZ,UAAS,UACT,QAAO;;;YAIX;gBAhCS;YAiCT,MAAM,OAAO,IAAC,OAAQ,cAAiB;gBACtC,KAAK,UAAU;gBAGf,IAAI,MAAM,aAAa,EAAE;oBACxB,sBACC,UAAA,MAAO;wBACN,QAAQ,GAAG,CAAC,KAAE;oBACf;sBACA,OAAA,IAAK,GAAG,EAAA;wBACP,QAAQ,GAAG,CAAC,OAAO,KAAE;oBACtB;;;YAGH;2BACC,OAAM,IAAI;YAEX,gCACC,6BACA,8BACA,sBACA;;uBA9LU,mBAAS,QAAA,SAAE,WAAK;;qBAE1B;gCAAY,yCAAU,SAAd,aAAI,YAAY,SAAA,UAAwB,aAAS,MAAA,eAAe,aAAM,MAAA,eAAY,UAAM,MAAA,YAC9F,UAAA,MAAU,YAAgB,cAAU,KAAA,aAAa,EAAG,cAAK,KAAA,aAAQ,EAAG,SAAA,KAAO,QAAA,8BAC7E,IAAA,EAAA,CAAa,EAAA;wBAAA;wBAAA;wBAAA;wBAAA;wBAAA;wBAAA;wBAAA;qBAAA"}