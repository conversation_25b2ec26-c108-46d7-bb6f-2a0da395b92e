{"version": 3, "sources": ["components/uc/u-form/u-form.uvue", "main.uts"], "sourcesContent": ["<template>\n\t<view class=\"u-form\" :style=\"[customStyle]\">\n\t\t<slot></slot>\n\t</view>\n</template>\n\n<script lang=\"uts\" setup>\n\timport { FormItemData, FormItemVerifyResult, FormValidResult, FormValidResultItem } from \"../types\";\r\n\tdefineOptions({\r\n\t\tname: 'u-form'\r\n\t})\n\tconst props = defineProps({\n\t\tcustomStyle: {\n\t\t\ttype: Object as PropType<UTSJSONObject>,\n\t\t\tdefault: {} as UTSJSONObject as UTSJSONObject\n\t\t},\n\t\ttriggerChange: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: false\n\t\t}\n\t});\n\tconst fieldItems = ref<FormItemData[]>([])\n\n\tconst emit = defineEmits(['submited', 'change', 'focus'])\n\n\tconst model = defineModel({\n\t\ttype: Object as PropType<UTSJSONObject>,\n\t\tdefault: {} as UTSJSONObject\n\t})\n\n\t// 先定义 valid 函数\n\tfunction valid(callback : FormValidResult) {\n\t\tlet allow = true\n\t\tlet verifyRes = [] as FormItemVerifyResult[]\n\t\tfieldItems.value.forEach((item : FormItemData) => {\n\t\t\tlet field = item.field\n\t\t\tlet value = (model.value as UTSJSONObject)[field]\n\t\t\tif (value != null) {\n\t\t\t\tlet _item = fieldItems.value.find((it : FormItemData) : boolean => it.field == field)\n\t\t\t\tif (_item != null) {\n\t\t\t\t\titem.instance.$callMethod('verify', value, (res : FormItemVerifyResult) => {\n\t\t\t\t\t\tif (!res.valid) {\n\t\t\t\t\t\t\tallow = false\n\t\t\t\t\t\t\tverifyRes.push(res)\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tallow = false\n\t\t\t}\n\t\t})\n\t\tif (allow) {\n\t\t\tif (callback.success != null) {\n\t\t\t\tcallback.success!()\n\t\t\t}\n\t\t} else {\n\t\t\tif (callback.fail != null) {\n\t\t\t\tcallback.fail!(verifyRes)\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction onSubmit(callback : FormValidResult) {\n\t\tvalid({\n\t\t\tsuccess: () => {\n\t\t\t\tif (callback.success != null) {\n\t\t\t\t\temit('submited')\n\t\t\t\t\tcallback.success!()\n\t\t\t\t}\n\t\t\t},\n\t\t\tfail: (failResults : FormItemVerifyResult[]) => {\n\t\t\t\tif (callback.fail != null) {\n\t\t\t\t\tcallback.fail!(failResults)\n\t\t\t\t}\n\t\t\t}\n\t\t} as FormValidResult)\n\t}\n\n\tfunction pushFielditem(fieldItem : FormItemData) {\n\t\tif (fieldItems.value.find((item : FormItemData) : boolean => item.field == fieldItem.field) != null) {\n\t\t\treturn;\n\t\t}\n\t\tconsole.log(fieldItem);\n\t\tfieldItems.value.push(fieldItem);\n\t\tconsole.log(\"pushFielditem-fieldItems:\", fieldItems.value.map(v=>v.field));\n\t}\n\tfunction validItem(field : string, callback : FormValidResultItem) {\n\t\tconst fieldItem = fieldItems.value.find(item => item.field == field);\r\n\t\tconsole.log(\"===fieldItems===:\", fieldItems);\r\n\t\tconsole.log(\"model:\", model);\n\t\tconst value = (model.value as UTSObject)[field]\n\t\tif (fieldItem != null) {\n\t\t\tfieldItem.instance.$callMethod('verify', value, (res : FormItemVerifyResult) => {\n\t\t\t\tif (!res.valid) {\n\t\t\t\t\tif (callback.fail != null) {\n\t\t\t\t\t\tcallback.fail!(res)\n\t\t\t\t\t}\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tif (callback.success != null) {\n\t\t\t\t\tcallback.success!()\n\t\t\t\t}\n\t\t\t})\n\t\t} else {\n\t\t\t// 如果找不到字段项，调用失败回调\n\t\t\tif (callback.fail != null) {\n\t\t\t\tcallback.fail!({\n\t\t\t\t\tvalid: false,\n\t\t\t\t\tmessage: `字段 ${field} 不存在`,\n\t\t\t\t\tfield: field\n\t\t\t\t} as FormItemVerifyResult)\n\t\t\t}\n\t\t}\n\t}\n\twatch(model, (value : UTSJSONObject) => {\n\t\temit('change', value)\n\t\t//TODO 测试\n\n\t\tif (props.triggerChange) {\n\t\t\tvalid({\n\t\t\t\tsuccess() {\n\t\t\t\t\tconsole.log(\"?\");\n\t\t\t\t},\n\t\t\t\tfail(res) {\n\t\t\t\t\tconsole.log(\"???\", res);\n\t\t\t\t}\n\t\t\t})\n\t\t}\n\t}, {\n\t\tdeep: true\n\t})\n\tdefineExpose({\n\t\tonSubmit,\n\t\tpushFielditem,\n\t\tvalidItem,\n\t\tvalid\n\t});\n</script>", null], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;wBA8DmB;;sDAvDlB,EAAA,EAuDkB;;;;;6BAgBK;;2DAvEvB,EAAA,EAuEuB;;;;;yBAQJ,OAAQ,MAAM,EAAE;;uDA/EnC,EAAA,EA+EmB,OAAQ,MAAM,EAAE;;;;;qBAvDpB;;mDAxBf,EAAA,EAwBe;;;;;;;;;;;;YApBf,IAAM,QAAQ;YAUd,IAAM,aAAa;YAEnB,IAAA,KAAA,OAAA,MAAA,EAAA,OAAA,yBAAA,GAAA,CAAA,EAAA;gBAAA,MAAA,IAAA,CAAA,QAAA;YAAA;YAEA,IAAM,QAAQ,SAAA,eAAA,MAAA,KAAA,EAAA;YAMd,IAAS,aAAM,yBAA0B,EAAA;gBACxC,IAAI,QAAQ,IAAI;gBAChB,IAAI,YAAY;gBAChB,WAAW,KAAK,CAAC,OAAO,CAAC,IAAC,mBAAuB;oBAChD,IAAI,QAAQ,KAAK,KAAK;oBACtB,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAA,EAAA,CAAI,aAAa,CAAC,CAAC,MAAM;oBACjD,IAAI,SAAS,IAAI,EAAE;wBAClB,IAAI,QAAQ,WAAW,KAAK,CAAC,IAAI,CAAC,IAAC,mBAAqB,OAAO;mCAAI,GAAG,KAAK,IAAI;;wBAC/E,IAAI,SAAS,IAAI,EAAE;4BAClB,KAAK,QAAQ,CAAC,aAAW,CAAC,UAAU,OAAO,IAAC,0BAA8B;gCACzE,IAAI,CAAC,IAAI,KAAK,EAAE;oCACf,QAAQ,KAAK;oCACb,UAAU,IAAI,CAAC;;4BAEjB;;;2BAEK;wBACN,QAAQ,KAAK;;gBAEf;;gBACA,IAAI,OAAO;oBACV,IAAI,SAAS,OAAO,IAAI,IAAI,EAAE;wBAC7B,SAAS,OAAO;;uBAEX;oBACN,IAAI,SAAS,IAAI,IAAI,IAAI,EAAE;wBAC1B,SAAS,IAAI,GAAE;;;YAGlB;gBA7BS;YA+BT,IAAS,gBAAS,yBAA0B,EAAA;gBAC3C,sBACC,UAAS,KAAK;oBACb,IAAI,SAAS,OAAO,IAAI,IAAI,EAAE;wBAC7B,KAAK;wBACL,SAAS,OAAO;;gBAElB;kBACA,OAAM,IAAC,4CAAwC;oBAC9C,IAAI,SAAS,IAAI,IAAI,IAAI,EAAE;wBAC1B,SAAS,IAAI,GAAE;;gBAEjB;;YAEF;gBAdS;YAgBT,IAAS,qBAAc,uBAAwB,EAAA;gBAC9C,IAAI,WAAW,KAAK,CAAC,IAAI,CAAC,IAAC,qBAAuB,OAAO;2BAAI,KAAK,KAAK,IAAI,UAAU,KAAK;;qBAAK,IAAI,EAAE;oBACpG;;gBAED,QAAQ,GAAG,CAAC,WAAQ;gBACpB,WAAW,KAAK,CAAC,IAAI,CAAC;gBACtB,QAAQ,GAAG,CAAC,6BAA6B,WAAW,KAAK,CAAC,GAAG,CAAC,IAAA,IAAC,MAAA;2BAAE,EAAE,KAAK;;mBAAA;YACzE;gBAPS;YAQT,IAAS,iBAAU,OAAQ,MAAM,EAAE,6BAA8B,EAAA;gBAChE,IAAM,YAAY,WAAW,KAAK,CAAC,IAAI,CAAC,IAAA,OAAI,OAAA;2BAAI,KAAK,KAAK,IAAI;;;gBAC9D,QAAQ,GAAG,CAAC,qBAAqB,YAAS;gBAC1C,QAAQ,GAAG,CAAC,UAAU,OAAI;gBAC1B,IAAM,QAAQ,CAAC,MAAM,KAAK,CAAA,EAAA,CAAI,SAAS,CAAC,CAAC,MAAM;gBAC/C,IAAI,aAAa,IAAI,EAAE;oBACtB,UAAU,QAAQ,CAAC,aAAW,CAAC,UAAU,OAAO,IAAC,0BAA8B;wBAC9E,IAAI,CAAC,IAAI,KAAK,EAAE;4BACf,IAAI,SAAS,IAAI,IAAI,IAAI,EAAE;gCAC1B,SAAS,IAAI,GAAE;;4BAEhB;;wBAED,IAAI,SAAS,OAAO,IAAI,IAAI,EAAE;4BAC7B,SAAS,OAAO;;oBAElB;uBACM;oBAEN,IAAI,SAAS,IAAI,IAAI,IAAI,EAAE;wBAC1B,SAAS,IAAI,wBACZ,QAAO,KAAK,EACZ,UAAS,kBAAM,QAAK,uBACpB,QAAO;;;YAIX;gBA3BS;YA4BT,MAAM,OAAO,IAAC,OAAQ,cAAiB;gBACtC,KAAK,UAAU;gBAGf,IAAI,MAAM,aAAa,EAAE;oBACxB,sBACC,UAAA,MAAO;wBACN,QAAQ,GAAG,CAAC,KAAE;oBACf;sBACA,OAAA,IAAK,GAAG,EAAA;wBACP,QAAQ,GAAG,CAAC,OAAO,KAAE;oBACtB;;;YAGH;2BACC,OAAM,IAAI;YAEX,gCACC,6BACA,8BACA,sBACA;;uBAtIU,mBAAS,QAAA,SAAE,WAAK;;qBAC1B"}