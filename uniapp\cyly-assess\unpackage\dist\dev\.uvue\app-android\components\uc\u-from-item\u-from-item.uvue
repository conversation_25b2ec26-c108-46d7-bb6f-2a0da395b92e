import { FormItemData, FormItemRule, FormItemVerifyResult } from "../types";
import { useVerify, findParent } from "../utils";
const __sfc__ = defineComponent({
    __name: 'u-from-item',
    props: {
        customStyle: {
            type: Object as PropType<UTSJSONObject>,
            default: {} as UTSJSONObject as UTSJSONObject
        },
        field: {
            type: String,
            default: ''
        },
        rule: {
            type: Object as PropType<FormItemRule>,
            default: (): FormItemRule => {
                return {} as FormItemRule;
            }
        },
        showError: {
            type: <PERSON>olean,
            default: false
        },
        marginTop: {
            type: Number,
            default: 0
        }
    },
    setup(__props, { expose: __expose }: SetupContext): any | null {
        const __ins = getCurrentInstance()!;
        const _ctx = __ins.proxy as InstanceType<typeof __sfc__>;
        const _cache = __ins.renderCache;
        const isValid = ref(true);
        const parentLabelName = "u-form";
        const hintMessage = ref('\u3000');
        const instance = getCurrentInstance()!;
        const props = __props;
        const getStyle = computed((): Map<string, string> => {
            console.log(props.marginTop, "props.marginTop", " at components/uc/u-from-item/u-from-item.uvue:48");
            const mp: Map<string, string> = new Map();
            console.log(props.marginTop, "props.marginTop", " at components/uc/u-from-item/u-from-item.uvue:50");
            mp.set('margin-top', `${props.marginTop}rpx`);
            return mp;
        });
        function verify(value: any, callback: (res: FormItemVerifyResult) => void) {
            return useVerify(isValid, hintMessage, props.field as string, props.rule as FormItemRule, value, callback);
        }
        function pushFormItemFieldToForm() {
            const that = instance.proxy!;
            //找到父组件form
            const parent = findParent(that, [parentLabelName]);
            console.log("==that==:", that.$parent?.$options.name, " at components/uc/u-from-item/u-from-item.uvue:61");
            if (parent == null) {
                console.error('error:', 'u-form-item must be used inside u-form', " at components/uc/u-from-item/u-from-item.uvue:63");
                return;
            }
            const item = {
                field: props.field as string,
                instance: that
            } as FormItemData;
            parent.$callMethod('pushFielditem', item);
        }
        onMounted(() => {
            pushFormItemFieldToForm();
        });
        __expose({
            verify
        });
        return (): any | null => {
            return createElementVNode("view", utsMapOf({
                class: "form-item-wrapper",
                style: normalizeStyle(unref(getStyle))
            }), [
                createElementVNode("view", utsMapOf({
                    class: normalizeClass(["input-container", utsMapOf({ 'has-error': _ctx.showError && !unref(isValid) })])
                }), [
                    renderSlot(_ctx.$slots, "default")
                ], 2 /* CLASS */),
                isTrue(_ctx.showError && !unref(isValid))
                    ? createElementVNode("view", utsMapOf({
                        key: 0,
                        class: "error-message-bottom"
                    }), [
                        createElementVNode("text", utsMapOf({ class: "error-text" }), toDisplayString(unref(hintMessage)), 1 /* TEXT */)
                    ])
                    : createCommentVNode("v-if", true)
            ], 4 /* STYLE */);
        };
    }
});
export default __sfc__;
const GenComponentsUcUFromItemUFromItemStyles = [utsMapOf([["form-item-wrapper", padStyleMapOf(utsMapOf([["marginBottom", "24rpx"], ["width", "100%"]]))], ["input-container", padStyleMapOf(utsMapOf([["width", "100%"], ["borderTopWidth", 1], ["borderRightWidth", 1], ["borderBottomWidth", 1], ["borderLeftWidth", 1], ["borderTopStyle", "solid"], ["borderRightStyle", "solid"], ["borderBottomStyle", "solid"], ["borderLeftStyle", "solid"], ["borderTopColor", "#EEEEEE"], ["borderRightColor", "#EEEEEE"], ["borderBottomColor", "#EEEEEE"], ["borderLeftColor", "#EEEEEE"], ["borderTopLeftRadius", "16rpx"], ["borderTopRightRadius", "16rpx"], ["borderBottomRightRadius", "16rpx"], ["borderBottomLeftRadius", "16rpx"], ["transitionProperty", "borderColor"], ["transitionDuration", "0.3s"], ["transitionTimingFunction", "ease"]]))], ["error-message-bottom", padStyleMapOf(utsMapOf([["display", "flex"], ["alignItems", "flex-start"], ["marginTop", "8rpx"], ["paddingTop", "12rpx"], ["paddingRight", "16rpx"], ["paddingBottom", "12rpx"], ["paddingLeft", "16rpx"], ["backgroundColor", "#fff2f0"], ["borderTopLeftRadius", "8rpx"], ["borderTopRightRadius", "8rpx"], ["borderBottomRightRadius", "8rpx"], ["borderBottomLeftRadius", "8rpx"], ["borderLeftWidth", "4rpx"], ["borderLeftStyle", "solid"], ["borderLeftColor", "#ff4d4f"], ["boxShadow", "0 2rpx 8rpx rgba(255, 77, 79, 0.1)"], ["animation", "errorFadeIn 0.3s ease-out"]]))], ["has-error", padStyleMapOf(utsMapOf([["borderTopWidth", 1], ["borderRightWidth", 1], ["borderBottomWidth", 1], ["borderLeftWidth", 1], ["borderTopStyle", "solid"], ["borderRightStyle", "solid"], ["borderBottomStyle", "solid"], ["borderLeftStyle", "solid"], ["borderTopColor", "#ff4d4f"], ["borderRightColor", "#ff4d4f"], ["borderBottomColor", "#ff4d4f"], ["borderLeftColor", "#ff4d4f"]]))], ["error-text", padStyleMapOf(utsMapOf([["color", "#ff4d4f"], ["fontSize", "24rpx"], ["lineHeight", 1.5], ["flex", 1], ["wordWrap", "break-word"], ["wordBreak", "break-all"]]))], ["@FONT-FACE", utsMapOf([["0", utsMapOf([])], ["1", utsMapOf([["form-item-wrapper", utsMapOf([["", utsMapOf([["marginBottom", "16rpx"]])]])], ["error-message-bottom", utsMapOf([["", utsMapOf([["paddingTop", "10rpx"], ["paddingRight", "14rpx"], ["paddingBottom", "10rpx"], ["paddingLeft", "14rpx"], ["marginTop", "6rpx"]])]])], ["error-text", utsMapOf([["", utsMapOf([["fontSize", "22rpx"]])]])], ["error-icon", utsMapOf([["", utsMapOf([["width", "28rpx"], ["height", "28rpx"], ["fontSize", "18rpx"], ["marginRight", "10rpx"]])]])]])]])], ["@TRANSITION", utsMapOf([["input-container", utsMapOf([["property", "borderColor"], ["duration", "0.3s"], ["timingFunction", "ease"]])]])]])];
//# sourceMappingURL=u-from-item.uvue.map