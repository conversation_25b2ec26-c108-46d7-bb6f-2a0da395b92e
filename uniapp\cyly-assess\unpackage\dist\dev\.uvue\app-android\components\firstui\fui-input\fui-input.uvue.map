{"version": 3, "sources": ["components/firstui/fui-input/fui-input.uvue"], "names": [], "mappings": "AA2CA,OAAO,EAAE,uBAAsB,EAAE,MAAO,KAAI,CAAA;AAC5C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAyDE;AACF,MAAK,OAAQ,GAAE,eAAA,CAAA;IACd,IAAI,EAAE,WAAW;IACjB,KAAK,EAAE,CAAC,OAAO,EAAE,mBAAmB,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,sBAAsB,CAAC;IACpG,KAAK,EAAE;QACN,QAAQ,EAAE;YACT,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,KAAI;SACb;QACD,aAAa,EAAE;YACd,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,EAAC;SACV;QACD,KAAK,EAAE;YACN,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,EAAC;SACV;QACD,SAAS,EAAE;YACV,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,CAAA;SACT;QACD,UAAU,EAAE;YACX,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,MAAK;SACd;QACD,UAAU,EAAE;YACX,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,GAAE;SACX;QACD,SAAS,EAAE;YACV,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,KAAI;SACb;QACD,UAAU,EAAE;YACX,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,SAAQ;SACjB;QACD,KAAK,EAAE;YACN,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,KAAI;SACb;QACD,WAAW,EAAE;YACZ,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,EAAC;SACV;QACD,gBAAgB,EAAE;YACjB,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,EAAC;SACV;QACD,IAAI,EAAE;YACL,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,EAAC;SACV;QACD,KAAK,EAAE;YACN,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;YAC9B,OAAO,EAAE,EAAC;SACV;QACD,MAAK;QACL,UAAU,EAAE;YACX,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;YAC9B,OAAO,EAAE,EAAC;SACV;QACD,IAAI,EAAE;YACL,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,MAAK;SACd;QACD,QAAQ,EAAE;YACT,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,KAAI;SACb;QACD,QAAQ,EAAE;YACT,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,KAAI;SACb;QACD,aAAa,EAAE;YACd,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,KAAI;SACb;QACD,QAAQ,EAAE;YACT,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,KAAI;SACb;QACD,SAAS,EAAE;YACV,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,GAAE;SACX;QACD,GAAG,EAAE;YACJ,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,CAAC,CAAA;SACV;QACD,GAAG,EAAE;YACJ,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,CAAC,CAAA;SACV;QACD,aAAa,EAAE;YACd,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,CAAC;SACV;QACD,WAAW,EAAE;YACZ,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,EAAC;SACV;QACD,WAAW,EAAE;YACZ,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,MAAK;SACd;QACD,WAAW,EAAE;YACZ,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,KAAK;SACd;QACD,MAAM,EAAE;YACP,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,CAAC,CAAA;SACV;QACD,cAAc,EAAE;YACf,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,CAAC,CAAA;SACV;QACD,YAAY,EAAE;YACb,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,CAAC,CAAA;SACV;QACD,cAAc,EAAE;YACf,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,IAAG;SACZ;QACD,IAAI,EAAE;YACL,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,CAAA;SACT;QACD,KAAK,EAAE;YACN,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,MAAK;SACd;QACD,WAAW,EAAE;YACZ,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,KAAI;SACb;QACD,QAAQ,EAAE;YACT,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,KAAI;SACb;QACD,MAAM,EAAE;YACP,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,CAAA;SACT;QACD,SAAS,EAAE;YACV,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,KAAI;SACb;QACD,OAAO,EAAE;YACR,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,CAAA;SACT;QACD,QAAQ,EAAE;YACT,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,CAAA;SACT;QACD,YAAY,EAAE;YACb,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,IAAG;SACZ;QACD,UAAU,EAAE;YACX,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,EAAC;SACV;QACD,WAAW,EAAE;YACZ,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,CAAA;SACT;QACD,WAAW,EAAE;YACZ,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,EAAC;SACV;QACD,IAAI,EAAE;YACL,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,IAAG;SACZ;QACD,SAAS,EAAE;YACV,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,MAAK;SACd;QACD,OAAO,EAAE;YACR,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,aAAY;SACrB;QACD,eAAe,EAAE;YAChB,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,SAAQ;SACjB;QACD,SAAS,EAAE;YACV,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,CAAA;SACV;KACA;IACD,IAAI;QACH,MAAM,UAAS,GAAI,aAAa,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAC,GAAI,IAAI,CAAC,CAAC,QAAQ,IAAE,EAAE,EAAE,CAAC,EAAE,CAAA;QAC1F,OAAO;YACN,KAAK,EAAE,UAAU;YACjB,GAAG,EAAE,EAAE;YACP,OAAO,EAAE,IAAG,IAAK,IAAG,GAAI,uBAAuB;YAC/C,mDAAkD;YAClD,SAAS,EAAE,CAAA;SACZ,CAAA;IACD,CAAC;IACD,QAAQ,EAAE;QACT,QAAQ,IAAI,MAAK;YAChB,MAAM,EAAE,EAAE,GAAG,CAAC,MAAM,EAAE,MAAM,CAAA,GAAI,IAAI,GAAG,EAAE,CAAA;YACzC,EAAE,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,CAAA;YAC/B,EAAE,CAAC,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC,eAAe,CAAC,CAAA;YAC1C,EAAE,CAAC,GAAG,CAAC,YAAY,EAAE,GAAG,IAAI,CAAC,SAAS,KAAK,CAAC,CAAA;YAC5C,EAAE,CAAC,GAAG,CAAC,eAAe,EAAE,IAAI,CAAC,QAAO,CAAE,CAAA,CAAE,OAAM,CAAE,CAAA,CAAE,GAAG,IAAI,CAAC,MAAM,KAAK,CAAC,CAAA;YAEtE,IAAI,IAAI,CAAC,WAAU,IAAK,IAAI,CAAC,WAAU,IAAK,EAAE;gBAAE,EAAE,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC,WAAW,CAAC,CAAA;YAExF,OAAO,EAAE,CAAA;QACV,CAAC;QACD,eAAe,IAAI,MAAK;YACvB,IAAI,MAAK,GAAI,GAAG,IAAI,CAAC,MAAK,GAAI,CAAC,KAAI,CAAA;YACnC,IAAI,IAAI,CAAC,QAAQ,EAAE;gBAClB,MAAK,GAAI,OAAM,CAAA;aAChB;YACA,OAAO,MAAM,CAAA;QACd,CAAC;QACD,cAAc,IAAI,MAAK;YACtB,MAAM,EAAE,EAAE,GAAG,CAAC,MAAM,EAAE,MAAM,CAAA,GAAI,IAAI,GAAG,EAAE,CAAA;YACzC,IAAI,IAAI,CAAC,WAAU,IAAK,EAAE;gBAAE,EAAE,CAAC,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC,WAAW,CAAC,CAAA;YAMlE,kCAAiC;YAEjC,EAAE,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,CAAA;YAEnB,EAAE,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,WAAW,KAAK,CAAC,CAAA;YACzC,OAAO,EAAE,CAAA;QACV,CAAC;QACD,gBAAgB,IAAI,MAAK;YACxB,MAAM,EAAE,EAAE,GAAG,CAAC,MAAM,EAAE,MAAM,CAAA,GAAI,IAAI,GAAG,EAAE,CAAA;YACzC,IAAI,IAAI,CAAC,aAAY,IAAK,EAAE;gBAAE,EAAE,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,CAAA;YACjE,OAAO,EAAE,CAAA;QACV,CAAC;QACD,YAAY,IAAI,MAAK;YACpB,MAAM,EAAE,EAAE,GAAG,CAAC,MAAM,EAAE,MAAM,CAAA,GAAI,IAAI,GAAG,EAAE,CAAA;YACzC,IAAI,IAAI,CAAC,UAAS,IAAK,EAAE;gBAAE,EAAE,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,UAAU,CAAC,CAAA;YAC3D,IAAI,IAAI,CAAC,SAAQ,IAAK,CAAC,EAAE;gBACxB,EAAE,CAAC,GAAG,CAAC,UAAU,EAAE,GAAG,IAAI,CAAC,SAAS,KAAK,CAAC,CAAA;gBAC1C,EAAE,CAAC,GAAG,CAAC,YAAY,EAAE,GAAG,IAAI,CAAC,SAAS,KAAK,CAAC,CAAA;aAC7C;YACA,OAAO,EAAE,CAAA;QACV,CAAA;KACA;IACD,KAAK,EAAE;QACN,KAAK,CAAC,GAAG,EAAE,OAAO;YACjB,IAAI,CAAC,SAAS,CAAC,GAAG,EAAC;gBAClB,UAAU,CAAC,GAAG,EAAC;oBACd,IAAI,CAAC,cAAc,CAAC,GAAG,CAAA,CAAA;gBACxB,CAAC,EAAE,EAAE,CAAA,CAAA;YACN,CAAC,CAAA,CAAA;QACF,CAAC;QACD,UAAU;YACT,IAAI,CAAC,GAAE,GAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAA,CAAA;QAC7C,CAAC;QACD,KAAK;YACJ,IAAI,CAAC,GAAE,GAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAA,CAAA;QACxC,CAAA;KACA;IACD,OAAO;QACN,UAAU,CAAC,GAAG,EAAC;YACd,MAAM,KAAI,GAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAA,CAAA;YAC1C,MAAM,UAAS,GAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAA,CAAA;YACpD,IAAI,KAAI,IAAK,EAAE,EAAE;gBAChB,IAAI,CAAC,GAAE,GAAI,KAAI,CAAA;aAChB;iBAAO,IAAI,UAAS,IAAK,EAAE,EAAE;gBAC5B,IAAI,CAAC,GAAE,GAAI,UAAS,CAAA;aACrB;YACA,kBAAiB;YACjB,MAAM,MAAK,GAAI,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAA;YACzC,IAAI,MAAM,EAAE;gBACX,MAAM,IAAG,GAAI,IAAI,CAAC,OAAM,IAAK,uBAAsB,CAAA;gBACnD,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAA,IAAK,uBAAuB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAG,IAAK,uBAAuB,CAAC,CAAA;aAChG;QACD,CAAC,EAAE,EAAE,CAAA,CAAA;IACN,CAAC;IACD,OAAO;QACN,IAAI,CAAC,SAAS,CAAC,GAAG,EAAC;YAClB,UAAU,CAAC,GAAG,EAAC;gBACd,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAA,CAAA;YAC/B,CAAC,EAAE,GAAG,CAAA,CAAA;QACP,CAAC,CAAA,CAAA;IACF,CAAC;IACD,OAAO,EAAE;QACR,YAAY,CAAC,GAAG,EAAE,GAAG,GAAG,MAAK;YAC5B,IAAI,GAAG,EAAE,MAAM,CAAA;YACf,IAAI,OAAO,GAAE,IAAK,QAAQ,EAAE;gBAC3B,GAAE,GAAI,GAAE,IAAK,MAAM,CAAA;aACpB;iBAAO,IAAI,OAAO,GAAE,IAAK,QAAQ,EAAE;gBAClC,GAAE,GAAI,CAAC,GAAE,IAAK,MAAM,CAAC,CAAC,QAAQ,IAAC,CAAA;aAChC;iBAAO;gBACN,GAAE,GAAI,GAAG,CAAC,QAAQ,EAAC,CAAA;aACpB;YACA,OAAO,GAAG,CAAA;QACX,CAAC;QACD,cAAc,CAAC,KAAK,EAAE,OAAO;YAC5B,IAAI,KAAK,EAAE;gBACV,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAA,IAAK,UAAU,CAAC,CAAC,KAAK,EAAC,CAAA;aAC9C;iBAAO;gBACN,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAA,IAAK,UAAU,CAAC,CAAC,IAAI,EAAC,CAAA;aAC7C;QACD,CAAC;QACD,aAAa,CAAC,GAAG,EAAE,MAAM,GAAG,OAAM;YACjC,OAAO,GAAE,IAAK,CAAC,gBAAe,IAAK,GAAE,IAAK,gBAAgB,CAAA;QAC3D,CAAC;QACD,OAAO,CAAC,KAAK,EAAE,aAAa;YAC3B,IAAI,KAAI,GAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAA;YAC9B,IAAI,IAAI,CAAC,IAAI;gBAAE,KAAI,GAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;YAC1C,IAAI,CAAC,GAAE,GAAI,KAAK,CAAA;YAChB,IAAI,UAAS,GAAI,UAAU,CAAC,KAAK,CAAA,CAAA;YACjC,uCAAsC;YACtC,IAAI,CAAC,IAAI,CAAC,IAAG,IAAK,OAAM,IAAK,IAAI,CAAC,IAAG,IAAK,QAAQ,CAAA,IAAK,CACtD,KAAK,CAAC,UAAU,CAAA,IAAK,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,EAAE;gBACrD,MAAM,GAAE,GAAI,IAAI,CAAC,GAAE,CAAA;gBACnB,MAAM,GAAE,GAAI,IAAI,CAAC,GAAE,CAAA;gBACnB,IAAI,GAAE,IAAK,CAAC,CAAA,IAAK,UAAS,GAAI,GAAG,EAAE;oBAClC,UAAS,GAAI,GAAE,CAAA;iBAChB;qBAAO,IAAI,GAAE,IAAK,CAAC,CAAA,IAAK,GAAE,GAAI,UAAU,EAAE;oBACzC,UAAS,GAAI,GAAE,CAAA;iBAChB;gBACA,KAAI,GAAI,UAAU,CAAC,QAAQ,IAAC,CAAA;aAC7B;YACA,IAAI,CAAC,SAAS,CAAC,GAAG,EAAC;gBAClB,oBAAmB;gBACnB,IAAI,KAAK,CAAC,MAAM,CAAC,KAAI,IAAK,EAAE;oBAAE,IAAI,CAAC,GAAE,GAAI,KAAK,CAAA;YAC/C,CAAC,CAAA,CAAA;YACD,yBAAwB;YACxB,MAAM,UAAS,GAAI,KAAK,CAAC,MAAM,CAAC,KAAI,IAAK,EAAC,CAAE,CAAA,CAAE,KAAI,CAAE,CAAA,CAAE,EAAC,CAAA;YACvD,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,CAAC,CAAA;YAC/B,IAAI,CAAC,KAAK,CAAC,mBAAmB,EAAE,UAAU,CAAA,CAAA;QAC3C,CAAC;QACD,OAAO,CAAC,KAAK,EAAE,kBAAkB;YAChC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,CAAA;QAC3B,CAAC;QACD,MAAM,CAAC,KAAK,EAAE,iBAAiB;YAC9B,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;QAC1B,CAAC;QACD,SAAS,CAAC,KAAK,EAAE,oBAAoB;YACpC,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,KAAK,CAAC,CAAA;QAC7B,CAAC;QACD,OAAO;YACN,IAAI,IAAI,CAAC,QAAO,IAAK,CAAC,IAAI,CAAC,QAAQ;gBAAE,OAAM;YAC3C,IAAI,CAAC,cAAc,CAAC,KAAK,CAAA,CAAA;YACzB,IAAI,CAAC,GAAE,GAAI,EAAE,CAAA;YACb,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC,CAAA;YACvB,IAAI,CAAC,KAAK,CAAC,mBAAmB,EAAE,EAAE,CAAA,CAAA;QACnC,CAAC;QACD,UAAU;YACT,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;QACjC,CAAC;QACD,sBAAsB,CAAC,CAAC,EAAE,iCAAiC;YAC1D,IAAI,CAAC,KAAK,CAAC,sBAAsB,EAAE,CAAC,CAAA,CAAA;QACrC,CAAC;QACD,OAAO,CAAC,GAAG,EAAE,MAAM,GAAG,MAAK;YAC1B,OAAO,GAAG,CAAC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAAA;QACrC,CAAC;QACD,SAAS,CAAC,IAAI,EAAE,MAAM,GAAG,OAAM;YAC9B,IAAI,IAAI,CAAC,OAAM,IAAK,IAAI;gBAAE,OAAO,KAAK,CAAA;YACtC,IAAI,MAAK,GAAI,IAAI,CAAC,OAAM,IAAK,uBAAuB,CAAA;YACpD,IAAI,UAAS,GAAI,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;YACxC,OAAO,UAAS,IAAK,IAAI,EAAE;gBAC1B,IAAI,MAAM,CAAC,OAAM,IAAK,IAAI;oBAAE,OAAO,KAAK,CAAA;gBACxC,MAAK,GAAI,MAAM,CAAC,OAAM,IAAK,uBAAuB,CAAA;gBAClD,IAAI,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAA,IAAK,EAAE;oBAAE,OAAO,KAAK,CAAA;gBAC/C,UAAS,GAAI,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;aACrC;YACA,IAAI,CAAC,OAAM,GAAI,MAAM,CAAA;YACrB,OAAO,IAAI,CAAA;QACZ,CAAC;QACD,mBAAkB;QAClB,KAAK;YACJ,IAAI,CAAC,cAAc,CAAC,KAAK,CAAA,CAAA;YACzB,IAAI,CAAC,GAAE,GAAI,EAAE,CAAA;YACb,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC,CAAA;YACvB,IAAI,CAAC,KAAK,CAAC,mBAAmB,EAAE,EAAE,CAAA,CAAA;QACnC,CAAC;QACD,cAAc,IAAI,MAAK;YACtB,OAAO,IAAI,CAAC,GAAE,CAAA;QACf,CAAA;KACD;CACD,CAAA,CAAA;;;;;WA1eC,kBAAA,CAsCO,MAAA,EAAA,QAAA,CAAA;QAtCD,KAAK,EAAA,cAAA,CAAA,CAAC,iBAAiB,EACpB,QAAA,CAAA,EAAA,wBAAA,EAAA,IAAA,CAAA,WAAA,EAAA,yBAAA,EAAA,IAAA,CAAA,WAAA,IAAA,IAAA,CAAA,WAAA,IAAA,EAAA,EAAA,0BAAA,EAAA,IAAA,CAAA,QAAA,IAAA,IAAA,CAAA,aAAA,EAAA,CAA6J,CAAA,CAAA;QACpK,KAAK,EAAA,cAAA,CAAE,IAAA,CAAA,QAAQ,CAAA;QAAG,OAAG,EAAE,IAAA,CAAA,UAAU;;eACtB,IAAA,CAAA,SAAS,IAAA,CAAK,IAAA,CAAA,WAAW,CAAA;cAArC,kBAAA,CAEO,MAAA,EAAA,QAAA,CAAA;;gBAFiC,KAAK,EAAA,cAAA,CAAE,QAAA,CAAA,EAAA,UAAA,EAAA,IAAA,CAAA,WAAA,EAAA,IAAA,EAAA,GAAA,IAAA,CAAA,OAAA,KAAA,EAAA,KAAA,EAAA,GAAA,IAAA,CAAA,QAAA,KAAA,EAAA,CAA2E,CAAA;gBACzH,KAAK,EAAA,cAAA,CAAA,CAAC,uBAAuB,EAAS,QAAA,CAAA,EAAA,uBAAA,EAAA,IAAA,CAAA,WAAA,IAAA,EAAA,EAAA,CAA8C,CAAA,CAAA;;;eAM7C,IAAA,CAAA,QAAQ,CAAA;cAAhD,kBAAA,CAGO,MAAA,EAAA,QAAA,CAAA;;gBAHD,KAAK,EAAC,qBAAqB;;gBAChC,kBAAA,CACuE,MAAA,EAAA,QAAA,CAAA;oBADhE,KAAK,EAAA,cAAA,CAAE,IAAA,CAAA,gBAAgB,CAAA;oBAAE,KAAK,EAAA,cAAA,CAAA,CAAC,0BAA0B,EACvD,QAAA,CAAA,EAAA,2BAAA,EAAA,IAAA,CAAA,aAAA,IAAA,EAAA,EAAA,CAAoD,CAAA,CAAA;oBAAE,GAAC,EAAA,CAAA,CAAA,kBAAA,CAAA;;;QAEc,IAAA,CAAA,KAAK,IAAA,EAAA;cAApF,kBAAA,CAEO,MAAA,EAAA,QAAA,CAAA;;gBAFD,KAAK,EAAC,kBAAkB;gBAAE,KAAK,EAAA,cAAA,CAAE,QAAA,CAAA,EAAA,QAAA,EAAA,GAAA,IAAA,CAAA,UAAA,KAAA,EAAA,CAAgC,CAAA;;gBACtE,kBAAA,CAAmG,MAAA,EAAA,QAAA,CAAA;oBAA5F,KAAK,EAAA,cAAA,CAAE,QAAA,CAAA,EAAA,uBAAA,EAAA,IAAA,CAAA,SAAA,IAAA,CAAA,EAAA,CAA2C,CAAA;oBAAG,KAAK,EAAA,cAAA,CAAE,IAAA,CAAA,YAAY,CAAA;oCAAK,IAAA,CAAA,KAAK,CAAA,EAAA,CAAA,CAAA,wBAAA,CAAA;;;QAE1F,UAAA,CAAyB,IAAA,CAAA,MAAA,EAAA,MAAA,CAAA;QACzB,kBAAA,CAOuG,OAAA,EAAA,QAAA,CAAA;YAP/F,GAAG,EAAE,IAAA,CAAA,KAAK;YAAE,KAAK,EAAA,cAAA,CAAA,CAAC,iBAAiB,EAClC,QAAA,CAAA,EAAA,qBAAA,EAAA,IAAA,CAAA,QAAA,IAAA,IAAA,CAAA,QAAA,EAAA,iBAAA,EAAA,IAAA,CAAA,IAAA,IAAA,CAAA,EAAA,CAA6E,CAAA,CAAA;YACpF,KAAK,EAAA,cAAA,CAAE,QAAA,CAAA,EAAA,QAAA,EAAA,GAAA,IAAA,CAAA,IAAA,KAAA,EAAA,KAAA,EAAA,IAAA,CAAA,KAAA,EAAA,SAAA,EAAA,IAAA,CAAA,SAAA,EAAA,CAA8D,CAAA;YAAE,mBAAiB,EAAC,wBAAwB;YACjH,IAAI,EAAE,IAAA,CAAA,IAAI;YAAG,IAAI,EAAE,IAAA,CAAA,IAAI;YAAG,KAAK,EAAE,IAAA,CAAA,GAAG;YAAG,WAAW,EAAE,IAAA,CAAA,WAAW;YAAG,QAAQ,EAAE,IAAA,CAAA,QAAQ,IAAI,IAAA,CAAA,IAAI,IAAA,UAAA;YAC5F,mBAAiB,EAAE,IAAA,CAAA,gBAAgB;YAAG,QAAQ,EAAE,IAAA,CAAA,QAAQ,IAAI,IAAA,CAAA,QAAQ;YAAG,gBAAc,EAAE,IAAA,CAAA,aAAa;YACpG,SAAS,EAAE,IAAA,CAAA,SAAS;YAAG,cAAY,EAAE,IAAA,CAAA,WAAW;YAAG,cAAY,EAAE,IAAA,CAAA,WAAW;YAAG,MAAM,EAAE,IAAA,CAAA,MAAM;YAC7F,iBAAe,EAAE,IAAA,CAAA,cAAc;YAAG,eAAa,EAAE,IAAA,CAAA,YAAY;YAAG,iBAAe,EAAE,IAAA,CAAA,cAAc;YAAG,OAAK,EAAE,IAAA,CAAA,OAAO;YAChH,MAAI,EAAE,IAAA,CAAA,MAAM;YAAG,OAAK,EAAE,IAAA,CAAA,OAAO;YAAG,SAAO,EAAE,IAAA,CAAA,SAAS;YAAG,sBAAoB,EAAE,IAAA,CAAA,sBAAsB;;eACrB,IAAA,CAAA,SAAS,IAAI,IAAA,CAAA,GAAG,IAAA,EAAA,CAAA;cAA9F,kBAAA,CAQO,MAAA,EAAA,QAAA,CAAA;;gBARD,KAAK,EAAC,uBAAuB;gBAAE,KAAK,EAAA,cAAA,CAAE,QAAA,CAAA,EAAA,UAAA,EAAA,IAAA,CAAA,UAAA,EAAA,CAA0B,CAAA;gBACpE,OAAG,EAAA,aAAA,CAAO,IAAA,CAAA,OAAO,EAAA,CAAA,MAAA,CAAA,CAAA;;gBAClB,kBAAA,CAEO,MAAA,EAAA,QAAA,CAAA,EAFD,KAAK,EAAC,kBAAkB,EAAA,CAAA,EAAA;oBAC7B,kBAAA,CAAwC,MAAA,EAAA,QAAA,CAAA,EAAlC,KAAK,EAAC,oBAAoB,EAAA,CAAA,CAAA;;gBAEjC,kBAAA,CAEO,MAAA,EAAA,QAAA,CAAA,EAFD,KAAK,EAAC,kBAAkB,EAAA,CAAA,EAAA;oBAC7B,kBAAA,CAAwC,MAAA,EAAA,QAAA,CAAA,EAAlC,KAAK,EAAC,oBAAoB,EAAA,CAAA,CAAA;;;;QAGlC,UAAA,CAAa,IAAA,CAAA,MAAA,EAAA,SAAA,CAAA;eACD,IAAA,CAAA,YAAY,IAAA,CAAK,IAAA,CAAA,WAAW,CAAA;cAAxC,kBAAA,CACgE,MAAA,EAAA,QAAA,CAAA;;gBADrB,KAAK,EAAA,cAAA,CAAE,IAAA,CAAA,cAAc,CAAA;gBAAE,KAAK,EAAA,cAAA,CAAA,CAAC,0BAA0B,EACzF,QAAA,CAAA,EAAA,uBAAA,EAAA,IAAA,CAAA,WAAA,IAAA,EAAA,EAAA,CAA8C,CAAA,CAAA", "file": "components/firstui/fui-input/fui-input.uvue", "sourcesContent": ["<template>\r\n\t<view class=\"fui-input__wrap\"\r\n\t\t:class=\"{ 'fui-input__border-uvue': inputBorder, 'fui-input__border-color': inputBorder && borderColor == '', 'fui-input__disabled-styl': disabled && disabledStyle }\"\r\n\t\t:style=\"getStyle\" @tap=\"fieldClick\">\r\n\t\t<view v-if=\"borderTop && !inputBorder\" :style=\"{ background: borderColor, left: `${topLeft}rpx`, right: `${topRight}rpx` }\"\r\n\t\t\tclass=\"fui-input__border-top\" :class=\"{ 'fui-input__background': borderColor == '' }\">\r\n\t\t</view>\r\n\r\n\r\n\r\n\r\n\t\t<view class=\"fui-input__required\" v-if=\"required\">\r\n\t\t\t<text :style=\"getRequiredColor\" class=\"fui-input__asterisk-text\"\r\n\t\t\t\t:class=\"{ 'fui-input__asterisk-color': requiredColor == '' }\">*</text>\r\n\t\t</view>\r\n\t\t<view class=\"fui-input__label\" :style=\"{ minWidth: `${labelWidth}rpx` }\" v-if=\"label != ''\">\r\n\t\t\t<text :class=\"{ 'fui-input__label-size': labelSize == 0 }\" :style=\"getLabelStyl\">{{ label }}</text>\r\n\t\t</view>\r\n\t\t<slot name=\"left\"></slot>\r\n\t\t<input :ref=\"refId\" class=\"fui-input__self\"\r\n\t\t\t:class=\"{ 'fui-input__disabled': disabled || readonly, 'fui-input__size': size == 0 }\"\r\n\t\t\t:style=\"{ fontSize: `${size}rpx`, color: color, textAlign: textAlign }\" placeholder-class=\"fui-input__placeholder\"\r\n\t\t\t:type=\"type\" :name=\"name\" :value=\"val\" :placeholder=\"placeholder\" :password=\"password || type == 'password'\"\r\n\t\t\t:placeholder-style=\"placeholderStyle\" :disabled=\"disabled || readonly\" :cursor-spacing=\"cursorSpacing\"\r\n\t\t\t:maxlength=\"maxlength\" :confirm-type=\"confirmType\" :confirm-hold=\"confirmHold\" :cursor=\"cursor\"\r\n\t\t\t:selection-start=\"selectionStart\" :selection-end=\"selectionEnd\" :adjust-position=\"adjustPosition\" @focus=\"onFocus\"\r\n\t\t\t@blur=\"onBlur\" @input=\"onInput\" @confirm=\"onConfirm\" @keyboardheightchange=\"onKeyboardheightchange\" />\r\n\t\t<view class=\"fui-input__clear-wrap\" :style=\"{ background: clearColor }\" v-if=\"clearable && val != ''\"\r\n\t\t\**********=\"onClear\">\r\n\t\t\t<view class=\"fui-input__clear\">\r\n\t\t\t\t<view class=\"fui-input__clear-a\"></view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"fui-input__clear\">\r\n\t\t\t\t<view class=\"fui-input__clear-b\"></view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<slot></slot>\r\n\t\t<view v-if=\"borderBottom && !inputBorder\" :style=\"getBtnLineStyl\" class=\"fui-input__border-bottom\"\r\n\t\t\t:class=\"{ 'fui-input__background': borderColor == '' }\"></view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport { ComponentPublicInstance } from 'vue'\r\n/**\r\n * Input 输入框\r\n * @description Input 输入框，该组件是对原生input组件的增强，内置了常用布局样式，同时包含 input 所有功能。\r\n * @tutorial https://unix.firstui.cn/\r\n * @property {Boolean} required {Boolean} 是否显示必填图标\r\n * @property {String} requiredColor {String} 必填图标颜色\t\r\n * @property {String} label {String} 左侧标题\r\n * @property {Number} labelSize {Number} 标题字体大小，单位rpx\r\n * @property {String} labelColor {String} 标题字体颜色\r\n * @property {Number} labelWidth {Number} 标题最小宽度，单位rpx\r\n * @property {Boolean} clearable {Boolean} 输入内容后是否显示清除按钮\r\n * @property {String} clearColor {String} 清除按钮颜色\r\n * @property {Boolean} focus {Boolean} 获取焦点\r\n * @property {String} placeholder {String} 输入框为空时占位符\r\n * @property {String} placeholderStyle {String}\t指定 placeholder 的样式\r\n * @property {String} name {String} 输入框名称\r\n * @property {String} value {String} 输入框值\r\n * @property {String} type {String} 输入框类型，参考官方 input 组件type属性\r\n * @property {Boolean} password {Boolean} 是否是密码类型\t\r\n * @property {Boolean} disabled {Boolean} 是否禁用，清除按钮一并失效\r\n * @property {Boolean} disabledStyle {Boolean} 是否启用禁用状态下的样式，与正常输入框样式略有区别，仅disabled为true时有效\r\n * @property {Boolean} readonly {Boolean} 是否只读，保留清除按钮使用，优先级高于disabled\r\n * @property {Number} maxlength {Number} 最大输入长度，设置为 -1 的时候不限制最大长度\r\n * @property {Number} min {Number} 最小值，当 type=number、type=digit 时有效\r\n * @property {Number} max {Number} 最小值，当ype=number、type=digit 时有效\r\n * @property {Number} cursorSpacing {Number} 指定光标与键盘的距离，单位 px \r\n * @property {String} cursorColor {String} 光标颜色\r\n * @property {String} confirmType {String} 设置键盘右下角按钮的文字，仅在 type=\"text\" 时生效\r\n * @property {Boolean} confirmHold {Boolean} 点击键盘右下角按钮时是否保持键盘不收起\r\n * @property {Number} cursor {Number} 指定focus时的光标位置\r\n * @property {Number} selectionStart {Number} 光标起始位置，自动聚集时有效，需与selection-end搭配使用\r\n * @property {Number} selectionEnd {Number}\t光标结束位置，自动聚集时有效，需与selection-start搭配使用\r\n * @property {Boolean} adjustPosition {Boolean}\t键盘弹起时，是否自动上推页面\r\n * @property {Number} size {Number}\t输入框字体大小，单位 rpx\r\n * @property {String} color {String} 输入框字体颜色\r\n * @property {Boolean} inputBorder {Boolean} 是否显示input边框，为true则属性borderTop，borderBottom失效\r\n * @property {Boolean} isFillet {Boolean} input是否显示为圆角，设置为true则属性radius失效\r\n * @property {Number} radius {Number} 自定义input圆角值，单位rpx\r\n * @property {Boolean} borderTop {Boolean} 是否显示上边框\t\r\n * @property {Number} topLeft {Number} 上边框left值，单位rpx\r\n * @property {Number} topRight {Number} 上边框right值，单位rpx\r\n * @property {Boolean} borderBottom {Boolean} 是否显示下边框\r\n * @property {Number} bottomLeft {Number} 下边框right值，单位rpx\r\n * @property {Number} bottomRight {Number} 下边框right值，单位rpx\r\n * @property {String} borderColor {String} 边框颜色\r\n * @property {Boolean} trim\t {Boolean} 是否自动去除两端的空格\r\n * @property {String} textAlign {String} 输入框内容对齐方式，可选值：left、center、right\r\n * @property {String} padding {String} 输入框padding值\r\n * @property {String} backgroundColor {String} 输入框背景颜色\r\n * @property {Number} marginTop {Number} 输入框margin-top值,单位rpx\r\n * @event {Function} onclick 点击输入框时触发，(event:string) => void\r\n * @event {Function} input 当键盘输入时，触发input事件，(event: string) => void\r\n * @event {Function} update:modelValue 输入框值改变时触发，用于双向绑定 (event: string) => void\r\n * @event {Function} focus 输入框聚焦时触发，(event: UniInputFocusEvent) => void\r\n * @event {Function} blur 输入框失去焦点时触发，(event: UniInputBlurEvent) => void\r\n * @event {Function} confirm 点击完成按钮时触发，(event: UniInputConfirmEvent) => void\r\n * @event {Function} keyboardheightchange 键盘高度发生变化的时候触发此事件，(event: UniInputKeyboardHeightChangeEvent) => void\r\n */\r\nexport default {\r\n\tname: \"fui-input\",\r\n\temits: ['input', 'update:modelValue', 'focus', 'blur', 'confirm', 'onclick', 'keyboardheightchange'],\r\n\tprops: {\r\n\t\trequired: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\trequiredColor: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\tlabel: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\tlabelSize: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: 0\r\n\t\t},\r\n\t\tlabelColor: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: '#333'\r\n\t\t},\r\n\t\tlabelWidth: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: 140\r\n\t\t},\r\n\t\tclearable: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\tclearColor: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: '#CCCCCC'\r\n\t\t},\r\n\t\tfocus: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\tplaceholder: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\tplaceholderStyle: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\tname: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\tvalue: {\r\n\t\t\ttype: [Object, String, Number],\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\t//保留属性\r\n\t\tmodelValue: {\r\n\t\t\ttype: [Object, String, Number],\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\ttype: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: 'text'\r\n\t\t},\r\n\t\tpassword: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\tdisabled: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\tdisabledStyle: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\treadonly: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\tmaxlength: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: 140\r\n\t\t},\r\n\t\tmin: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: -1\r\n\t\t},\r\n\t\tmax: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: -1\r\n\t\t},\r\n\t\tcursorSpacing: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: 0,\r\n\t\t},\r\n\t\tcursorColor: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\tconfirmType: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: 'done'\r\n\t\t},\r\n\t\tconfirmHold: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false,\r\n\t\t},\r\n\t\tcursor: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: -1\r\n\t\t},\r\n\t\tselectionStart: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: -1\r\n\t\t},\r\n\t\tselectionEnd: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: -1\r\n\t\t},\r\n\t\tadjustPosition: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: true\r\n\t\t},\r\n\t\tsize: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: 0\r\n\t\t},\r\n\t\tcolor: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: '#333'\r\n\t\t},\r\n\t\tinputBorder: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\tisFillet: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\tradius: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: 0\r\n\t\t},\r\n\t\tborderTop: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\ttopLeft: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: 0\r\n\t\t},\r\n\t\ttopRight: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: 0\r\n\t\t},\r\n\t\tborderBottom: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: true\r\n\t\t},\r\n\t\tbottomLeft: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: 32\r\n\t\t},\r\n\t\tbottomRight: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: 0\r\n\t\t},\r\n\t\tborderColor: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\ttrim: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: true\r\n\t\t},\r\n\t\ttextAlign: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: 'left'\r\n\t\t},\r\n\t\tpadding: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: '28rpx 32rpx'\r\n\t\t},\r\n\t\tbackgroundColor: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: '#FFFFFF'\r\n\t\t},\r\n\t\tmarginTop: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: 0\r\n\t\t}\r\n\t},\r\n\tdata() {\r\n\t\tconst refInputId = `fui_input_${parseInt(Math.ceil(Math.random() * 10e5).toString(), 36)}`;\r\n\t\treturn {\r\n\t\t\trefId: refInputId,\r\n\t\t\tval: '',\r\n\t\t\tfuiForm: null as null | ComponentPublicInstance,\r\n\t\t\t//1-string 2-number 3-boolean 4-string[] 5-number[]\r\n\t\t\tvalueType: 1\r\n\t\t}\r\n\t},\r\n\tcomputed: {\r\n\t\tgetStyle(): object {\r\n\t\t\tconst mp: Map<string, string> = new Map();\r\n\t\t\tmp.set('padding', this.padding);\r\n\t\t\tmp.set('background', this.backgroundColor);\r\n\t\t\tmp.set('margin-top', `${this.marginTop}rpx`);\r\n\t\t\tmp.set('border-radius', this.isFillet ? '120px' : `${this.radius}rpx`);\r\n\r\n\t\t\tif (this.inputBorder && this.borderColor != '') mp.set('border-color', this.borderColor);\r\n\r\n\t\t\treturn mp;\r\n\t\t},\r\n\t\tgetBorderRadius(): string {\r\n\t\t\tlet radius = `${this.radius * 2}rpx`\r\n\t\t\tif (this.isFillet) {\r\n\t\t\t\tradius = '240px'\r\n\t\t\t}\r\n\t\t\treturn radius;\r\n\t\t},\r\n\t\tgetBtnLineStyl(): object {\r\n\t\t\tconst mp: Map<string, string> = new Map();\r\n\t\t\tif (this.borderColor != '') mp.set('background', this.borderColor);\r\n\r\n\r\n\r\n\r\n\r\n\t\t\t//app端left值与父元素padding-left一致【待修复】\r\n\r\n\t\t\tmp.set('left', `0`);\r\n\r\n\t\t\tmp.set('right', `${this.bottomRight}rpx`);\r\n\t\t\treturn mp;\r\n\t\t},\r\n\t\tgetRequiredColor(): object {\r\n\t\t\tconst mp: Map<string, string> = new Map();\r\n\t\t\tif (this.requiredColor != '') mp.set('color', this.requiredColor);\r\n\t\t\treturn mp;\r\n\t\t},\r\n\t\tgetLabelStyl(): object {\r\n\t\t\tconst mp: Map<string, string> = new Map();\r\n\t\t\tif (this.labelColor != '') mp.set('color', this.labelColor);\r\n\t\t\tif (this.labelSize != 0) {\r\n\t\t\t\tmp.set('fontSize', `${this.labelSize}rpx`);\r\n\t\t\t\tmp.set('lineHeight', `${this.labelSize}rpx`);\r\n\t\t\t}\r\n\t\t\treturn mp;\r\n\t\t}\r\n\t},\r\n\twatch: {\r\n\t\tfocus(val: boolean) {\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tthis.setFocusOrBlur(val)\r\n\t\t\t\t}, 50)\r\n\t\t\t})\r\n\t\t},\r\n\t\tmodelValue() {\r\n\t\t\tthis.val = this.getStringVal(this.modelValue)\r\n\t\t},\r\n\t\tvalue() {\r\n\t\t\tthis.val = this.getStringVal(this.value)\r\n\t\t}\r\n\t},\r\n\tcreated() {\r\n\t\tsetTimeout(() => {\r\n\t\t\tconst value = this.getStringVal(this.value)\r\n\t\t\tconst modelValue = this.getStringVal(this.modelValue)\r\n\t\t\tif (value != '') {\r\n\t\t\t\tthis.val = value\r\n\t\t\t} else if (modelValue != '') {\r\n\t\t\t\tthis.val = modelValue\r\n\t\t\t}\r\n\t\t\t//用于submit、reset事件\r\n\t\t\tconst isForm = this.getParent('fui-form');\r\n\t\t\tif (isForm) {\r\n\t\t\t\tconst form = this.fuiForm as ComponentPublicInstance\r\n\t\t\t\t(form.$data['formChildren'] as ComponentPublicInstance[]).push(this as ComponentPublicInstance);\r\n\t\t\t}\r\n\t\t}, 50)\r\n\t},\r\n\tmounted() {\r\n\t\tthis.$nextTick(() => {\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tthis.setFocusOrBlur(this.focus)\r\n\t\t\t}, 300)\r\n\t\t})\r\n\t},\r\n\tmethods: {\r\n\t\tgetStringVal(val: any): string {\r\n\t\t\tlet str: string;\r\n\t\t\tif (typeof val == 'string') {\r\n\t\t\t\tstr = val as string;\r\n\t\t\t} else if (typeof val == 'number') {\r\n\t\t\t\tstr = (val as number).toString()\r\n\t\t\t} else {\r\n\t\t\t\tstr = val.toString()\r\n\t\t\t}\r\n\t\t\treturn str;\r\n\t\t},\r\n\t\tsetFocusOrBlur(focus: boolean) {\r\n\t\t\tif (focus) {\r\n\t\t\t\t(this.$refs[this.refId] as UniElement).focus()\r\n\t\t\t} else {\r\n\t\t\t\t(this.$refs[this.refId] as UniElement).blur()\r\n\t\t\t}\r\n\t\t},\r\n\t\tisSafeInteger(num: number): boolean {\r\n\t\t\treturn num >= -9007199254740991 && num <= 9007199254740991;\r\n\t\t},\r\n\t\tonInput(event: UniInputEvent) {\r\n\t\t\tlet value = event.detail.value;\r\n\t\t\tif (this.trim) value = this.trimStr(value);\r\n\t\t\tthis.val = value;\r\n\t\t\tlet currentVal = parseFloat(value)\r\n\t\t\t// -9007199254740992 ～ 9007199254740992\r\n\t\t\tif ((this.type == 'digit' || this.type == 'number') && !\r\n\t\t\t\tisNaN(currentVal) && this.isSafeInteger(currentVal)) {\r\n\t\t\t\tconst min = this.min\r\n\t\t\t\tconst max = this.max\r\n\t\t\t\tif (min != -1 && currentVal < min) {\r\n\t\t\t\t\tcurrentVal = min\r\n\t\t\t\t} else if (max != -1 && max < currentVal) {\r\n\t\t\t\t\tcurrentVal = max\r\n\t\t\t\t}\r\n\t\t\t\tvalue = currentVal.toString()\r\n\t\t\t}\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t//当输入框为空时，输入框显示不赋值为0\r\n\t\t\t\tif (event.detail.value != '') this.val = value;\r\n\t\t\t})\r\n\t\t\t//如果为空时返回0 ，当双向绑定会将输入框赋值0\r\n\t\t\tconst inputValue = event.detail.value != '' ? value : ''\r\n\t\t\tthis.$emit('input', inputValue);\r\n\t\t\tthis.$emit('update:modelValue', inputValue)\r\n\t\t},\r\n\t\tonFocus(event: UniInputFocusEvent) {\r\n\t\t\tthis.$emit('focus', event);\r\n\t\t},\r\n\t\tonBlur(event: UniInputBlurEvent) {\r\n\t\t\tthis.$emit('blur', event);\r\n\t\t},\r\n\t\tonConfirm(event: UniInputConfirmEvent) {\r\n\t\t\tthis.$emit('confirm', event);\r\n\t\t},\r\n\t\tonClear() {\r\n\t\t\tif (this.disabled && !this.readonly) return;\r\n\t\t\tthis.setFocusOrBlur(false)\r\n\t\t\tthis.val = '';\r\n\t\t\tthis.$emit('input', '');\r\n\t\t\tthis.$emit('update:modelValue', '')\r\n\t\t},\r\n\t\tfieldClick() {\r\n\t\t\tthis.$emit('onclick', this.name);\r\n\t\t},\r\n\t\tonKeyboardheightchange(e: UniInputKeyboardHeightChangeEvent) {\r\n\t\t\tthis.$emit('keyboardheightchange', e)\r\n\t\t},\r\n\t\ttrimStr(str: string): string {\r\n\t\t\treturn str.replace(/^\\s+|\\s+$/g, '');\r\n\t\t},\r\n\t\tgetParent(name: string): boolean {\r\n\t\t\tif (this.$parent == null) return false;\r\n\t\t\tlet parent = this.$parent as ComponentPublicInstance;\r\n\t\t\tlet parentName = parent.$options['name'];\r\n\t\t\twhile (parentName != name) {\r\n\t\t\t\tif (parent.$parent == null) return false;\r\n\t\t\t\tparent = parent.$parent as ComponentPublicInstance;\r\n\t\t\t\tif (parent.$options['name'] == '') return false;\r\n\t\t\t\tparentName = parent.$options['name'];\r\n\t\t\t}\r\n\t\t\tthis.fuiForm = parent;\r\n\t\t\treturn true;\r\n\t\t},\r\n\t\t//暂时做重置处理（还原需记录初始值）\r\n\t\treset() {\r\n\t\t\tthis.setFocusOrBlur(false)\r\n\t\t\tthis.val = '';\r\n\t\t\tthis.$emit('input', '');\r\n\t\t\tthis.$emit('update:modelValue', '')\r\n\t\t},\r\n\t\tgetSubmitValue(): string {\r\n\t\t\treturn this.val\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n$fui-color-danger: #FF2B2B !default;\r\n$fui-color-border: #EEEEEE !default;\r\n$fui-placeholder-color: #CCCCCC !default;\r\n$fui-input-border-width: 1px !default;\r\n$fui-input-label-size: 32rpx !default;\r\n$fui-input-size: 32rpx !default;\r\n\r\n.fui-input__wrap {\r\n\twidth: 100%;\r\n\tdisplay: flex;\r\n\tflex-direction: row;\r\n\talign-items: center;\r\n\tposition: relative;\r\n\tbox-sizing: border-box;\r\n\r\n\r\n\r\n\toverflow: visible;\r\n}\r\n\r\n\r\n.fui-input__border-uvue {\r\n\tborder-width: 0.5px;\r\n\tborder-style: solid;\r\n}\r\n\r\n\r\n\r\n.fui-input__border-color {\r\n\tborder-color: $fui-color-border !important;\r\n}\r\n\r\n.fui-input__background {\r\n\tbackground: $fui-color-border !important;\r\n}\r\n\r\n.fui-input__border-top {\r\n\tposition: absolute;\r\n\ttop: 0;\r\n\theight: $fui-input-border-width;\r\n\ttransform: scaleY(0.5);\r\n\ttransform-origin: 0 0;\r\n\tz-index: 1;\r\n\tpointer-events: none;\r\n}\r\n\r\n.fui-input__border-bottom {\r\n\tposition: absolute;\r\n\tbottom: 0;\r\n\theight: $fui-input-border-width;\r\n\ttransform: scaleY(0.5);\r\n\ttransform-origin: 0 100%;\r\n\tz-index: 1;\r\n\tpointer-events: none;\r\n}\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n.fui-input__required {\r\n\r\n\r\n\r\n\tposition: absolute;\r\n\tleft: 12rpx;\r\n\theight: 100%;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n}\r\n\r\n.fui-input__asterisk-text {\r\n\tfont-size: 32rpx;\r\n\theight: 32rpx;\r\n\tline-height: 32rpx;\r\n}\r\n\r\n.fui-input__asterisk-color {\r\n\tcolor: $fui-color-danger !important;\r\n}\r\n\r\n\r\n.fui-input__label {\r\n\tpadding-right: 12rpx;\r\n\tflex-shrink: 0;\r\n}\r\n\r\n.fui-input__label-size {\r\n\tfont-size: $fui-input-label-size !important;\r\n\tline-height: $fui-input-label-size !important;\r\n}\r\n\r\n\r\n.fui-input__self {\r\n\tflex: 1;\r\n\tpadding-right: 12rpx;\r\n\toverflow: visible;\r\n\tbackground-color: transparent;\r\n\tbox-sizing: border-box;\r\n}\r\n\r\n.fui-input__size {\r\n\tfont-size: $fui-input-size !important;\r\n}\r\n\r\n.fui-input__clear-wrap {\r\n\twidth: 32rpx;\r\n\theight: 32rpx;\r\n\ttransform: rotate(45deg) scale(1.1);\r\n\tposition: relative;\r\n\tflex-shrink: 0;\r\n\tborder-radius: 32rpx;\r\n}\r\n\r\n.fui-input__clear {\r\n\twidth: 32rpx;\r\n\theight: 32rpx;\r\n\tdisplay: flex;\r\n\tflex-direction: row;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tposition: absolute;\r\n\tleft: 0;\r\n\ttop: 0;\r\n\ttransform: scale(0.5);\r\n}\r\n\r\n.fui-input__clear-a {\r\n\twidth: 32rpx;\r\n\tborder: 2rpx solid #fff;\r\n\tbackground-color: #fff;\r\n\tbox-sizing: border-box;\r\n}\r\n\r\n.fui-input__clear-b {\r\n\theight: 32rpx;\r\n\tborder: 2rpx solid #fff;\r\n\tbackground-color: #fff;\r\n\tbox-sizing: border-box;\r\n}\r\n\r\n.fui-input__placeholder {\r\n\tcolor: $fui-placeholder-color;\r\n\toverflow: visible;\r\n}\r\n\r\n.fui-input__disabled {\r\n\tpointer-events: none;\r\n}\r\n\r\n.fui-input__disabled-styl {\r\n\topacity: .6;\r\n}\r\n</style>"]}